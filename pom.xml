<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>wshifu-microservice-parent</artifactId>
        <version>3.5.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wanshifu</groupId>
    <artifactId>master-order-push-backend-service</artifactId>
    <packaging>war</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <!--<modules>-->
    <!--<module>common</module>-->
    <!--</modules>-->

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <wanshfiu.framework.version>3.5.1</wanshfiu.framework.version>
        <hutool.version>4.5.0</hutool.version>
        <wanshifu.order.config.service.version>2.0.41</wanshifu.order.config.service.version>
        <wanshifu.address.service.version>1.0.33</wanshifu.address.service.version>
        <wanshifu.apollo.version>2.1.0</wanshifu.apollo.version>
    </properties>

    <dependencies>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-lang</artifactId>
            <version>${wanshfiu.framework.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.wshifu.bigdata.tablestore</groupId>-->
<!--            <artifactId>tablestore-sql</artifactId>-->
<!--            <version>1.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-web-api-core-spring-boot-starter</artifactId>
            <version>${wanshfiu.framework.version}</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-push-service-api</artifactId>
            <version>1.0.45</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>order-config-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-consume-dispatcher</artifactId>
            <version>${wanshfiu.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.3.2</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-push-message-service-api</artifactId>
            <version>1.0.17</version>
            <exclusions>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-content-manage-service-api</artifactId>
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>image-service-api</artifactId>
            <version>1.2.3</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-test</artifactId>
            <version>${wanshfiu.framework.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <!--H2-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>3.2.0</version>
        </dependency>
        <!--订单配置服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-config-service-api</artifactId>
            <version>2.0.131</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-address-service-api</artifactId>
            <version>${wanshifu.address.service.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--阿波罗配置中心-->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${wanshifu.apollo.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-security</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.security.oauth</groupId>-->
<!--            <artifactId>spring-security-oauth2</artifactId>-->
<!--            <version>2.3.8.RELEASE</version>-->
<!--        </dependency>-->

        <!--<dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-full</artifactId>
            <version>5.2.3</version>
        </dependency>-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.9.1</version>
            <optional>true</optional>
        </dependency>
        <!--<dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.3</version>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.wanshifu</groupId>-->
<!--            <artifactId>master-order-distribute-service-api</artifactId>-->
<!--            <version>1.0.18-project-cooperative-business-20250308-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-notice-service-api</artifactId>
            <version>1.0.12</version>
            <exclusions>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-persistence-spring-boot-starter</artifactId>
            <version>${wanshfiu.framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.wanshifu</groupId>-->
<!--            <artifactId>wshifu-framework-zipkin-spring-boot-starter</artifactId>-->
<!--            <version>1.0.43</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-sort-service-api</artifactId>
            <version>1.0.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>4.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <!--多环境部署配置-->
    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <deploy.env>test</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy.env>prod</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy.env>dev</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy.env>local</deploy.env>
            </properties>
        </profile>
    </profiles>

    <build>
        <filters>
            <filter>src/main/resources/config/application-${deploy.env}.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.5</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <warName>master-order-push-backend-service</warName>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>