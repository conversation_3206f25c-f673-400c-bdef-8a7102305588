package com.wanshifu;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {SecurityAutoConfiguration.class})
@MapperScan("com.wanshifu.master.order.push.mapper")
@EnableTransactionManagement
@EnableFeignClients
@EnableApolloConfig
public class MasterOrderPushBackendServiceApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(
            SpringApplicationBuilder application) {
        return application.sources(MasterOrderPushBackendServiceApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(MasterOrderPushBackendServiceApplication.class, args);
    }
}
