package com.wanshifu;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {SecurityAutoConfiguration.class})
@MapperScan("com.wanshifu.master.order.push.mapper")
@EnableTransactionManagement
@EnableFeignClients
@EnableApolloConfig
@EnableScheduling
public class MasterOrderPushBackendServiceApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(
            SpringApplicationBuilder application) {
        return application.sources(MasterOrderPushBackendServiceApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(MasterOrderPushBackendServiceApplication.class, args);
    }
}
