package com.wanshifu.master.order.push.domain.response.orderMatchRoute;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class OrderMatchRouteListResp {

    private Integer routeId;

    private String routeName;

    private String orderPushFlag;

    private String orderPriorityMatchRule;

    private Integer standbyMatchRuleNum;

    private String lastUpdateAccountName;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
