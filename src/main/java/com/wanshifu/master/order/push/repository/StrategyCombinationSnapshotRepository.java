//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.master.order.push.domain.po.StrategyCombinationSnapshot;
//import org.springframework.stereotype.Repository;
//
//@Repository
//public class StrategyCombinationSnapshotRepository extends BaseRepository<StrategyCombinationSnapshot> {
//
//    public Long insert(String combinationName, String combinationDesc, String categoryIds, String cityIds, String priorityStrategyJson, String alternateStrategyJson, Integer businessLineId, Long loginUserId) {
//
//        StrategyCombinationSnapshot strategyCombinationSnapshot = new StrategyCombinationSnapshot();
//        strategyCombinationSnapshot.setCombinationName(combinationName);
//        strategyCombinationSnapshot.setCombinationDesc(combinationDesc);
//        strategyCombinationSnapshot.setCategoryIds(categoryIds);
//        strategyCombinationSnapshot.setCityIds(cityIds);
//        strategyCombinationSnapshot.setPriorityStrategyCombination(priorityStrategyJson);
//        strategyCombinationSnapshot.setAlternateStrategyCombination(alternateStrategyJson);
//        strategyCombinationSnapshot.setBusinessLineId(businessLineId);
//        strategyCombinationSnapshot.setUpdateAccountId(loginUserId);
//        strategyCombinationSnapshot.setCreateAccountId(loginUserId);
//        this.insertSelective(strategyCombinationSnapshot);
//        return strategyCombinationSnapshot.getSnapshotId();
//    }
//}