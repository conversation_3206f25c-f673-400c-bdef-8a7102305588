package com.wanshifu.master.order.push.controller.agentDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.AgentDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetTobGroupAgentListResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import com.wanshifu.master.order.push.service.agentDistributeStrategy.AgentDistributeStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-21 19:54
 */
@RestController
@RequestMapping("/agentDistributeStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))

public class AgentDistributeStrategyController {

    @Resource
    private AgentDistributeStrategyService agentDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.enable(rqt);
    }


    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public AgentDistributeStrategyDetailResp detail(@Valid @RequestBody AgentDistributeStrategyDetailRqt rqt) {
        return agentDistributeStrategyService.detail(rqt);
    }


    /**
     * 查询代理商列�?
     * @param rqt
     * @return
     */
    @GetMapping(value = "agentList")
    public SimplePageInfo<GetAgentListResp> getAgentList(@Valid GetAgentListRqt rqt){
        return agentDistributeStrategyService.getAgentList(rqt);
    }

    /**
     * B端团队师傅代理商
     * @param rqt
     * @return
     */
    @GetMapping(value = "tobGroupAgentList")
    public SimplePageInfo<GetTobGroupAgentListResp> tobGroupAgentList(@Valid TobGroupAgentInfoRqt rqt){
        return agentDistributeStrategyService.tobGroupAgentList(rqt);
    }

    /**
     * 查询单个代理�?
     * @param rqt
     * @return
     */
    @GetMapping(value = "getAgentById")
    public GetAgentListResp getAgentById(@Valid GetAgentByIdRqt rqt){
        return agentDistributeStrategyService.getAgentById(rqt);
    }

    /**
     * 单个B端团队师傅代理商
     * @param rqt
     * @return
     */
    @GetMapping(value = "getTobGroupAgentById")
    public GetTobGroupAgentListResp getTobGroupAgentById(@Valid GetTobGroupAgentByIdRqt rqt){
        return agentDistributeStrategyService.getTobGroupAgentById(rqt);
    }



    /**
     * 代理调度策略列表
     * @param rqt
     * @return
     */
    @PostMapping(value = "list")
    public SimplePageInfo<GetAgentDistributeStrategyListResp> list(@Valid @RequestBody GetAgentDistributeStrategyListRqt rqt){
        return agentDistributeStrategyService.list(rqt);
    }

}
