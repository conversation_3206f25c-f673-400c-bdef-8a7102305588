package com.wanshifu.master.order.push.domain.response.orderSortingStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 描述 :  订单排序策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 类目
     */
    private String categoryNames;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 策略状态
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer strategyStatus;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否实验， 1：是，0：否
     */
    private Integer testFlag;

    /**
     * 实验组策略名
     */
    private List<String> testGroupNameList;

    /**
     * 实验类别， 1：按师傅分组，2：按订单分组
     */
    private Integer testType;
}