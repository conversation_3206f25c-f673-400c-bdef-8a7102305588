package com.wanshifu.master.order.push.domain.vo.common;

import java.io.Serializable;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-18 14:14
 */
public class R<T> implements Serializable {
    private static final long serialVersionUID = 6738387175874422264L;

    private Integer code;

    private String msg;

    private T data;

    private R() {
    }

    public static <T> R<T> ok() {
        return createResult(null, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
    }

    public static <T> R<T> ok(T data) {
        return createResult(data, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
    }

    public static <T> R<T> ok(T data, String msg) {
        return createResult(data, ResultCode.SUCCESS.getCode(), msg);
    }

    public static <T> R<T> fail(ResultCode resultCode) {
        return createResult(null, resultCode.getCode(), resultCode.getMsg());
    }

    public static <T> R<T> fail(ResultCode resultCode, T data) {
        return createResult(data, resultCode.getCode(), resultCode.getMsg());
    }

    public static <T> R<T> fail(Integer code, String msg) {
        return createResult(null, code, msg);
    }

    private static <T> R<T> createResult(T data, Integer code, String msg) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setData(data);
        r.setmsg(msg);
        return r;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getmsg() {
        return msg;
    }

    public void setmsg(String msg) {
        this.msg = msg;
    }

    @Override public String toString() {
        return "R{" +
                "code=" + code +
                ", data=" + data +
                ", msg='" + msg + '\'' +
                '}';
    }
}