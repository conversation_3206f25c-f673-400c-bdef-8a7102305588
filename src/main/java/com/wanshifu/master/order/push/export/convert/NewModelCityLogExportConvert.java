package com.wanshifu.master.order.push.export.convert;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.dto.NewModelCityPushMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.export.exceldto.NewModelCityPushMatchLogExcelDto;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 14:38
 */
public class NewModelCityLogExportConvert implements PushMatchLogExportConvert<NewModelCityPushMatchLogDto, NewModelCityPushMatchLogExcelDto> {

    @Override
    public NewModelCityPushMatchLogExcelDto convert(NewModelCityPushMatchLogDto source) {
        NewModelCityPushMatchLogExcelDto target = new NewModelCityPushMatchLogExcelDto();

        target.setMatchType(PushLogMatchType.NEW_MODEL_CITY.getDesc());
        target.setOrderCityName(source.getOrderCityName());
        target.setOrderNo(source.getOrderNo());
        target.setOrderCreateTime(Objects.isNull(source.getOrderCreateTime()) ? "-" : DateUtils.formatDateTime(source.getOrderCreateTime()));
        target.setOrderSource(source.getOrderSource());
        target.setServeTypeName(source.getServeTypeName());
        target.setMasterId(source.getMasterId().toString());
        target.setMasterName(source.getMasterName());

        if (Objects.isNull(source.getIsMainMaster())) {
            target.setIsMainMaster("-");
        } else {
            if (source.getIsMainMaster() == 1) {
                target.setIsMainMaster("是");
            } else {
                target.setIsMainMaster("否");
            }
        }

        if (Objects.isNull(source.getIsMatchSuccess())) {
            target.setIsMatchSuccess("-");
        } else {

            if (source.getIsMatchSuccess() == 1) {
                target.setIsMatchSuccess("是");
            } else {
                target.setIsMatchSuccess("否");
            }
        }
        target.setMatchFailReason(Strings.isNullOrEmpty(source.getMatchFailReason()) ? "" : source.getMatchFailReason());

        if (Objects.isNull(source.getIsFilter())) {
            target.setIsFilter("否");
        } else {
            if (source.getIsFilter() == 1) {
                target.setIsFilter("是");
            } else {
                target.setIsFilter("否");
            }
        }

        target.setFilterReason(Strings.isNullOrEmpty(source.getFilterReason()) ? "" : source.getFilterReason());

        if (Objects.isNull(source.getIsDistribute())) {
            target.setIsDistribute("否");

        } else {
            if (source.getIsDistribute() == 1) {

                target.setIsDistribute("是");
            } else {
                target.setIsDistribute("否");
            }
        }

        target.setDistributeRule(Strings.isNullOrEmpty(source.getDistributeRule()) ? "-" : source.getDistributeRule());

        if (Objects.isNull(source.getIsAutoGrabSuccess())) {
            target.setIsAutoGrabSuccess("否");

        } else {
            if (source.getIsAutoGrabSuccess() == 1) {
                target.setIsAutoGrabSuccess("否");
            } else {
                target.setIsAutoGrabSuccess("否");
            }
        }

        target.setAutoGrabFailReason(Strings.isNullOrEmpty(source.getAutoGrabFailReason()) ? "" : source.getAutoGrabFailReason());

        target.setAbandonTime(Objects.isNull(source.getAbandonTime()) ? "-" : DateUtils.formatDateTime(source.getAbandonTime()));
        target.setOrderVersion(source.getOrderVersion());

        target.setCreateTime(Objects.isNull(source.getCreateTime()) ? "-" : DateUtils.formatDateTime(source.getCreateTime()));

        return target;
    }
}
