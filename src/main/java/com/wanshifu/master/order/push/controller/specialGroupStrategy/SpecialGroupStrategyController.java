package com.wanshifu.master.order.push.controller.specialGroupStrategy;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.specialGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.service.specialGroupStrategy.SpecialGroupStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "特殊人群策略")
@RestController
@RequestMapping("/specialGroupStrategy")
public class SpecialGroupStrategyController {

    @Resource
    private SpecialGroupStrategyService specialGroupStrategyService;

    /**
     * 创建特殊人群策略
     *
     * @param rqt 创建请求
     * @return 策略id
     */
    @ApiOperation("创建特殊人群策略")
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return specialGroupStrategyService.create(rqt);
    }

    /**
     * 更新特殊人群策略
     *
     * @param rqt 更新请求
     * @return 更新结果
     */
    @ApiOperation("更新特殊人群策略")
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return specialGroupStrategyService.update(rqt);
    }

    /**
     * 获取特殊人群策略详情
     *
     * @param rqt 详情请求
     * @return 策略详情
     */
    @ApiOperation("获取特殊人群策略详情")
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return specialGroupStrategyService.detail(rqt);
    }

    /**
     * 分页查询特殊人群策略列表
     *
     * @param rqt 列表查询请求
     * @return 分页结果
     */
    @ApiOperation("分页查询特殊人群策略列表")
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return specialGroupStrategyService.list(rqt);
    }

    /**
     * 启用/禁用特殊人群策略
     *
     * @param rqt 启用/禁用请求
     * @return 操作结果
     */
    @ApiOperation("启用/禁用特殊人群策略")
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt) {
        return specialGroupStrategyService.enable(rqt);
    }

    /**
     * 删除特殊人群策略
     *
     * @param rqt 删除请求
     * @return 删除结果
     */
    @ApiOperation("删除特殊人群策略")
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return specialGroupStrategyService.delete(rqt);
    }
}