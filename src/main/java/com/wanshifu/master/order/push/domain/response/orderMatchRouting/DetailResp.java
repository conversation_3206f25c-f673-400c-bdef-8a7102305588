package com.wanshifu.master.order.push.domain.response.orderMatchRouting;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  召回策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    private Integer routingId;

    private String routingName;

    private String routingDesc;

    private String orderFrom;

    @NotNull
    private String orderTag;

    @NotNull
    private String routingType;

    @NotNull
    private String matchRouting;

    @NotNull
    private String lv1MasterType;

    private String lv2MasterType;

    private String lv3MasterType;

}