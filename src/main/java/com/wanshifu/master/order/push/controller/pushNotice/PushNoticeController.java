package com.wanshifu.master.order.push.controller.pushNotice;


import com.wanshifu.master.notice.domains.request.pushNotice.*;
import com.wanshifu.master.notice.service.api.PushNoticeApi;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@RestController
@RequestMapping("pushNotice")
public class PushNoticeController {

    @Resource
    private PushNoticeService pushNoticeService;

    
    /**
     * 发送报价短�?
     * @param rqt
     * @return
     */
    @PostMapping(value = "sendOfferSms")
    public Integer sendOfferSms(@Valid @RequestBody SendOfferSmsRqt rqt){
        return pushNoticeService.sendOfferSms(rqt);
    }



}
