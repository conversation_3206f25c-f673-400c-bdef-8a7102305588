//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.master.order.push.domain.po.SortingStrategySnapshot;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Condition;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.List;
//
//@Repository
//public class SortingStrategySnapshotRepository extends BaseRepository<SortingStrategySnapshot> {
//
//
//    public Long insert(String strategyName, String strategyDesc, String categoryIds, String sortingRule, String ruleExpression, Integer businessLineId, Long loginUserId) {
//        SortingStrategySnapshot sortingStrategySnapshot = new SortingStrategySnapshot();
//        sortingStrategySnapshot.setStrategyName(strategyName);
//        sortingStrategySnapshot.setStrategyDesc(strategyDesc);
//        sortingStrategySnapshot.setCategoryIds(categoryIds);
//        sortingStrategySnapshot.setSortingRule(sortingRule);
//        sortingStrategySnapshot.setRuleExpression(ruleExpression);
//        sortingStrategySnapshot.setBusinessLineId(businessLineId);
//        sortingStrategySnapshot.setCreateAccountId(loginUserId);
//        sortingStrategySnapshot.setUpdateAccountId(loginUserId);
//          super.insertSelective(sortingStrategySnapshot);
//          return sortingStrategySnapshot.getSnapshotId();
//    }
//
//
//    public List<SortingStrategySnapshot> selectBySnapshotIdList(List<Long> snapshotIdList){
//        Condition condition = new Condition(SortingStrategySnapshot.class);
//        Example.Criteria criteria = condition.createCriteria();
//        criteria.andIn("snapshotId", snapshotIdList);
//        return this.selectByCondition(condition);
//    }
//}