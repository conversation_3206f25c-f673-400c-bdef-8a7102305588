package com.wanshifu.master.order.push.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 统一线程池配置管理
 * @date 2025/5/27 15:09
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    /**
     * 异步导出线程池
     * @return
     */
    @Bean(name = "asyncExportExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor asyncExportExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("asyncExportExecutor-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
