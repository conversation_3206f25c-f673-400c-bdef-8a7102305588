package com.wanshifu.master.order.push.decoder;

import com.google.gson.Gson;
import com.google.gson.JsonIOException;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 16:01
 */
public class MasterBigDataOpenApiDecoder implements Decoder {

    public MasterBigDataOpenApiDecoder() {

    }

    @Override
    public Object decode(Response response, Type type) throws IOException {
        Gson gson = new Gson();
        try {
            return gson.fromJson(response.body().asReader(), type);
        } catch (JsonIOException e) {
            if (e.getCause() != null &&
                    e.getCause() instanceof IOException) {
                throw IOException.class.cast(e.getCause());
            }
            throw e;
        }
    }

    /**
     * 大数据错误解析器
     */
    public static class ApiErrorDecoder implements ErrorDecoder {

        public ApiErrorDecoder() {

        }

        @Override
        public Exception decode(String s, Response response) {
            String content;
            try {
                InputStream inputStream = response.body().asInputStream();
                content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                return new ApiAccessException(response.request().url(), content);
            } catch (IOException var6) {
                return new ApiAccessException(response.request().url(), "");
            }
        }
    }
}
