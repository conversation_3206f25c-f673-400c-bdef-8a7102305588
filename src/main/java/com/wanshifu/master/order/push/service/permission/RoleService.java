package com.wanshifu.master.order.push.service.permission;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;


public interface RoleService {

    int add(AddRoleRqt rqt);

    int update(UpdateRoleRqt rqt);

    SimplePageInfo<GetRoleListResp> list(GetRoleListRqt rqt);

    int delete(DeleteRoleRqt rqt);

    GetRoleDetailResp detail(GetRoleDetailRqt rqt);

    int addAccount(AddAccountRqt rqt);


}
