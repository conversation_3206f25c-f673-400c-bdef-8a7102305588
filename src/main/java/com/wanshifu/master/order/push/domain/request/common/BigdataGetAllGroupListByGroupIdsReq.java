package com.wanshifu.master.order.push.domain.request.common;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 描述 :  根据人群id列表查询人群信息.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:29
 */
@Data
@AllArgsConstructor
public class BigdataGetAllGroupListByGroupIdsReq {
    public BigdataGetAllGroupListByGroupIdsReq(List<Long> groupIds) {
        this.groupIds = groupIds;
    }

    private List<Long> groupIds;
    /**
     * 用户和师傅区分标志 1-用户 2-师傅
     */
    private Integer personaId = 2;

}