package com.wanshifu.master.order.push.service.orderSelectStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.GetOrderSelectStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.OrderSelectStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;


public interface OrderSelectStrategyService {

    SimplePageInfo<GetOrderSelectStrategyListResp> list(GetOrderSelectStrategyListRqt rqt);

    Integer create(CreateOrderSelectStrategyRqt rqt);


    Integer update(UpdateOrderSelectStrategyRqt rqt);

    Integer enable(EnableOrderSelectStrategyRqt rqt);

    OrderSelectStrategyDetailResp detail(OrderSelectStrategyDetailRqt rqt);

    Integer delete(DeleteOrderSelectStrategyRqt rqt);

    SimplePageInfo<MasterQuotaResp> quotaList(MasterQuotaListRqt rqt);

    }
