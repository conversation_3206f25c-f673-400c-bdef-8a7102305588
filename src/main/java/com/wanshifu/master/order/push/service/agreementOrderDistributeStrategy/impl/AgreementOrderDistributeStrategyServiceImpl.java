package com.wanshifu.master.order.push.service.agreementOrderDistributeStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.*;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.*;

import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.OrderSelectStrategyDetailRqt;
import com.wanshifu.master.order.push.service.agreementOrderDistributeStrategy.AgreementOrderDistributeStrategyService;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.master.order.sort.domain.po.OrderScoreStrategy;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementOrderDistributeStrategyServiceImpl implements AgreementOrderDistributeStrategyService {

    @Resource
    private AgreementOrderDistributeStrategyApi agreementOrderDistributeStrategyApi;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private ServeCommonService serveCommonService;

    @Resource
    private OrderRoutingStrategyApi orderRoutingStrategyApi;

    @Resource
    private CompensateDistributeApi compensateDistributeApi;

//    @Resource
//    private AuthHandler authHandler;

    @Override
    @Transactional
    public int create(CreateRqt rqt) {
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
     return agreementOrderDistributeStrategyApi.create(rqt);
    }


    @Override
    @Transactional
    public Integer update(UpdateRqt rqt) {
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agreementOrderDistributeStrategyApi.update(rqt);
    }


    @Override
    @Transactional
    public Integer delete(DeleteRqt rqt) {
        return agreementOrderDistributeStrategyApi.delete(rqt);
    }

    @Override
    @Transactional
    public Integer enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agreementOrderDistributeStrategyApi.enable(rqt);
    }


    @Resource
    private OrderScoringStrategyApi orderScoringStrategyApi;

    @Resource
    private OrderSelectStrategyApi orderSelectStrategyApi;

    @Override
    public DetailResp detail(DetailRqt rqt) {

        DetailResp detailResp = new DetailResp();


        AgreementOrderDistributeStrategy distributeStrategy = agreementOrderDistributeStrategyApi.detail(rqt);


        BeanUtils.copyProperties(distributeStrategy, detailResp);

        List<CreateRqt.DistributeStrategyItem> strategyItemList = JSON.parseArray(distributeStrategy.getDistributeStrategyList(), CreateRqt.DistributeStrategyItem.class);
        List<Integer> selectStrategyIds = strategyItemList.stream().map(CreateRqt.DistributeStrategyItem::getSelectStrategyId).distinct().collect(Collectors.toList());
        List<Integer> scoreStrategyIds = strategyItemList.stream().map(CreateRqt.DistributeStrategyItem::getScoreStrategyId).distinct().collect(Collectors.toList());

        scoreStrategyIds.remove(null);

        List<OrderSelectStrategy> orderSelectStrategyList = selectStrategyIds.stream().map(selectStrategyId -> {
            OrderSelectStrategyDetailRqt selectStrategyDetailRqt = new OrderSelectStrategyDetailRqt();
            selectStrategyDetailRqt.setStrategyId(selectStrategyId);
            return orderSelectStrategyApi.detail(selectStrategyDetailRqt);
        }).collect(Collectors.toList());





        if (distributeStrategy != null) {

            List<DetailResp.DistributeStrategyItem> distributeStrategyItemList = JSON.parseArray(distributeStrategy.getDistributeStrategyList(), DetailResp.DistributeStrategyItem.class);

            Set<Long> serveIds = distributeStrategyItemList.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                    .filter(it -> CollectionUtils.isNotEmpty(it.getServeIdList()))
                    .flatMap(it -> it.getServeIdList().stream())
                    .collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

            Map<Long, String> serveInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(serveIds)) {
                serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                        .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
            }
            Map<Long, String> finalServeInfoMap = serveInfoMap;

            Map<Integer,OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream().collect(Collectors.toMap(OrderSelectStrategy::getStrategyId,OrderSelectStrategy->OrderSelectStrategy));


            Map<Integer, OrderScoringStrategy> orderScoringStrategyMap = null;

            if(CollectionUtils.isNotEmpty(scoreStrategyIds)){
                List<OrderScoringStrategy> orderScoringStrategyList = scoreStrategyIds.stream().map(scoreStrategyId -> {
                    OrderScoringStrategyDetailRqt orderScoringStrategyDetailRqt = new OrderScoringStrategyDetailRqt();
                    orderScoringStrategyDetailRqt.setStrategyId(scoreStrategyId);
                    return orderScoringStrategyApi.detail(orderScoringStrategyDetailRqt);
                }).collect(Collectors.toList());
                orderScoringStrategyMap = orderScoringStrategyList.stream().collect(Collectors.toMap(OrderScoringStrategy::getStrategyId, OrderScoringStrategy->OrderScoringStrategy));
            }

            Map<Integer, OrderScoringStrategy> finalOrderScoringStrategyMap = orderScoringStrategyMap;

            distributeStrategyItemList.forEach(distributeStrategyItem -> Optional.ofNullable(distributeStrategyItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

                List<LinkedList<Long>> serveIdListList = item.getServeIdList();
                if (CollectionUtils.isNotEmpty(serveIdListList)) {
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    item.setServeInfoList(serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }

                OrderSelectStrategy orderSelectStrategy= orderSelectStrategyMap.get(distributeStrategyItem.getSelectStrategyId());
                OrderScoringStrategy orderScoringStrategy= Objects.nonNull(finalOrderScoringStrategyMap) ? finalOrderScoringStrategyMap.get(distributeStrategyItem.getScoreStrategyId()) : null;

                distributeStrategyItem.setSelectStrategyName(orderSelectStrategy != null ? orderSelectStrategy.getStrategyName() : "");
                distributeStrategyItem.setScoreStrategyName(orderScoringStrategy != null ? orderScoringStrategy.getStrategyName() : "");

                //TODO 查询匹配路由


//                orderMatchRoutingApi.detail()


            })));

        detailResp.setDistributeStrategyList(distributeStrategyItemList);

//            List<DetailResp.MatchRoutingRule> matchRoutingRuleList = JSON.parseArray(distributeStrategy.getCompensateDistributeStrategyList(), DetailResp.MatchRoutingRule.class);
//
//            matchRoutingRuleList.forEach(matchRoutingRule -> {
//                com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt();
//                detailRqt.setRoutingId(matchRoutingRule.getMatchRoutingId());
//                OrderMatchRouting orderMatchRouting = orderMatchRoutingApi.detail(detailRqt);
//                if(orderMatchRouting != null){
//                    matchRoutingRule.setMatchRoutingName(orderMatchRouting.getRoutingName());
//                }
//
//            });
//            detailResp.setCompensateDistributeStrategyList(matchRoutingRuleList);

            if(StringUtils.isNotBlank(distributeStrategy.getCompensateDistributeList())){
                detailResp.setCompensateDistributeList(JSON.parseArray(distributeStrategy.getCompensateDistributeList(), DetailResp.CompensateDistributeVo.class));

                if(CollectionUtils.isNotEmpty(detailResp.getCompensateDistributeList())){
                    detailResp.getCompensateDistributeList().forEach(compensateDistributeVo -> {
                        if(Objects.nonNull(compensateDistributeVo.getCompensateDistributeId())){
                            com.wanshifu.master.order.push.domain.rqt.compensateDistribute.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.compensateDistribute.DetailRqt();
                            detailRqt.setDistributeId(compensateDistributeVo.getCompensateDistributeId());
                            CompensateDistribute compensateDistribute = compensateDistributeApi.detail(detailRqt);
                            compensateDistributeVo.setCompensateDistributeName(compensateDistribute.getStrategyName());
                        }

                        if(Objects.nonNull(compensateDistributeVo.getOrderRoutingStrategyId())){
                            com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt();
                            detailRqt.setStrategyId(compensateDistributeVo.getOrderRoutingStrategyId());
                            OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyApi.detail(detailRqt);
                            compensateDistributeVo.setOrderRoutingStrategyName(orderRoutingStrategy.getStrategyName());
                        }
                    });

                }
            }

        }
        return detailResp;

    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        SimplePageInfo<AgreementOrderDistributeStrategy> simplePageInfo = agreementOrderDistributeStrategyApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<AgreementOrderDistributeStrategy> agreementOrderDistributeStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = agreementOrderDistributeStrategyList.stream().map(AgreementOrderDistributeStrategy::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(",")))
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        //城市地址
        String cityIdsStr = agreementOrderDistributeStrategyList.stream().map(AgreementOrderDistributeStrategy::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        List<Address> divisionInfoListByDivisionIds = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        List<Long> updateAccountIds = agreementOrderDistributeStrategyList.stream().map(AgreementOrderDistributeStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(agreementOrderDistributeStrategyList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(","))
                    .map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it))
                    .collect(Collectors.joining(",")));

            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

}
