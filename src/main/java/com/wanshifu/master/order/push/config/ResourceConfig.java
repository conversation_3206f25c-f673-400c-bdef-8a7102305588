//package com.wanshifu.master.order.push.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
//import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
//import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
//
//import jakarta.annotation.Resource;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-17 14:24
// */
//
//@Configuration
//@EnableResourceServer
//public class ResourceConfig extends ResourceServerConfigurerAdapter {
//
//    @Resource
//    private AuthenticationExceptionEntryPoint authenticationExceptionEntryPoint;
//
//
//    @Override
//    public void configure(ResourceServerSecurityConfigurer resources) {
//        resources.resourceId("rid");
//
//        resources.authenticationEntryPoint(authenticationExceptionEntryPoint);
//    }
//
//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http.formLogin().loginPage("/login").permitAll()
//                .and()
//                .authorizeRequests()
//                .antMatchers("/health", "/orderPush/listOrderPushRecordDetail"
//                        , "/orderPush/listInfoOrderPushRecordDetail"
//                        , "/orderPush/getOrderPushPushNumber","/pushNotice/sendOfferSms",
//                        "/pushLimitRule/create",
//                        "/pushLimitRule/update",
//                        "/pushLimitRule/detail",
//                        "/pushLimitRule/list",
//                        "/pushLimitRule/enable")
//                //.antMatchers("/*/*","/health")
//                .permitAll().and()
//                .authorizeRequests()
//                .anyRequest().authenticated();
//        // 关跨域保�?
//        http.csrf().disable();
//    }
//}
