package com.wanshifu.master.order.push.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.dto.IfExpressDto;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.function.AllMatch;
import com.wanshifu.master.order.push.function.AnyMatch;
import com.wanshifu.master.order.push.operator.OperatorContain;
import com.wanshifu.master.order.push.operator.OperatorContainsAny;
import com.wanshifu.master.order.push.operator.OperatorNotContain;
import com.wanshifu.master.order.push.operator.OperatorNotContainsAny;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述 :  QlExpressUtil.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-08 16:07
 */
public class QlExpressUtil {


    /**
     * 目前支持的操作符
     */
    private final static List<String> TERMS = Lists.newArrayList(
            "in", "not_in","+", "<", "=", "!=", ">", "<=", ">=", "*", "contain", "notContain", "notContainsAny", "containsAny");

    /**
     * 且或关系
     *
     * @param condition
     * @param qlExpressDtoList
     * @return
     */
    public static String transitionQlExpress(String condition, List<QlExpressDto> qlExpressDtoList) {
        if (CollectionUtils.isEmpty(qlExpressDtoList)) {
            return "";
        }
        if (qlExpressDtoList.size() > 1) {
            Assert.notBlank(condition, "或且关系(condition) 不可为空!");
            Assert.isTrue(Lists.newArrayList("or", "and").contains(condition), StrUtil.format("或且关系【{condition}】 值有误!", condition));
        }
        String qlExpress = qlExpressDtoList.stream().map(it -> QlExpressUtil.transitionQlExpress(it.getItemName(), it.getTerm(), it.getItemValue(),it.getC()))
                .collect(Collectors.joining(condition));
        checkSyntaxRule(qlExpress);
        return qlExpress;
    }

    /**
     * 且或关系
     *
     * @param condition
     * @param qlExpressStrList
     * @return
     */
    public static String transitionQlExpressStr(String condition, List<String> qlExpressStrList) {
        if (CollectionUtils.isEmpty(qlExpressStrList)) {
            return "";
        }
        if (qlExpressStrList.size() > 1) {
            Assert.notBlank(condition, "或且关系(condition) 不可为空!");
            Assert.isTrue(Lists.newArrayList("or", "and").contains(condition), StrUtil.format("或且关系【{condition}】 值有误!", condition));
        }
        String qlExpress = String.join(condition, qlExpressStrList);
        checkSyntaxRule(qlExpress);
        return qlExpress;
    }

    /**
     * 生成ql表达式
     * 例如：
     * itemName         term      itemValue             return
     * age              >            3                   age > 3
     * age              *            3                   age * 3
     * age              <            3                   age < 3
     * age              >=            3                   age >= 3
     * age              <=            3                   age <= 3
     * age              =             3                  age == 3
     * age              !=             3                  age != 3
     * age              in          1,2,3               age in [1,2,3]
     * name             in          zhangsan,lisi        name in ['zhangsan','lisi']
     * age              not_in      1,2,3                !(age in [1,2,3])
     * master_group     contain     1                    master_group contain 1
     * master_group     notContain     1                  master_group notContain 1
     *
     * @param itemName  变量名
     * @param term      符号
     * @param itemValue 变量值
     * @return 表达式
     */
    public static String transitionQlExpress(String itemName, String term, String itemValue, Class<?> c) {
        Assert.isTrue(StringUtils.isNotBlank(itemName) && StringUtils.isNotBlank(term) &&
                StringUtils.isNotBlank(itemValue), "参数不可为空!");
        String qlExpress;
        Assert.isTrue(TERMS.contains(term), StrUtil.format("不支持该操作符【{}】!", term));

        itemValue = Arrays.stream(itemValue.split(",")).map(it -> {
            if (!NumberUtil.isNumber(it)) {
                return StrUtil.format("'{}'", it);
            }
            if (c == Long.class) {
                return StrUtil.format("{}L", it);
            }
            return it;
        }).collect(Collectors.joining(","));

        if (Lists.newArrayList("in", "not_in").contains(term)) {
            if (StringUtils.equals(term, "in")) {
                //in
                qlExpress = StrUtil.format(" {} in [{}] ", itemName, itemValue);
            } else {
                qlExpress = StrUtil.format(" !({} in [{}]) ", itemName, itemValue);
            }
            return qlExpress;
        }
        if (Lists.newArrayList("notContainsAny", "containsAny").contains(term)) {
            return StrUtil.format(" {} {} [{}] ", itemName, term, itemValue);
        }
        term = StringUtils.equals("=", term) ? "==" : term;
        qlExpress = StrUtil.format(" {} {} {} ", itemName, term, itemValue);
        checkSyntaxRule(qlExpress);
        return qlExpress;
    }


    /**
     * 生成ql表达式
     * if(a+b==12) then {return 7*0.6;}   else if(a+b==11)then{return 2;}    else {return 0;}
     * if(a+b==12) then {return 7*0.6;}                                       else {return 0;}
     *
     * @param ifExpressDtos
     * @param elseReturn
     * @return
     */
    public static String transitionQlExpress(List<IfExpressDto> ifExpressDtos, String elseReturn) {
        Assert.isTrue(CollectionUtils.isNotEmpty(ifExpressDtos), "参数有误!");
        StringBuilder qlExpress = new StringBuilder();
        for (int i = 0; i < ifExpressDtos.size(); i++) {
            IfExpressDto ifExpressDto = ifExpressDtos.get(i);
            qlExpress.append(StrUtil.format(" {} if({}) then {return {};} ", i == 0 ? "" : "else", ifExpressDto.getIfExpress(), ifExpressDto.getElseExpress()));
        }
        qlExpress.append(StrUtil.format(" else {return {};}", elseReturn));
        checkSyntaxRule(qlExpress.toString());
        return qlExpress.toString();
    }

    /**
     * 脚本语法是否正确，可以通过ExpressRunner编译指令集的接口来完成
     * 如果调用过程不出现异常，指令集instructionSet就是可以被加载运行（execute）了
     *
     * @param expressString 脚本
     */
    public static void checkSyntaxRule(String expressString) {
        //isPrecise 是否需要高精度计算
        //isShortCircuit 是否使用逻辑短路特性
        //isTrace 是否输出所有的跟踪信息
        ExpressRunner runner = new ExpressRunner(false, true);
        //runner.setShortCircuit(true);
        try {
            runner.addOperator("contain", new OperatorContain());
            runner.addOperator("notContain", new OperatorNotContain());
            runner.addOperator("containsAny", new OperatorContainsAny());
            runner.addOperator("notContainsAny", new OperatorNotContainsAny());

            runner.addFunction("anyMatch", new AnyMatch());
            runner.addFunction("allMatch", new AllMatch());

            runner.parseInstructionSet(expressString);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusException(StrUtil.format("生成表达式【{}】有误!请检查!", expressString));
        }
    }
}