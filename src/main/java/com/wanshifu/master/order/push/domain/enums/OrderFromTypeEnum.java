package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 订单来源类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum OrderFromTypeEnum {

    /**
     * 企业
     */
    SITE("site","企业"),

    /**
     * 内部总包
     */
    ENTERPRISE_INSIDE("enterprise_inside","内部总包"),

    /**
     * 一口价
     */
    ENTERPRISE_OUTSIDE("enterprise_outside","外部总包"),

    /**
     * 家庭
     */
    FAMILY("family","家庭"),

    /**
     * 家庭总包
     */
    FAMILY_ENTERPRISE("family_enterprise","家庭总包");


    public final String code;

    public final String name;

    OrderFromTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, OrderFromTypeEnum> valueMapping = new HashMap<>((int) (OrderFromTypeEnum.values().length / 0.75));

    static {
        for (OrderFromTypeEnum instance : OrderFromTypeEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static OrderFromTypeEnum asValue(String code) {
        return valueMapping.get(code);
    }

    public static Boolean isInclude(String code) {
        if(Objects.isNull(code)) {
            return false;
        }
        for (OrderFromTypeEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

}
