package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 订单来源类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum OrderFromEnum {

    /**
     * 企业
     */
    SITE("site","企业"),

    /**
     * 总包
     */
    ENTERPRISE("enterprise","总包"),


    /**
     * 家庭
     */
    FAMILY("family","家庭"),

    /**
     * 家庭总包
     */
    ALL("all","不限");


    public final String code;

    public final String name;

    OrderFromEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, OrderFromEnum> valueMapping = new HashMap<>((int) (OrderFromEnum.values().length / 0.75));

    static {
        for (OrderFromEnum instance : OrderFromEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static OrderFromEnum asValue(String code) {
        return valueMapping.get(code);
    }

    public static Boolean isInclude(String code) {
        if(Objects.isNull(code)) {
            return false;
        }
        for (OrderFromEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

}
