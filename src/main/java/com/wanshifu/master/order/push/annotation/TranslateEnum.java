package com.wanshifu.master.order.push.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 16:40
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TranslateEnum {
    /**
     * 需要翻译说明的枚举class
     * @return
     */
    Class<?> enumClass();

    /**
     * 对应翻译后返回字段的名称 如果不填写默认本字段来显示翻译
     * @return
     */
    String fieldName() default "";
}