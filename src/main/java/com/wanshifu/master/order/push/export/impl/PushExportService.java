package com.wanshifu.master.order.push.export.impl;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;


import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.master.order.push.api.PushExportTaskApi;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.UpdatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;

import com.wanshifu.master.order.push.export.strategy.PushMatchLogExportFactory;
import com.wanshifu.master.order.push.export.strategy.PushMatchLogExportStrategy;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;


import jakarta.annotation.Resource;
import java.io.File;

import java.io.IOException;
import java.nio.file.Files;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 15:06
 */
@Service
@Slf4j
public class PushExportService {

    @Resource
    private PushExportTaskApi pushExportTaskApi;




    /**
     * 异步导出推单派单日志
     *
     * @param pushMatchLogRqt
     */
    public void asyncExportPushMatchLogList(Long taskId, String exportType, PushMatchLogRqt pushMatchLogRqt) {

        PushMatchLogExportStrategy strategy = PushMatchLogExportFactory.getStrategy(exportType);

        try {
            //配置Excel样式
            //表头样式（居中+加粗）
            WriteCellStyle headStyle = new WriteCellStyle();
            headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //灰色背景
            headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headStyle.setWriteFont(headWriteFont);

            //内容样式(左对齐+自动换行)
            WriteCellStyle contentStyle = new WriteCellStyle();
            contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
            contentStyle.setWrapped(true);

            //设置表头样式和内容样式
            HorizontalCellStyleStrategy styleStrategy =
                    new HorizontalCellStyleStrategy(headStyle, contentStyle);

            //使用 EasyExcel 分批次写入（每次攒够 1 万条写入一次，直到数据全部导出,避免大数据量导出产生内存溢出）
            FileUploadResp fileUploadResp = strategy.exportAndUpload(pushMatchLogRqt, styleStrategy);


            if (Objects.nonNull(fileUploadResp)
                    && Objects.nonNull(fileUploadResp.getData())) {
                //更新任务状态为成功
                updateExportTask(taskId, "success", fileUploadResp.getData().getFileUrl(), null);
            } else {
                updateExportTask(taskId, "fail", null, "导出文件上传七牛云失败！");
            }


        } catch (Exception e) {
            //失败处理
             updateExportTask(taskId, "fail", null, e.getMessage());
            log.error("推单派代日志导出失败", e);
        }
    }

    /**
     * 更新导出任务状态
     * @param taskId
     * @param status
     * @param url
     * @param failReason
     */
    private void updateExportTask(Long taskId, String status, String url, String failReason) {
        UpdatePushExportTaskRqt updatePushExportTaskRqt = new UpdatePushExportTaskRqt();
        updatePushExportTaskRqt.setId(taskId);
        updatePushExportTaskRqt.setStatus(status);
        updatePushExportTaskRqt.setUrl(url);
        updatePushExportTaskRqt.setFailReason(failReason);

        pushExportTaskApi.updatePushExportTask(updatePushExportTaskRqt);
    }
}
