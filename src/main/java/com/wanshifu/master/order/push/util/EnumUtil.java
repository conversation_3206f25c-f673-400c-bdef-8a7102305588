package com.wanshifu.master.order.push.util;

import com.google.common.collect.Lists;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 17:25
 */
public class EnumUtil{

    private static final String DEFAULT_ENUM_NAME = "name";

    /**
     * 首字母转大写
     *
     * @param s 需要操作的字符�?
     * @return 转换后结�?
     */
    private static String toUpperCaseFirstOne(String s) {
        if (Character.isUpperCase(s.charAt(0))) {
            return s;
        } else {
            return Character.toUpperCase(s.charAt(0)) + s.substring(1);
        }
    }

    /**
     * 枚举转List格式
     *
     * @param targetEnumClazz 目标枚举Clazz
     * @return 装换结果
     */
    public static List<Map<String, Object>> toMapList(Class targetEnumClazz) {
        return toList(targetEnumClazz, DEFAULT_ENUM_NAME);
    }

    /**
     * 枚举转List格式
     *
     * @param targetEnumClazz 目标枚举Clazz
     * @param enumName        返回JSON中枚举名称对应的Key
     * @return 转换结果
     */
    public static List<Map<String, Object>> toList(Class targetEnumClazz, String enumName) {
        try {
            //获取方法
            Method[] methods = targetEnumClazz.getMethods();
            Field[] fields = targetEnumClazz.getDeclaredFields();
            List<Field> fieldList = Lists.newArrayList();
            for (Method method : methods) {
                for (Field field : fields) {
                    if (method.getName().endsWith(toUpperCaseFirstOne(field.getName()))) {
                        fieldList.add(field);
                    }
                }
            }

            List<Map<String, Object>> resultList = Lists.newArrayList();
            //获取�?
            Enum[] enums = (Enum[]) targetEnumClazz.getEnumConstants();
            for (Enum e : enums) {
                Map<String, Object> eMap = new HashMap();
                String enumNameValue = e.name();
                for (Field field : fieldList) {
                    field.setAccessible(true);
                    if (field.getName().equals(enumName)) {
                        enumNameValue = enumNameValue + ";" + field.get(e);
                    } else {
                        eMap.put(field.getName(), field.get(e));
                    }
                }
                if (enumNameValue.startsWith(";")) {
                    enumNameValue = enumNameValue.substring(1);
                }
                eMap.put(enumName, enumNameValue);
                resultList.add(eMap);
            }

            return resultList;
        } catch (RuntimeException | IllegalAccessException e) {
        }
        return null;
    }
}
