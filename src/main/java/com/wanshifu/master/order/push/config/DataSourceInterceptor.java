package com.wanshifu.master.order.push.config;

import com.wanshifu.framework.persistence.annotation.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 16:04
 */
@Slf4j
public class DataSourceInterceptor implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        //先清除DataSourceKey防止串号
        DynamicDataSource.clearDataSourceKey();
        Method method = invocation.getMethod();
        String dsKey = "";
        boolean hasMethodAno = method.isAnnotationPresent(DataSource.class);
        if (hasMethodAno) {
            //被调用的方法上加了@DataSource注解，则该注解生效（优先级高于类上的@DataSource注解）
            DataSource dataSource = method.getAnnotation(DataSource.class);
            dsKey = dataSource.value();
            DynamicDataSource.setDataSourceKey(dsKey);
        } else {
            //被调用的方法未添加@DataSource注解，但该类添加了@DataSource注解，则该注解生效
            boolean hasDsAnoInClass = method.getDeclaringClass().isAnnotationPresent(DataSource.class);
            if (hasDsAnoInClass) {
                DataSource ds = method.getDeclaringClass().getAnnotation(DataSource.class);
                dsKey = ds.value();
                DynamicDataSource.setDataSourceKey(dsKey);
            }
        }
        //log.info("DataSourceInterceptor thread ={}, method ={}, dataSourceKey ={}", Thread.currentThread().getName(),method.getName(), dsKey);
        try{
            Object resultObj = invocation.proceed();
            return resultObj;
        }finally {
            //执行完方法，不管是否抛出了异常，都清除DataSourceKey
            DynamicDataSource.clearDataSourceKey();
        }
    }
}
