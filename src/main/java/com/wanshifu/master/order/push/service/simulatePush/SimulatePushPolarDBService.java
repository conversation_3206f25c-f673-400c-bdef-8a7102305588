package com.wanshifu.master.order.push.service.simulatePush;

import com.alibaba.druid.pool.DruidDataSource;
import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.SimulatePushDetail;
import com.wanshifu.master.order.push.service.MysqlConnectionPool;
import com.wanshifu.master.order.push.service.PolarDBService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class SimulatePushPolarDBService {

    private DruidDataSource masterOrderPushServiceDataSource;

    @Value("${poraldb.username}")
    private String username;

    @Value("${poraldb.password}")
    private String password;

    @Value("${poraldb.masterOrderPushService.url}")
    private String masterOrderPushServiceUrl;

    public static final String DRIVER = "com.mysql.jdbc.Driver";




    private Connection getMasterOrderPushServiceConnection() throws Exception {
        if (masterOrderPushServiceDataSource != null) {
            return masterOrderPushServiceDataSource.getConnection();
        }
        synchronized (PolarDBService.class) {
            if (masterOrderPushServiceDataSource != null) {
                return masterOrderPushServiceDataSource.getConnection();
            }
            masterOrderPushServiceDataSource = new DruidDataSource();
            masterOrderPushServiceDataSource.setDriverClassName(DRIVER);
            masterOrderPushServiceDataSource.setUsername(username);
            masterOrderPushServiceDataSource.setPassword(password);
            masterOrderPushServiceDataSource.setUrl(masterOrderPushServiceUrl);
            masterOrderPushServiceDataSource.setInitialSize(5); // 初始化连�?
            masterOrderPushServiceDataSource.setMinIdle(1); // 最小空闲连�?
            masterOrderPushServiceDataSource.setMaxActive(500); // 最大连接数�?
            masterOrderPushServiceDataSource.setMaxWait(6000);// 连接等待超时的时�?
            masterOrderPushServiceDataSource.setPoolPreparedStatements(false);
            return masterOrderPushServiceDataSource.getConnection();
        }
    }


    List<SimulatePush> getSimulatePushList(Long simulateId,Integer pageNum,Integer pageSize){

        Integer startOffset = (pageNum -1 ) * pageSize;


        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<SimulatePush> simulatePusheList = new ArrayList<>();


        try {
            connection = getMasterOrderPushServiceConnection();
            preparedStatement = connection
                    .prepareStatement("SELECT simulate_push_id,strategy_list_name,order_id,push_master_num,push_rounds,is_alternate_strategy FROM `simulate_push` where simulate_id = ? limit ? , ?");
            preparedStatement.setLong(1, simulateId);
            preparedStatement.setInt(2, startOffset);
            preparedStatement.setLong(3, pageSize);
            resultSet = preparedStatement.executeQuery();
            int goodsNum=0;
            while (resultSet.next()) {
                SimulatePush simulatePush = new SimulatePush();
                simulatePush.setSimulatePushId(resultSet.getLong("simulate_push_id"));
                simulatePush.setOrderId(resultSet.getLong("order_id"));
                simulatePush.setStrategyListName(resultSet.getString("strategy_list_name"));
                simulatePush.setPushMasterNum(resultSet.getInt("push_master_num"));
                simulatePush.setPushRounds(resultSet.getInt("push_rounds"));
                simulatePush.setIsAlternateStrategy(resultSet.getInt("is_alternate_strategy"));
                simulatePusheList.add(simulatePush);
            }
        } catch (Exception e) {
//			logger.warn("master_order_id:{} BussinessId12-商品 查询失败:{}", masterOrderId, e.getMessage());
        } finally {
            MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
        }
        return simulatePusheList;
    }


    List<SimulatePushDetail> getSimulatePushDetailList(Long simulatePushId){
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<SimulatePushDetail> simulatePushDetailList = new ArrayList<>();

        try {
            connection = getMasterOrderPushServiceConnection();
            preparedStatement = connection
                    .prepareStatement("SELECT order_id,master_id,push_time,push_round FROM `simulate_push_detail` where simulate_push_id = ?");
            preparedStatement.setLong(1, simulatePushId);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                SimulatePushDetail simulatePushDetail = new SimulatePushDetail();
                simulatePushDetail.setOrderId(resultSet.getLong("order_id"));
                simulatePushDetail.setMasterId(resultSet.getLong("master_id"));
                simulatePushDetail.setPushTime(resultSet.getDate("push_time"));
                simulatePushDetail.setPushRound(resultSet.getInt("push_round"));
                simulatePushDetailList.add(simulatePushDetail);
            }
        } catch (Exception e) {

//			logger.warn("master_order_id:{} BussinessId12-商品 查询失败:{}", masterOrderId, e.getMessage());
        } finally {
            MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
        }
        return simulatePushDetailList;
    }


}
