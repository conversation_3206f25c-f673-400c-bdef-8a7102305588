package com.wanshifu.master.order.push.domain.response.orderSelectStrategy;

import lombok.Data;

import java.util.Date;

@Data
public class GetOrderSelectStrategyListResp {

    private Integer strategyId;

    private String strategyName;

    private String orderFrom;

    private String strategyDesc;

    private Integer isAppointGroup;

    private Integer strategyStatus;

    private String masterResources;

    private String lastUpdateAccountName;

    private Date createTime;

    private Date updateTime;

}
