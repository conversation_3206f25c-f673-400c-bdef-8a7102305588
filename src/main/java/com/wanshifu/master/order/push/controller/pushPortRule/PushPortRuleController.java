package com.wanshifu.master.order.push.controller.pushPortRule;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushPortRuleApi;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.response.pushPortRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushPortRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.*;
import com.wanshifu.master.order.push.service.pushPortRule.PushPortRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/pushPortRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushPortRuleController {

    @Resource
    private PushPortRuleService pushPortRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return pushPortRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return pushPortRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return pushPortRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return pushPortRuleService.list(rqt);
    }


    /**
     * 启用/禁用端口规则
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
        return pushPortRuleService.enable(rqt);
    }




}
