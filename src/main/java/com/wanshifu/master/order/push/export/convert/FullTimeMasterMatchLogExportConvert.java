package com.wanshifu.master.order.push.export.convert;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.dto.CooperationBusinessPushMatchLogDto;
import com.wanshifu.master.order.push.domain.dto.OrderFullTimeMasterMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.export.exceldto.CooperationBusinessPushMatchLogExcelDto;
import com.wanshifu.master.order.push.export.exceldto.FullTimeMasterMatchLogExcelDto;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 11:39
 */
public class FullTimeMasterMatchLogExportConvert implements PushMatchLogExportConvert<OrderFullTimeMasterMatchLogDto, FullTimeMasterMatchLogExcelDto> {

    @Override
    public FullTimeMasterMatchLogExcelDto convert(OrderFullTimeMasterMatchLogDto source) {
        FullTimeMasterMatchLogExcelDto target = new FullTimeMasterMatchLogExcelDto();
        target.setMatchType(PushLogMatchType.FULL_TIME_MASTER.getDesc());
        target.setOrderCityName(source.getOrderCityName());
        target.setOrderNo(source.getOrderNo());
        target.setOrderCreateTime(Objects.isNull(source.getOrderCreateTime()) ? "-" : DateUtils.formatDateTime(source.getOrderCreateTime()));
        target.setOrderSource(source.getOrderSource());
        target.setServeTypeName(source.getServeTypeName());
        target.setMasterId(source.getMasterId().toString());
        target.setMasterName(source.getMasterName());

        if (Objects.isNull(source.getIsMatchSuccess())) {
            target.setIsMatchSuccess("-");
        } else {

            if (source.getIsMatchSuccess() == 1) {
                target.setIsMatchSuccess("是");
            } else {
                target.setIsMatchSuccess("否");
            }
        }
        target.setMatchFailReason(Strings.isNullOrEmpty(source.getMatchFailReason()) ? "" : source.getMatchFailReason());

        if (Objects.isNull(source.getIsFilter())) {
            target.setIsFilter("否");
        } else {
            if (source.getIsFilter() == 1) {
                target.setIsFilter("是");
            } else {
                target.setIsFilter("否");
            }
        }

        target.setFilterReason(Strings.isNullOrEmpty(source.getFilterReason()) ? "" : source.getFilterReason());

        if (Objects.isNull(source.getIsDistribute())) {
            target.setIsDistribute("否");

        } else {
            if (source.getIsDistribute() == 1) {

                target.setIsDistribute("是");
            } else {
                target.setIsDistribute("否");
            }
        }

        target.setDistributeRule(Strings.isNullOrEmpty(source.getDistributeRule()) ? "-" : source.getDistributeRule());

        if (Objects.isNull(source.getIsAutoGrabSuccess())) {
            target.setIsAutoGrabSuccess("否");

        } else {
            if (source.getIsAutoGrabSuccess() == 1) {
                target.setIsAutoGrabSuccess("否");
            } else {
                target.setIsAutoGrabSuccess("否");
            }
        }

        target.setAutoGrabFailReason(Strings.isNullOrEmpty(source.getAutoGrabFailReason()) ? "" : source.getAutoGrabFailReason());

        target.setOrderVersion(source.getOrderVersion());

        target.setCreateTime(Objects.isNull(source.getCreateTime()) ? "-" : DateUtils.formatDateTime(source.getCreateTime()));

        return target;
    }
}
