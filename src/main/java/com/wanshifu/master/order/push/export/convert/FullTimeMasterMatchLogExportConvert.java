package com.wanshifu.master.order.push.export.convert;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.dto.CooperationBusinessPushMatchLogDto;
import com.wanshifu.master.order.push.domain.dto.OrderFullTimeMasterMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.export.exceldto.CooperationBusinessPushMatchLogExcelDto;
import com.wanshifu.master.order.push.export.exceldto.FullTimeMasterMatchLogExcelDto;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 11:39
 */
public class FullTimeMasterMatchLogExportConvert implements PushMatchLogExportConvert<OrderFullTimeMasterMatchLogDto, FullTimeMasterMatchLogExcelDto> {

    @Override
    public FullTimeMasterMatchLogExcelDto convert(OrderFullTimeMasterMatchLogDto source) {
        FullTimeMasterMatchLogExcelDto target = new FullTimeMasterMatchLogExcelDto();
        target.setMatchType(PushLogMatchType.FULL_TIME_MASTER.getDesc());
        target.setOrderCityName(source.getOrderCityName());
        target.setOrderNo(source.getOrderNo());
        target.setOrderCreateTime(Objects.isNull(source.getOrderCreateTime()) ? "-" : DateUtils.formatDateTime(source.getOrderCreateTime()));
        target.setOrderSource(source.getOrderSource());
        target.setServeTypeName(source.getServeTypeName());
        target.setMasterId(source.getMasterId().toString());
        target.setMasterName(source.getMasterName());

        if (Objects.isNull(source.getIsMatchSuccess())) {
            target.setIsMatchSuccess("-");
        } else {

            if (source.getIsMatchSuccess() == 1) {
                target.setIsMatchSuccess("�?);
            } else {
                target.setIsMatchSuccess("�?);
            }
        }
        target.setMatchFailReason(Strings.isNullOrEmpty(source.getMatchFailReason()) ? "" : source.getMatchFailReason());

        if (Objects.isNull(source.getIsFilter())) {
            target.setIsFilter("�?);
        } else {
            if (source.getIsFilter() == 1) {
                target.setIsFilter("�?);
            } else {
                target.setIsFilter("�?);
            }
        }

        target.setFilterReason(Strings.isNullOrEmpty(source.getFilterReason()) ? "" : source.getFilterReason());

        if (Objects.isNull(source.getIsDistribute())) {
            target.setIsDistribute("�?);

        } else {
            if (source.getIsDistribute() == 1) {

                target.setIsDistribute("�?);
            } else {
                target.setIsDistribute("�?);
            }
        }

        target.setDistributeRule(Strings.isNullOrEmpty(source.getDistributeRule()) ? "-" : source.getDistributeRule());

        if (Objects.isNull(source.getIsAutoGrabSuccess())) {
            target.setIsAutoGrabSuccess("�?);

        } else {
            if (source.getIsAutoGrabSuccess() == 1) {
                target.setIsAutoGrabSuccess("�?);
            } else {
                target.setIsAutoGrabSuccess("�?);
            }
        }

        target.setAutoGrabFailReason(Strings.isNullOrEmpty(source.getAutoGrabFailReason()) ? "" : source.getAutoGrabFailReason());

        target.setOrderVersion(source.getOrderVersion());

        target.setCreateTime(Objects.isNull(source.getCreateTime()) ? "-" : DateUtils.formatDateTime(source.getCreateTime()));

        return target;
    }
}
