package com.wanshifu.master.order.push.controller.export;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.request.export.PushExportTaskListRqt;
import com.wanshifu.master.order.push.domain.response.export.PushExportTaskResp;
import com.wanshifu.master.order.push.export.PushExportTaskService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 16:17
 */
@RestController
@RequestMapping("/export")
public class ExportController {

    @Resource
    private PushExportTaskService pushExportTaskService;

    @PostMapping("/taskList")
    public SimplePageInfo<PushExportTaskResp> list(@Valid @RequestBody PushExportTaskListRqt rqt) {
        return pushExportTaskService.list(rqt);
    }

}
