package com.wanshifu.master.order.push.service.strategyCombination.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.BaseSelectStrategyApi;
import com.wanshifu.master.order.push.api.SimulatePushApi;
import com.wanshifu.master.order.push.api.StrategyCombinationApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.strategyCombination.DetailResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.strategyCombination.ListResp;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.GetStrategyCombinationSimulateRqt;
import com.wanshifu.master.order.push.domain.rqt.strategyCombination.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.strategyCombination.StrategyCombinationService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StrategyCombinationServiceImpl implements StrategyCombinationService {

    private final AddressCommonService addressCommon;
    private final GoodsCommonService goodsCommon;
    // private final AuthHandler authHandler;
    private final IopAccountApi iopAccountApi;
    private final StrategyCombinationApi strategyCombinationApi;

    private final SimulatePushApi simulatePushApi;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
       return strategyCombinationApi.create(rqt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return strategyCombinationApi.update(rqt);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return strategyCombinationApi.enable(rqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();

        Page<?> startPage = PageHelper.startPage(pageNum, pageSize);
        SimplePageInfo<StrategyCombination> strategyCombinationSimplePageInfo = strategyCombinationApi.list(rqt);

        List<StrategyCombination> strategyCombinationList = strategyCombinationSimplePageInfo.getList();

        String cityIdsStr = strategyCombinationList.stream().map(StrategyCombination::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));

        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        //类目名称
        List<Long> goodsIds = strategyCombinationList.stream().map(StrategyCombination::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));
        Map<Long, List<StrategyCombinationSimulate>> strategyCombinationSimulateMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(strategyCombinationList)){
            List<Long> combinationIds = strategyCombinationList.stream().map(StrategyCombination::getCombinationId).collect(Collectors.toList());
            GetStrategyCombinationSimulateRqt getStrategyCombinationSimulateRqt = new GetStrategyCombinationSimulateRqt();
            getStrategyCombinationSimulateRqt.setCombinationIds(combinationIds);
            strategyCombinationSimulateMap = simulatePushApi.getStrategyCombinationSimulate(getStrategyCombinationSimulateRqt)
                    .stream().collect(Collectors.groupingBy(StrategyCombinationSimulate::getStrategyCombinationId));
        }

        List<Long> updateAccountIds = strategyCombinationList.stream().map(StrategyCombination::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        Map<Long, List<StrategyCombinationSimulate>> finalStrategyCombinationSimulateMap = strategyCombinationSimulateMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(strategyCombinationList, ListResp.class, (s, t) -> {
            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setProvince("全国");
                t.setCity("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCity(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
                t.setProvince(addresses.stream().map(Address::getLv2DivisionName).distinct().collect(Collectors.joining(",")));
            }
            if (StringUtils.equals(s.getCategoryIds(), "all")) {
                t.setCategoryName("全部(不限类目)");
            } else {
                t.setCategoryName(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }
            List<StrategyCombinationSimulate> strategyCombinationSimulates = finalStrategyCombinationSimulateMap.get(s.getCombinationId());
            Integer simulationStatus = 0;
            if (CollectionUtils.isNotEmpty(strategyCombinationSimulates)) {
                //按照测算时间取最新的测算记录 1：测算中�?：测算完�?
                simulationStatus = strategyCombinationSimulates.stream().max(Comparator.comparing(StrategyCombinationSimulate::getSimulateTime)).map(StrategyCombinationSimulate::getSimulateStatus).orElse(0);
            }
            //0: 待测算，1：测算中�?: 测算完成
            t.setSimulationStatus(simulationStatus);
            InterprectChineseUtil.reflexEnum(t);
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(DeleteRqt rqt) {
        return strategyCombinationApi.delete(rqt);
    }


    @Override
    public DetailResp detail(DetailRqt rqt) {
        DetailResp detailResp = strategyCombinationApi.combinationDetail(rqt);


        String cityIds = detailResp.getCityIds();
        if (StringUtils.equals(cityIds, "all")) {
            detailResp.setProvinceIds("all");
        } else {
            //城市地址
            List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIds);
            Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

            List<Address> addresses = Arrays.stream(cityIds.split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
            detailResp.setProvinceIds(addresses.stream().map(it -> it.getLv2DivisionId().toString()).distinct().collect(Collectors.joining(",")));
        }

        return detailResp;
    }
}
