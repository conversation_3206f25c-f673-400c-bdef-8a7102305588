package com.wanshifu.master.order.push.service.exclusiveScheduler.impl;

import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.ExclusiveOrderSchedulerApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.exclusiveScheduler.ListResp;
import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.exclusiveScheduler.ExclusiveOrderSchedulerService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExclusiveOrderSchedulerImpl implements ExclusiveOrderSchedulerService {

    private final ExclusiveOrderSchedulerApi exclusiveOrderSchedulerApi;

//    private final AuthHandler authHandler;
    private final IopAccountApi iopAccountApi;

    private final GoodsCommonService goodsCommon;

    @Override
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        exclusiveOrderSchedulerApi.create(rqt);
        return 0;
    }

    @Override
    public int update(UpdateRqt rqt) {
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        exclusiveOrderSchedulerApi.update(rqt);
        return 0;
    }

    @Override
    public ExclusiveOrderScheduler detail(DetailRqt rqt) {
        return null;
    }

    @Override
    public int enable(EnableRqt rqt) {
        return 0;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        final SimplePageInfo<ExclusiveOrderScheduler> list = exclusiveOrderSchedulerApi.list(rqt);


        final List<ExclusiveOrderScheduler> exclusiveOrderSchedulers = list.getList();
        List<Long> updateAccountIds = exclusiveOrderSchedulers.stream().map(ExclusiveOrderScheduler::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        //类目名称
        List<Long> goodsIds = exclusiveOrderSchedulers.stream().map(ExclusiveOrderScheduler::getCategoryId)
                .map(row->row.longValue()).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));



        List<ListResp> listResps = BeanCopyUtil.copyListProperties(exclusiveOrderSchedulers, ListResp.class, (d, v) -> {
            v.setCategoryName(goodsNameMap.get(d.getCategoryId().longValue()));
            v.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(d.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(list.getPages());
        listRespSimplePageInfo.setPageNum(list.getPageNum());
        listRespSimplePageInfo.setTotal(list.getTotal());
        listRespSimplePageInfo.setPageSize(list.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    public int delete(DeleteRqt rqt) {
        return 0;
    }

    @Override
    public List<ExclusiveOrderScheduler> selectBySnapshotIdList(List<Long> snapshotIdList) {
        return null;
    }
}
