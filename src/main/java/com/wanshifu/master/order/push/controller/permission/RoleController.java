package com.wanshifu.master.order.push.controller.permission;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;
import com.wanshifu.master.order.push.service.permission.RoleService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("role")
public class RoleController {

    @Resource
    private RoleService roleService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddRoleRqt rqt) {
        return roleService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRoleRqt rqt) {
        return roleService.update(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<GetRoleListResp> list(@Valid @RequestBody GetRoleListRqt rqt) {
        return roleService.list(rqt);
    }


    @PostMapping(value = "detail")
    public GetRoleDetailResp detail(@Valid @RequestBody GetRoleDetailRqt rqt) {
        return roleService.detail(rqt);
    }


    @PostMapping(value = "delete")
    public Integer detail(@Valid @RequestBody DeleteRoleRqt rqt) {
        return roleService.delete(rqt);
    }

    @PostMapping(value = "addAccount")
    public Integer addAccount(@Valid @RequestBody AddAccountRqt rqt) {
        return roleService.addAccount(rqt);
    }

}
