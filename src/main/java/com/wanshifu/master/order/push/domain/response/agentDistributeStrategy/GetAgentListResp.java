package com.wanshifu.master.order.push.domain.response.agentDistributeStrategy;


import lombok.Data;

import java.util.List;

@Data
public class GetAgentListResp {

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;
    private String masterSourceType;

    private Long provinceDivisionId;

    private String province;

    private Long cityDivisionId;

    private String city;
    
    private String divisionNames;

    private Integer agentStatus;


    private List<GetAgentListResp.AgentServe> serveList;


    private List<GetAgentListResp.AgentDivision> divisionList;

    @Data
    public static class AgentServe{

        private Long serveId;

        private String serveName;

    }


    @Data
    public static class AgentDivision{

        private Long thirdDivisionId;

        private String thirdDivisionName;

        private List<GetAgentListResp.AgentFourthDivision> fourthDivisionList;

    }

    @Data
    public static class AgentFourthDivision{

        private Long fourthDivisionId;

        private String fourthDivisionName;

    }
}
