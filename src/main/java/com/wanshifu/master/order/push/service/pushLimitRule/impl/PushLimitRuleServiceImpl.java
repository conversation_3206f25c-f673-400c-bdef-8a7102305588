package com.wanshifu.master.order.push.service.pushLimitRule.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.PushLimitRuleApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.pushLimitRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushLimitRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.pushLimitRule.PushLimitRuleService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PushLimitRuleServiceImpl implements PushLimitRuleService {

    @Resource
    private PushLimitRuleApi pushLimitRuleApi;


    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private ServeCommonService serveCommonService;

    @Override
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushLimitRuleApi.create(rqt);
    }



    @Override
    public int update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushLimitRuleApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {
        PushLimitRule pushLimitRule = pushLimitRuleApi.detail(rqt);
        if(Objects.isNull(pushLimitRule)){
            return null;
        }
        DetailResp detailResp = new DetailResp();
        BeanCopyUtil.copyProperties(pushLimitRule,detailResp,"limitServeRule","exclusiveRule","crowdGroup");
        if(StringUtils.isNotBlank(pushLimitRule.getLimitServeRule())){
            DetailResp.LimitServeRule limitServeRule = JSON.parseObject(pushLimitRule.getLimitServeRule(), DetailResp.LimitServeRule.class);
            detailResp.setLimitServeRule(limitServeRule);


            Set<Long> serveIds = detailResp.getLimitServeRule().getItemList().stream().filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                    .flatMap(it -> it.getServeIdList().stream())
                    .collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());


            Map<Long, String> serveInfoMap =Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(serveIds)){
                serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                        .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
            }
            Map<Long, String> finalServeInfoMap = serveInfoMap;
            detailResp.getLimitServeRule().getItemList().forEach(item -> {

                List<List<Long>> serveIdListList = item.getServeIdList();
                if(CollectionUtils.isNotEmpty(serveIdListList)){
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
            });

        }

        if(StringUtils.isNotBlank(pushLimitRule.getExclusiveRule())){
            DetailResp.ExclusiveRule exclusiveRule = JSON.parseObject(pushLimitRule.getExclusiveRule(), DetailResp.ExclusiveRule.class);
            detailResp.setExclusiveRule(exclusiveRule);
        }

        if(StringUtils.isNotBlank(pushLimitRule.getCrowdGroup())){
            DetailResp.CrowdGroup crowdGroup = JSON.parseObject(pushLimitRule.getCrowdGroup(), DetailResp.CrowdGroup.class);
            detailResp.setCrowdGroup(crowdGroup);

            //配置中的师傅人群id
            List<Long> masterGroupIds = crowdGroup.getItemList().stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());


            Map<Long, String> groupNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }

            Map<Long,String> finalGroupNameMap = groupNameMap;


            crowdGroup.getItemList().forEach(itemVo -> {
                //师傅人群
                String itemTitle = "师傅人群";
                List<DetailResp.TermItem> groupTermItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包含", "not_in"));
                List<DetailResp.ValueItem> valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(itemVo.getItemValue()), "师傅人群"), itemVo.getItemValue()));
                itemVo.setItemTitle(itemTitle);
                itemVo.setTermList(groupTermItems);
                itemVo.setValueList(valueItems);
                itemVo.setItemType("master_group");
            });
        }


        return detailResp;
    }

    @Override
   public SimplePageInfo<ListResp> list(ListRqt rqt){

        SimplePageInfo<PushLimitRule> simplePageInfo = pushLimitRuleApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PushLimitRule> pushLimitRuleList = simplePageInfo.getList();


        List<Long> updateAccountIds = pushLimitRuleList.stream().map(PushLimitRule::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        //城市地址
        String cityIdsStr = pushLimitRuleList.stream().map(PushLimitRule::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));


        List<PushLimitRule> crowdGroupLimitRuleList = pushLimitRuleList.stream().filter(pushLimitRule -> "crowd_group".equals(pushLimitRule.getCrowdType())).collect(Collectors.toList());

        List<Long> masterGroupIds = new ArrayList<>();
        crowdGroupLimitRuleList.forEach(pushLimitRule -> {
            CreateRqt.CrowdGroup crowdGroup = JSON.parseObject(pushLimitRule.getCrowdGroup(), CreateRqt.CrowdGroup.class);
            masterGroupIds.addAll(crowdGroup.getItemList().stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList()));
        });


        Map<Long, String> groupNameMap = Maps.newHashMap();

        if(CollectionUtils.isNotEmpty(crowdGroupLimitRuleList)){

            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }
        }

        List<Address> divisionInfoListByDivisionIds = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        final Map<Long, String> finalGroupNameMap = groupNameMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(pushLimitRuleList, ListResp.class, (s, t) -> {


            if("crowd_group".equals(s.getCrowdType())){
                //配置中的师傅人群id
                List<Long> groupIdList = JSON.parseObject(s.getCrowdGroup(), CreateRqt.CrowdGroup.class).getItemList().stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());
                List<String> groupNameList = groupIdList.stream().map(it -> finalGroupNameMap.get(it)).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCrowdValue(groupNameList.stream().distinct().collect(Collectors.joining(",")));

            }else if("crowd_label".equals(s.getCrowdType())){
                if("cooperative_operation".equals(s.getCrowdLabel())){
                    t.setCrowdValue("合作经营师傅");
                }

            }

            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }

            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }


    @Override
    public Integer enable(EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushLimitRuleApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteRqt rqt){
        return pushLimitRuleApi.delete(rqt);
    }

}
