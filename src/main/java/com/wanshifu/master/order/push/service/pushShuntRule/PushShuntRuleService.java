package com.wanshifu.master.order.push.service.pushShuntRule;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.pushShuntRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushShuntRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushShuntRule.*;

/**
 * <AUTHOR>
 * @date 2025/03/08 15:13
 */
public interface PushShuntRuleService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);


    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

}
