package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.vo.strategyCombination.AlternateStrategyVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-24 14:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AlternateStrategyDto extends AlternateStrategyVo {
    private OpenConditionQlExpression openConditionQlExpression;

    @Data
    @AllArgsConstructor
    public static class OpenConditionQlExpression {
        private String qlExpression;
        private String qlExpressionParams;
    }
}
