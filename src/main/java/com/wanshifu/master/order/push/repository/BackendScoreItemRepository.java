//package com.wanshifu.master.order.push.repository;
//
//import cn.hutool.core.util.StrUtil;
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.po.ScoreItem;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Collection;
//import java.util.Collections;
//import java.util.List;
//
//@Repository
//public class BackendScoreItemRepository extends BaseRepository<ScoreItem> {
//
//
//    public List<ScoreItem> selectByItemCodes(List<String> itemCodes) {
//        if (CollectionUtils.isEmpty(itemCodes)) {
//            return Collections.emptyList();
//        }
//        Example example = new Example(ScoreItem.class);
//        example.createCriteria().andIn("itemCode", itemCodes)
//                .andEqualTo("isDelete", 0);
//        return this.selectByExample(example);
//    }
//
//    public List<ScoreItem> selectByItemName(String itemName) {
//        Example example = new Example(ScoreItem.class);
//        if (StringUtils.isNotBlank(itemName)) {
//            example.createCriteria().andLike("itemName", StrUtil.format("%{}%", itemName));
//        }
//        return this.selectByExample(example);
//    }
//}