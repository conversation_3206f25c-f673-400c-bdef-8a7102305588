package com.wanshifu.master.order.push.domain.po;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 订单排序策略表
 */
@Data
@ToString
@Table(name = "order_sorting_strategy")
public class OrderSortingStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Long strategyId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 城市id，多个以逗号拼接
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 排序规则配置(JSON格式)
     */
    @Column(name = "sorting_rule")
    private String sortingRule;

    /**
     * 排序规则表达式
     */
    @Column(name = "rule_expression")
    private String ruleExpression;

    /**
     * 策略状态，1：启用，0：禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

}