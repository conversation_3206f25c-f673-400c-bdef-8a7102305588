package com.wanshifu.master.order.push.domain.response.pushRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.rqt.pushRule.CreateRqt;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 10:44
 */
@Data
public class DetailResp {

    private Integer ruleId;

    private String ruleName;

    private String ruleDesc;

    @NotEmpty
    private List<DetailResp.PushRuleEntity> pushRuleList;


    @Data
    public static class PushRuleEntity{

        private Integer appointType;

        /**
         * 大数据ab实验编号
         */
        private Integer testId;

        /**
         * 是否ab实验，1：是，0：否
         */
        private Integer testFlag;

        /**
         * 大数据实验组别id
         */
        private Integer testGroupId;

        /**
         * 大数据实验组别名称
         */
        private String testGroupName;

        private String pushRuleType;

        private Integer bestOfferNum;

        private Integer bestOfferIntervalTime;

        private CreateRqt.FixedRoundsRule fixedRoundsRule;

        private List<CreateRqt.DynamicRoundsRule> dynamicRoundsRuleList;

    }


    @Data
    public static class FixedRoundsRule{

        /**
         * 最佳报价数
         */
        @NotNull
        private Integer bestOfferNum;

        /**
         * 推送时间间隔
         */
        @NotNull
        private Integer delayMinutesBetweenRounds;

        /**
         * 首轮推送人数
         */
        @NotNull
        private Integer firstPushMasterNumPerRound;

        /**
         * 非首轮推送人数
         */
        @NotNull
        private Integer delayPushMasterNumPerRound;

        /**
         * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String firstPushMasterFlag;

        /**
         * 首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal firstPushMasterPercent;

        /**
         * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String delayPushMasterFlag;

        /**
         * 非首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal delayPushMasterPercent;    }


    @Data
    public static class DynamicRoundsRule{

        private String rounds;

        private Integer pushScoreStartValue;

        private Integer pushScoreEndValue;

        private Integer offerRate;

        private Integer masterNum;

        private Integer batchNum;

        private Integer deliveryPercent;

    }

}