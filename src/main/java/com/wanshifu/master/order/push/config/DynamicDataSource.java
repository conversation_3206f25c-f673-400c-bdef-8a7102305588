package com.wanshifu.master.order.push.config;

import com.wanshifu.framework.utils.TransmittableContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 16:02
 */
@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {
    /**
     * dataSourceKey线程本地变量，实现线程内部的透传（注意线程池场景）
     */
    private static final ThreadLocal<String> DATA_SOURCE_KEY_HOLDER = new ThreadLocal<>();


    @Override
    protected Object determineCurrentLookupKey() {

        String dataSourceKey = getDataSourceKey();
        if(StringUtils.isBlank(dataSourceKey)){
            dataSourceKey = DataSourceType.PUSH_DATASOURCE;
        }

        if(TransmittableContext.isPtScene()){
            dataSourceKey = TransmittableContext.SCENE_CODE_PT + "_" + dataSourceKey;
        }else{
            dataSourceKey = "default" + "_" + dataSourceKey;
        }
        log.info("DynamicDataSource thread ={},dataSourceKey ={}", Thread.currentThread().getName(), dataSourceKey);
        return dataSourceKey;

    }


    public static void setDataSourceKey(String dsKey) {
        DATA_SOURCE_KEY_HOLDER.set(dsKey);
    }

    public static String getDataSourceKey() {
        return DATA_SOURCE_KEY_HOLDER.get();
    }

    public static void clearDataSourceKey() {
        DATA_SOURCE_KEY_HOLDER.remove();
    }
}
