package com.wanshifu.master.order.push.domain.request.common;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * 描述 :  查询师傅指标数据.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MasterQuotaReq extends Pager {

    /**
     * 指标名称
     */
    private String quotaName;

    /**
     * 业务线： 1:企业，2：家庭
     */
    @NotNull
    private Integer businessLineId;
}