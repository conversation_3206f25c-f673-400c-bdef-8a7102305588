package com.wanshifu.master.order.push.mapper;

import cn.hutool.core.date.DateTime;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderPush;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17 10:34
 */
public interface OrderPushMapper extends IBaseCommMapper<OrderPush> {
    int softDeleteOrderPush(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "appendNote") String appendNote, @Param(value = "limitCount") int limitCount);

    List<OrderPush> selectByMasterIdAndOrderIdsFiler(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId, @Param("orderIdIdList") List<Long> orderIdIdList, @Param("currentDate") Date currentDate, @Param("tmplCityFlag") List<Integer> tmplCityFlag);

    List<OrderPush> selectIocWaitOfferFilter(@Param(value = "provinceNextId") List<Long> provinceNextIds, @Param("masterId") Long masterId, @Param(value = "queryNumber") int queryNumber, @Param("currentDate") Date currentDate);
    List<Long> selectWaitOfferMasterIdsByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId);

    List<Long> selectInviteMasterByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId, @Param(value = "currentDateTime") Date currentDateTime);
    List<Long> selectMasterCategorySelector(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId, @Param("currentDateTime") DateTime currentDateTime);
    List<OrderPush> selectOrderPushForNotice(@Param("orderId") Long orderId, @Param("provinceNextId") List<Long> provinceNextIds);

    List<OrderPush> selectOrderPushByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId, @Param(value = "currentDateTime") Date currentDateTime);


    List<OrderPush> selectMasterOrderPushByAccount(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId,
                                                   @Param(value = "accountId") Long accountId, @Param(value = "accountType") String accountType,
                                                   @Param("currentDateTime") DateTime currentDateTime);


    /**
     * 师傅小程序游客模式获取推单数据
     *
     * @param masterDivisionId masterDivisionId
     * @param stopOfferTime    stopOfferTime
     * @param pushTime         pushTime
     * @return List<OrderPush>
     */
    List<OrderPush> listByMasterDivisionIdForAppletUnLogin(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterDivisionId") Long masterDivisionId, @Param("stopOfferTime") Date stopOfferTime, @Param("pushTime") Date pushTime);

    int deleteByOrderIdAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "isDelete") Integer isDelete, @Param(value = "limit") Integer limit);

    int deleteByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "limit") int limit);

    void deleteExpiredOrderPushByPushIds(@Param(value = "tableName") String tableName, @Param(value = "pushIds") List<Long> pushIds);

    /**
     * 分批更新menuCategory
     * @param provinceNextId
     * @param menuCategory
     * @param orderId
     * @param limit
     * @param currentDateTime
     * @return
     */
    int updateMenuCategoryByOrderIdAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                            @Param(value = "menuCategory") int menuCategory, @Param(value = "limit") int limit, @Param(value = "currentDateTime") Date currentDateTime);

    /**
     * 分批更新menuCategory
     * @param provinceNextId
     * @param menuCategory
     * @param orderId
     * @param limit
     * @param currentDateTime
     * @return
     */
    int updateMenuCategoryByOrderIdAndLimitV2(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                              @Param(value = "menuCategory") int menuCategory, @Param(value = "limit") int limit, @Param(value = "currentDateTime") Date currentDateTime);

}
