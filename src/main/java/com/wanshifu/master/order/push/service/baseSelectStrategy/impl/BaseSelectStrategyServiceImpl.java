package com.wanshifu.master.order.push.service.baseSelectStrategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.BaseSelectStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.DistanceTypeEnum;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.*;
import com.wanshifu.master.order.push.service.baseSelectStrategy.BaseSelectStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BaseSelectStrategyServiceImpl implements BaseSelectStrategyService {

    private final BaseSelectStrategyApi baseSelectStrategyApi;

    private final IopAccountApi iopAccountApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(CreateRqt rqt) {
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return baseSelectStrategyApi.create(rqt);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UpdateRqt rqt) {
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return baseSelectStrategyApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyApi.detail(rqt);
        DetailResp detailResp = new DetailResp();
        BeanUtil.copyProperties(baseSelectStrategy, detailResp);
       // detailResp.setOpenCondition(JSON.parseObject(baseSelectStrategy.getOpenCondition(), OpenConditionVo.class));
        detailResp.setRangeSelect(JSON.parseObject(baseSelectStrategy.getRangeSelect(), RangeSelectVo.class));
        detailResp.setTechniqueSelect(JSON.parseObject(baseSelectStrategy.getTechniqueSelect(), TechniqueSelectVo.class));
        detailResp.setStatusSelect(JSON.parseObject(baseSelectStrategy.getStatusSelect(), StatusSelectVo.class));

        if(StringUtils.isNotBlank(baseSelectStrategy.getServeDataSelect())){
            detailResp.setServeDataSelect(JSON.parseObject(baseSelectStrategy.getServeDataSelect(), ServeDataSelectVo.class));
        }

        return detailResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return baseSelectStrategyApi.enable(rqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {

        SimplePageInfo<BaseSelectStrategy> selectStrategySimplePageInfo = baseSelectStrategyApi.list(rqt);
        List<BaseSelectStrategy> baseSelectStrategyList = selectStrategySimplePageInfo.getList();
        List<Long> updateAccountIds = baseSelectStrategyList.stream().map(BaseSelectStrategy::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(baseSelectStrategyList, ListResp.class, (d, v) -> {
            //订单标识
//            v.setOrderFlag(Optional.ofNullable(JSON.parseObject(d.getOpenCondition(), OpenConditionVo.class)).
//                    filter(it -> StringUtils.equals("orderFlag", it.getConditionType())).map(OpenConditionVo::getConditionValue).orElse(""));
            RangeSelectVo rangeSelectVo = JSON.parseObject(d.getRangeSelect(), RangeSelectVo.class);
            //范围类型
            v.setRangeType(Optional.ofNullable(rangeSelectVo).map(RangeSelectVo::getRangeType).orElse(""));
            //范围规则-距离类型
            String distanceType = Optional.ofNullable(rangeSelectVo).map(RangeSelectVo::getRangeRule)
                    .map(RangeSelectVo.RangeRule::getDistanceType).orElse("");
            String distanceTypeDesc = InterprectChineseUtil.enumExplain(DistanceTypeEnum.class, distanceType);
            Integer distanceValue = Optional.ofNullable(rangeSelectVo).map(RangeSelectVo::getRangeRule)
                    .map(RangeSelectVo.RangeRule::getDistance).orElse(null);
            if (StringUtils.isNotBlank(distanceTypeDesc) && distanceValue != null) {
                v.setRangeRule(StrUtil.format("{}{}公里内", distanceTypeDesc, distanceValue));
            }
            InterprectChineseUtil.reflexEnum(v);
            v.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(d.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(selectStrategySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(selectStrategySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(selectStrategySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(selectStrategySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(DeleteRqt rqt) {
        return baseSelectStrategyApi.delete(rqt);
    }
}