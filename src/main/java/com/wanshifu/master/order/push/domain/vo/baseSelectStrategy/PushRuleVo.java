package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/16 10:43
 */
@Data
public class PushRuleVo {

    /**
     * 推送类型
     * one_all：一次性推送，
     * batch_all：分批次推送
     */
    @NotEmpty
    @ValueIn("one_all,batch_all")
    private String pushType;

    /**
     * 分批时间
     */
    private Integer batchTime;

    /**
     * 分批人数
     */
    private Integer batchCount;
}
