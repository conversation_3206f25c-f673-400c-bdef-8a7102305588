package com.wanshifu.master.order.push.domain.request.common;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  批量根据人群id查询人群名称.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-23 17:51
 */
@Data
public class GetGroupByGroupIdsReq {

    @NotEmpty
    private List<Long> groupIds;
    /**
     * 用户和师傅区分标志 1-用户 2-师傅
     */
    @NotNull
    @ValueIn("1,2")
    private Integer personaId;
}