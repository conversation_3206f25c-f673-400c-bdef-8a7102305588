package com.wanshifu.master.order.push.domain.response.orderScoringStrategy;

import lombok.Data;

import java.util.Date;

@Data
public class GetOrderScoringStrategyListResp {

    private Integer strategyId;

    private String strategyName;

    private String strategyDesc;

    private String categoryNames;

    private String masterResources;

    private Integer itemNum;

    private Integer strategyStatus;

    private String lastUpdateAccountName;

    private Date createTime;

    private Date updateTime;

}
