package com.wanshifu.master.order.push.domain.vo.orderpush;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/20 17:28
 */
@Data
public class InfoOrderPushRecordDetailVo {


    /**
     * 师傅ID
     */
    @JSONField(name = "master_id")
    private Long masterId;


    /**
     * 推送时�?
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss",name = "push_time")
    private Date pushTime;

}
