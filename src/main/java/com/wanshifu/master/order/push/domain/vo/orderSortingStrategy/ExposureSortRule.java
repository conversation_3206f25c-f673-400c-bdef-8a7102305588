package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import com.wanshifu.master.order.push.domain.vo.common.ExposureRuleItem;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 干预排序规则
 * @date 2024/11/25 14:13
 */
@Data
public class ExposureSortRule {


    /**
     * 干预位置配置
     */
    @NotNull
    @Valid
    private ExposurePositionConfig exposurePositionConfig;

    /**
     * 排序规则
     */
    @NotEmpty
    @Valid
    private List<ExposureRuleItem> itemList;
}
