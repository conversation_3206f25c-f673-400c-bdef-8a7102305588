package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 指派类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum OrderPushFlagEnum {

    /**
     * 直接雇佣
     */
    NORMAL("normal","普通订�?),

    /**
     * 专属订单
     */
    EXCLUSIVE("exclusive","专属订单"),

    /**
     * 优选订�?
     */
    EXCELLENT("excellent","优选订�?),

    /**
     * 合约订单
     */
    CONTRACT("contract","合约订单"),


    /**
     * 合约订单
     */
    DIRECT_APPOINT("direct_appoint","直约订单"),


    /**
     * 合约订单
     */
    BRAND("brand","品牌订单"),



    /**
     * 合约订单
     */
    ORDER_PACKAGE("order_package","订单�?),


    /**
     * 合约订单
     */
    AGENT("agent","合作�?),

    AGREEMENT("agreement","协议师傅"),

    SUPPORT_MASTER("support_master","新师傅（扶持师傅�?),

    NEW_MODEL("new_model","样板城市师傅"),

    ENTERPRISE_APPOINT("enterprise_appoint","总包直接指派");


    public final String code;

    public final String name;

    OrderPushFlagEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, OrderPushFlagEnum> valueMapping = new HashMap<>((int) (OrderPushFlagEnum.values().length / 0.75));

    static {
        for (OrderPushFlagEnum instance : OrderPushFlagEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static OrderPushFlagEnum asValue(String value) {
        return valueMapping.get(value);
    }

    public static Boolean isInclude(Integer value) {
        if(Objects.isNull(value)) {
            return false;
        }
        for (OrderPushFlagEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

}
