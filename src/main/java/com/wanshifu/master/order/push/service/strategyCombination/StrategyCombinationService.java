package com.wanshifu.master.order.push.service.strategyCombination;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.strategyCombination.DetailResp;
import com.wanshifu.master.order.push.domain.response.strategyCombination.ListResp;
import com.wanshifu.master.order.push.domain.rqt.strategyCombination.*;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface StrategyCombinationService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    int enable(EnableRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    int delete(DeleteRqt rqt);
}