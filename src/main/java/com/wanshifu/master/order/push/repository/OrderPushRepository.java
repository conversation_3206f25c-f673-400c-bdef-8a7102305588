package com.wanshifu.master.order.push.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderPush;
import com.wanshifu.master.order.push.mapper.OrderPushMapper;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 10:37
 */
@Repository
public class OrderPushRepository extends BaseRepository<OrderPush> {

    @Resource
    private OrderPushMapper orderPushMapper;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer deleteByMasterIdAndOrderId(List<Long> provinceNextId, Long masterId, Long orderId) {
        int count;
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);
        criteria.andIn("provinceNextId", provinceNextId);
        count = this.deleteByCondition(condition);
        return count;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteByMasterIdAndOrderIds(List<Long> provinceNextId, Long masterId, List<Long> orderIds) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIds)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        this.deleteByCondition(condition);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteByOrderIdAndMasterIds(List<Long> provinceNextId, Long orderId, List<Long> masterIds) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIds)
                .andIn("provinceNextId", provinceNextId);

        this.deleteByCondition(condition);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int deleteByOrderIdAndLimit(List<Long> provinceNextId, Long orderId, Integer isDelete, Integer limit) {
        return orderPushMapper.deleteByOrderIdAndLimit(provinceNextId, orderId, isDelete, limit);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int deleteByOrderId(List<Long> provinceNextId, Long orderId, Integer limit) {
        return orderPushMapper.deleteByOrderId(provinceNextId, orderId, limit);
    }

    public List<OrderPush> selectByOrderId(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIn("provinceNextId", provinceNextId);
        condition.setOrderByClause("push_time  desc");
        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectByOrderIdV2(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete",0)
                .andIn("provinceNextId", provinceNextId);
        condition.setOrderByClause("create_time  desc");
        return this.selectByCondition(condition);
    }

    public int selectPushNumber(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete",0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectCountByCondition(condition);
    }

    /**
     * 更新推单记录的offerTime
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param date 报价时间
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer updateOfferTime(List<Long> provinceNextId, Long orderId, Long masterId, Date date) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setOfferTime(date);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);
        criteria.andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(orderPush, example);
        return count;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateOfferTimeAndNote(List<Long> provinceNextId, Long orderId, Long masterId, Date offerTime, String note) {
        OrderPush orderPush = new OrderPush();
        orderPush.setOfferTime(offerTime);
        orderPush.setIsDelete(1);
        orderPush.setNote(note);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        this.updateByConditionSelective(orderPush, condition);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateIsArrivedByOrderId(List<Long> provinceNextId, Long orderId, Integer isArrived) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId);
        criteria.andIn("provinceNextId", provinceNextId);

        OrderPush orderPush = new OrderPush();
        orderPush.setIsArrived(isArrived);
        this.updateByConditionSelective(orderPush, condition);
    }

    /**
     * 更新推单记录的first_view_time
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param date 当前时间
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer updateFirstViewTimeByMasterOrderId(List<Long> provinceNextId, Long orderId, Long masterId, Date date) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setFirstViewTime(date);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId).andIsNull("firstViewTime");
        criteria.andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(orderPush, example);
        return count;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer updatePullOrderDistanceByMasterOrderId(List<Long> provinceNextId, Long orderId, Long masterId, Integer isPullOrderDistance) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setIsPullOrderDistance(isPullOrderDistance);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(orderPush, example);
        return count;
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer updateByOrderIdAndMasters(List<Long> provinceNextId, Long orderId, List<Long> masterIdList, Date pushTime) {
        int count;
        OrderPush infoOrderPush = new OrderPush();
        infoOrderPush.setPushTime(pushTime);
        infoOrderPush.setIsDelete(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdList)
                .andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(infoOrderPush, example);
        return count;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer updateMasterAddressByOrderId(List<Long> provinceNextId, Long masterId, Long orderId,
                                                BigDecimal masterLongitude, BigDecimal masterLatitude,
                                                Long pushDistance, Integer pushDistanceType) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setMasterLongitude(masterLongitude);
        orderPush.setMasterLatitude(masterLatitude);
        orderPush.setPushDistance(pushDistance);
        if (Objects.nonNull(pushDistanceType)) {
            orderPush.setPushDistanceType(pushDistanceType);
        }
        orderPush.setUpdateTime(DateUtil.date());
        Example example = new Example(OrderPush.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(orderPush, example);
        return count;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int softDeleteOrderPush(List<Long> provinceNextId, Long orderId, String appendNote, int limitCount) {
        return orderPushMapper.softDeleteOrderPush(provinceNextId, orderId, appendNote, limitCount);
    }

    /**
     * 查询推单已拉取距离的记录
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    public List<OrderPush> selectModifyPushId(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        condition.selectProperties("masterId");
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("isDelete", 0).andIsNull("offerTime").andEqualTo("isPullOrderDistance", 1);
        criteria.andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public OrderPush selectByOrderIdAndMasterId(List<Long> provinceNextIds, Long orderId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextIds);

        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public List<OrderPush> selectByMasterIdAndAppointType(List<Long> provinceNextIds, Long masterId, Integer appointType,Integer limitCount) {
        Condition condition = new Condition(OrderPush.class);
        condition.selectProperties("orderId");
        condition.selectProperties("pushDistance");

        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("appointType", appointType)
                .andIn("provinceNextId", provinceNextIds)
                .andIsNull("offerTime")
                .andGreaterThan("stopOfferTime", new Date())
                .andGreaterThan("pushDistance", 0L)
                .andEqualTo("isDelete", 0);

        condition.setOrderByClause("push_id DESC limit " + limitCount);


        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectByMasterIdOrderIds(List<Long> provinceNextId, Long masterId, List<Long> orderIdIdList) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIdIdList)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }


    public List<OrderPush> selectByOrderIdsAndMasterId(List<Long> provinceNextId, Long orderId, Set<Long> masterIdSet) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdSet)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int orderPushAnew(Long provinceNextId, OrderPush orderPush) {
        int count;
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderPush.getOrderId())
                .andEqualTo("masterId", orderPush.getMasterId())
                .andEqualTo("provinceNextId", provinceNextId);
        //写分�?
        count = this.updateByConditionSelective(orderPush, condition);
        return count;
    }

    /**
     * 待报价列表v4(订单id查询)
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId
     * @param orderIdIdList
     * @param tmplCityFlag 样板城市订单标识
     * @return
     */
    public List<OrderPush> selectByMasterIdAndOrderIdsFiler(List<Long> provinceNextIds, Long masterId,
                                                            List<Long> orderIdIdList, List<Integer> tmplCityFlag) {
        return orderPushMapper.selectByMasterIdAndOrderIdsFiler(provinceNextIds, masterId, orderIdIdList, new Date(), tmplCityFlag);
    }


    /**
     * 更新推单记录菜单类别
     * @param provinceNextId
     * @param orderId
     * @param masterId
     * @param menuCategory
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateMenuCategory(List<Long> provinceNextId, Long orderId, Long masterId, Integer menuCategory) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setMenuCategory(menuCategory);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);
        //写分�?
        count = this.updateByConditionSelective(orderPush, condition);
        return count;
    }

    /**
     * 更新推单记录菜单类别
     * @param provinceNextId
     * @param orderId
     * @param masterIds
     * @param menuCategory
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateMenuCategory(List<Long> provinceNextId, Long orderId, List<Long> masterIds, Integer menuCategory) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setMenuCategory(menuCategory);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIds)
                .andIn("provinceNextId", provinceNextId);
        //写分�?
        count = this.updateByConditionSelective(orderPush, condition);
        return count;
    }




    /**
     * IOC(智能运营)活动待报�?筛选数�?
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId 师傅id
     * @param queryNumber 查询条数限制
     * @return
     */
    public List<OrderPush> selectIocWaitOfferFiler(List<Long> provinceNextIds, Long masterId, Integer queryNumber) {
        return orderPushMapper.selectIocWaitOfferFilter(provinceNextIds, masterId, queryNumber, new Date());
    }

    public List<Long> selectWaitOfferMasterIdsByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectWaitOfferMasterIdsByOrderId(provinceNextId, orderId);
    }

    public List<Long> selectInviteMasterByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectInviteMasterByOrderId(provinceNextId, orderId, new Date());
    }

    public List<OrderPush> selectByMasterId(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectByOrderIdsAndMasterId(List<Long> provinceNextId, Long orderId, List<Long> masterIdList) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId);
        criteria.andEqualTo("isDelete", 0);
        if (CollectionUtils.isNotEmpty(masterIdList)) {
            criteria.andIn("masterId", masterIdList);
        }
        criteria.andIn("provinceNextId", provinceNextId);
        return this.selectByCondition(condition);
    }

    /**
     * 查询订单已经查看师傅数量
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return 订单已经查看师傅数量
     */
    public Integer selectMasterViewNumber(List<Long> provinceNextId, Long orderId) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete", 0)
                .andIsNotNull("firstViewTime")
                .andIn("provinceNextId", provinceNextId);
        return this.selectCountByExample(example);
    }


    /**
     * 修改专属订单标签
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Integer updateOrderExclusiveFlagByOrderId(List<Long> provinceNextId, Long orderId, Integer exclusiveFlag) {
        int count;
        OrderPush infoOrderPush = new OrderPush();
        infoOrderPush.setExclusiveFlag(exclusiveFlag);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId);
        criteria.andIn("provinceNextId", provinceNextId);
        //写分�?
        count = this.updateByExampleSelective(infoOrderPush, example);
        return count;
    }

    public List<OrderPush> selectUserHireOrder(List<Long> provinceNextId, Long masterId, Date startTime, Date endTime) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("masterId", masterId);
        criteria.andBetween("pushTime", startTime, endTime);
        criteria.andEqualTo("appointType", 3);
        criteria.andEqualTo("orderFrom", OrderFrom.SITE.toString());
        criteria.andIsNull("offerTime");
        criteria.andEqualTo("isDelete", 0);
        criteria.andIn("provinceNextId", provinceNextId);
        return this.selectByExample(example);
    }

    /**
     * 更新竞争少标�?
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateLssContendFlagByOrderIdAndMasterIdList(List<Long> provinceNextId, Long orderId, List<Long> masterIdList) {
        int count;
        OrderPush updatePo = new OrderPush();
        updatePo.setLessContendFlag(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdList)
                .andEqualTo("lessContendFlag", 0)
                .andIn("provinceNextId", provinceNextId);

        //写分�?
        count = this.updateByExampleSelective(updatePo, example);
        return count;
    }


    public List<OrderPush> selectWaitOfficeOrderPullList(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        condition.selectProperties("orderId");

        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete", 0)
                .andIsNull("offerTime")
                .andEqualTo("isPullOrderDistance", 1)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByExample(condition);
    }


    /**
     * 删除分表过期的推单记�?
     * @param tableName 分表表名
     * @param pushIds 分表主键id
     */
    public void deleteExpiredOrderPushByPushIds(String tableName, List<Long> pushIds) {
        orderPushMapper.deleteExpiredOrderPushByPushIds(tableName,pushIds);
    }


    /**
     * 获取师傅推单列表。特殊场景才使用，不然存在性能问题
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @return
     */
    public List<OrderPush> getMasterNormalList(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        condition.selectProperties("orderId");
        condition.selectProperties("pushDivisionLevel");
        condition.setDistinct(true);

        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectOrderPushForNotice(List<Long> provinceNextIds, Long orderId) {
        return orderPushMapper.selectOrderPushForNotice(orderId, provinceNextIds);
    }


    public List<OrderPush> selectOrderPushByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectOrderPushByOrderId(provinceNextId, orderId, new Date());

    }


    public List<OrderPush> selectMasterOrderPushByAccount(List<Long> provinceNextId, Long masterId,Long accountId,String accountType) {
        return orderPushMapper.selectMasterOrderPushByAccount(provinceNextId, masterId,accountId,accountType,DateUtil.date());

    }



    public Integer getOrderShowNumOfPeople(List<Long> provinceNextId, Long orderId) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId);
        criteria.andIsNull("firstViewTime");
        criteria.andIn("provinceNextId", provinceNextId);

        return this.selectCountByExample(example);
    }


    public List<OrderPush> selectOrderPushNoOfferByOrderId(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIsNull("offerTime")
                .andGreaterThan("stopOfferTime", new Date())
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);
        return this.selectByCondition(condition);
    }

    /**
     * 更新师傅拉取待报价列表订单时�?
     * @param provinceNextIds 省下级地址id
     * @param orderIds 订单ids
     * @param masterId 师傅id
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateFirstPullTime(List<Long> provinceNextIds, List<Long> orderIds, Long masterId) {
        OrderPush orderPush = new OrderPush();
        orderPush.setFirstPullTime(new Date());

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andIn("orderId", orderIds)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextIds);
        this.updateByConditionSelective(orderPush, condition);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateMenuCategoryLimit(List<Long> provinceNextIds, Long orderId, int menuCategory, int limit) {
        int count;
        count = orderPushMapper.updateMenuCategoryByOrderIdAndLimit(provinceNextIds, orderId, menuCategory, limit, new Date());

        return count;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateMenuCategoryLimitV2(List<Long> provinceNextIds, Long orderId, int limit) {
        int count;
        count = orderPushMapper.updateMenuCategoryByOrderIdAndLimitV2(provinceNextIds, orderId, 0, limit, new Date());

        return count;
    }


    /**
     * 更新推单记录的first_view_time
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param date 当前时间
     * @return
     */
    public Integer updateIsPullViewByMasterOrderId(List<Long> provinceNextId, List<Long> orderIdList, Long masterId, Date date) {
        int count;
        OrderPush orderPush = new OrderPush();
        orderPush.setIsPullView(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orderId", orderIdList).andEqualTo("masterId", masterId).andEqualTo("isPullView",0);
        criteria.andIn("provinceNextId", provinceNextId);

        count = this.updateByExampleSelective(orderPush, example);
        return count;
    }


    public List<OrderPush> selectByOrderId(List<Long> provinceNextIds, Long orderId, Integer limitCount) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIn("provinceNextId", provinceNextIds);
        condition.setOrderByClause("push_score DESC limit " + limitCount);
        return this.selectByCondition(condition);
    }
}
