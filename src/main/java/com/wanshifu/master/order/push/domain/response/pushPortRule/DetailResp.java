package com.wanshifu.master.order.push.domain.response.pushPortRule;

import com.wanshifu.master.order.push.domain.dto.PortPushRuleDTO;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.CreateRqt;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
public class DetailResp {
    /**
     * 规则名称
     */
    @NotEmpty
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;


    /**
     * 应用城市id集合
     */
    private String cityIds;

    /**
     * 一级服务id集合
     */
    private String lv1ServeIds;


    /**
     * 指派模式
     */
    private String appointType;


    /**
     * 订单标签
     */
    private String orderTag;


    /**
     * 端口间隔时间
     */
    private Integer intervalTime;


    /**
     * 端口报价人数
     */
    private Integer offerNum;

    /**
     * 创建人id
     */
    private Long createAccountId;


    private List<PortPushRuleDTO> ruleList;
}
