package com.wanshifu.master.order.push.service.pushNotice;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.notice.domains.request.pushNotice.*;
import com.wanshifu.master.order.push.domain.request.pushnotice.TemplateListRqt;
import com.wanshifu.master.order.push.domain.response.pushNotice.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.ListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.MonitorListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.TemplateListResp;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

public interface PushNoticeStrategyService {


    Integer create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);


    Integer enable(EnableRqt rqt);

    Integer delete(DeleteRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);


    DetailResp detail(StrategyIdRqt rqt);

    SimplePageInfo<MonitorListResp> monitorList(MonitorListRqt rqt);

    Integer exportPushNoticeList(MonitorListRqt rqt, HttpServletResponse httpServletResponse);

    List<TemplateListResp> templateList(TemplateListRqt rqt);

}
