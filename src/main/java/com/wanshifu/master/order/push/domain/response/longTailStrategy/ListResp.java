package com.wanshifu.master.order.push.domain.response.longTailStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  长尾策略列表Resp.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class ListResp {
    /**
     * 策略id
     */
    private Integer longTailStrategyId;

    /**
     * 策略名称
     */
    private String longTailStrategyName;

    /**
     * 推送类型：nearby_more: 附近更多，bonus_order: 附近红包单,区县外订单：out_district
     */
    private String pushType;

    /**
     * 状态
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer isActive;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}