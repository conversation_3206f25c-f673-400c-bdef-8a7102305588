package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 分表配置
 * @date 2024/12/17 16:24
 */
@Data
@Table(name = "order_push_sharding_config")
public class OrderPushShardingConfig {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 表名后缀
     */
    @Column(name = "suffix_table_name")
    private String suffixTableName;

    /**
     * 城市地址id
     */
    @Column(name = "city_division_id")
    private String cityDivisionId;

    /**
     * 城市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
