package com.wanshifu.master.order.push.domain.response.orderPushMonitory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class OrderDynamicRoundsPushListResp {

    private Long id;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    private String orderNo;

    private String city;

    private String district;

    private String street;

    private String serveCategory;

    private String serveType;

    private String orderFrom;

    private String appointType;

    private Integer pushMasterNum;

    private JSONArray pushMasterDetailList;


    @Data
    public static class PushMasterDetail{

        private String roundsName;

        private String roundsPushNum;

        private String roundsOfferRate;
    }


}
