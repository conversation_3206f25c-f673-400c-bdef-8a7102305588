package com.wanshifu.master.order.push.controller.address;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.master.order.push.domain.request.common.AddressListReq;
import com.wanshifu.master.order.push.domain.request.common.GetSubListByDivisionIdReq;
import com.wanshifu.master.order.push.domain.response.address.AddressListResp;
import com.wanshifu.master.order.push.domain.response.common.GetSubListByDivisionIdResp;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-21 19:54
 */
@RestController
@RequestMapping("/address")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))

public class AddressController {

    private final AddressCommonService addressCommonService;

    /**
     * 查询所有地址数据(省市)
     *
     * @return
     */
    @PostMapping("/addressList")
    public List<AddressListResp> addressList(@RequestBody AddressListReq req) {
        return addressCommonService.addressList(req.getCityId());
    }

    @PostMapping("/getSubListByDivisionId")
    List<GetSubListByDivisionIdResp> getSubListByDivisionId(@RequestBody GetSubListByDivisionIdReq  rqt) {
        return addressCommonService.getSubListByDivisionId(rqt);
    }


    @PostMapping("/getCityList")
    public List<AddressListResp> getCityList(@RequestBody AddressListReq req) {
        return addressCommonService.getCityList(req.getCityId());
    }
}
