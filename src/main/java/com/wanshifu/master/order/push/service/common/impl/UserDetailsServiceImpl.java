//package com.wanshifu.master.order.push.service.common.impl;
//
//import cn.hutool.core.util.StrUtil;
//import com.wanshifu.master.order.push.domain.dto.UserDetail;
//import com.wanshifu.master.order.push.domain.dto.UserInfoDto;
//import com.wanshifu.master.order.push.util.ThreadLocalUtil;
//import org.springframework.security.core.authority.AuthorityUtils;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.core.userdetails.UserDetailsService;
//import org.springframework.security.core.userdetails.UsernameNotFoundException;
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
//import org.springframework.stereotype.Service;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-17 14:27
// */
//@Service
//public class UserDetailsServiceImpl implements UserDetailsService {
//
//    @Override
//    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
//        UserInfoDto userInfoDto = ThreadLocalUtil.get(StrUtil.format("iop_account_login_username_{}", username));
//        ThreadLocalUtil.remove();
//        //创建用户以及权限
//        return new UserDetail(username, new BCryptPasswordEncoder().encode(userInfoDto.getPassword()), AuthorityUtils.commaSeparatedStringToAuthorityList("ROLE_CLIENT"), userInfoDto.getUserId());
//    }
//}
