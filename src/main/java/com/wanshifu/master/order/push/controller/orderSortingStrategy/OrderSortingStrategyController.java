package com.wanshifu.master.order.push.controller.orderSortingStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.request.orderSortingStrategy.*;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.feature.SpecialCategoryResp;
import com.wanshifu.master.order.push.service.orderSortingStrategy.OrderSortingStrategyService;
import com.wanshifu.master.order.sort.domain.po.ComplexFeature;
import com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.ListRqt;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 描述 :  订单排序策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/orderSortStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderSortingStrategyController {

    private final OrderSortingStrategyService orderSortingStrategyService;

    /**
     * 创建订单排序策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public Integer create(@RequestBody @Valid CreateRqt rqt) {
        return orderSortingStrategyService.create(rqt);
    }

    /**
     * 修改订单排序策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    public Integer update(@RequestBody @Valid UpdateRqt rqt) {
        return orderSortingStrategyService.update(rqt);
    }

    /**
     * 订单排序策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return orderSortingStrategyService.detail(rqt);
    }

    /**
     * 订单排序策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return orderSortingStrategyService.list(rqt);
    }


    /**
     * 启用/禁用订单排序策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt) {
        return orderSortingStrategyService.enable(rqt);
    }

    /**
     * 删除订单排序策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer detele(@RequestBody @Valid DeleteRqt rqt) {
        return orderSortingStrategyService.delete(rqt);
    }

    /**
     * 特征配置-特殊类目-查询
     *
     * @return
     */
    @PostMapping("/specialCategoryConfig/query")
    public SpecialCategoryResp specialCategoryConfigQuery() {
        return orderSortingStrategyService.specialCategoryConfigQuery();
    }

    /**
     * 特征配置-特殊类目-更新
     *
     * @param rqt
     * @return
     */
    @PostMapping("/specialCategoryConfig/update")
    public Integer specialCategoryConfigUpdate(@RequestBody @Valid UpdateSpecialCategoryRqt rqt) {
        return orderSortingStrategyService.specialCategoryConfigUpdate(rqt);
    }
}