package com.wanshifu.master.order.push.domain.response.sortingStrategy;

import com.alibaba.fastjson.JSONArray;
import com.wanshifu.master.order.push.domain.response.common.GetGroupByGroupIdsResp;
import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
import lombok.Data;

import java.util.List;

/**
 * 描述 :  精排策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailV2Resp {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 类目id，多个以逗号拼接 all:不限类目
     */
    private String categoryIds;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 策略状态
     */
    private Integer strategyStatus;

    /**
     * 师傅人群列表
     */
    private List<GetGroupByGroupIdsResp> masterGroupList;

    /**
     * 规则json
     */
    private List<SortRule> ruleList;

    private String  orderFlag;
}