package com.wanshifu.master.order.push.service.orderPushMonitor;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.NoPushedMasterOrderListResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 推单监控Service
 * <AUTHOR>
 */
public interface OrderPushMonitorService {

    SimplePageInfo<NoPushedMasterOrderListResp> noPushedMasterOrderList(NoPushedMasterOrderListRqt rqt);

    Integer exportNoPushMasterOrderList(NoPushedMasterOrderListRqt rqt, HttpServletResponse httpServletResponse);

    List<GetFilterDataResp> filterData(GetFilterDataRqt rqt);

}
