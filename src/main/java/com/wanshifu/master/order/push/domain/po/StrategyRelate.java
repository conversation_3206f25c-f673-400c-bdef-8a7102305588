package com.wanshifu.master.order.push.domain.po;

import javax.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 策略关联表
 */
@Data
@ToString
@Table(name = "strategy_relate")
public class StrategyRelate {

    /**
     * PK
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 关联策略id
     */
    @Column(name = "relate_id")
    private Long relateId;

    /**
     * 关联类型[repush_policy:重推机制,strategy_combination_priority:策略组合-优先推荐路由,strategy_combination_alternate:策略组合-备用推荐路由]
     */
    @Column(name = "relate_type")
    private String relateType;

    /**
     * 初筛策略id
     */
    @Column(name = "base_select_strategy_Id")
    private Long baseSelectStrategyId;

    /**
     * 召回策略id
     */
    @Column(name = "filter_strategy_Id")
    private Long filterStrategyId;

    /**
     * 精排策略id
     */
    @Column(name = "sorting_strategy_Id")
    private Long sortingStrategyId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}