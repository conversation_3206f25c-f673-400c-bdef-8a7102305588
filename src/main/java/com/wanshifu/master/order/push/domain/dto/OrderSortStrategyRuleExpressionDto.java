package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  订单排序规则表达式Dto.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-28 17:26
 */
@Data
public class OrderSortStrategyRuleExpressionDto {
    /**
     * 通用项排序规则表达式集合
     */
    private ScoreRuleItemExpressionVo generalRuleExpression;

    /**
     * 差异项排序规则表达式集合
     */
    private DifferenceRuleRuleExpressionVo differenceRuleExpression;

    /**
     * 特殊项排序规则表达式集合
     */
    @Deprecated
    private ScoreRuleItemExpressionVo specialRuleRuleExpression;

//    /**
//     * 特殊项(类目)排序规则表达式集合
//     */
//    private ScoreRuleItemExpressionVo specialCategoryRuleExpression;


    @Data
    public static class ScoreRuleItemExpressionVo {
        /**
         * 整体权重 0-1
         */
        private BigDecimal weight;
        private List<ScoreRuleItemExpression> scoreRuleItemExpressionList;
    }

    @Data
    public static class ScoreRuleItemExpression {
        private String ruleName;
        private String ruleCode;
        private String openConditionRuleExpression;
        private String openConditionRuleParams;
        private String scoreRuleExpression;
        private String scoreRuleParams;
    }

    @Data
    public static class DifferenceRuleRuleExpressionVo {

        /**
         * 整体权重 0-1
         */
        private BigDecimal weight;

        private List<DifferenceRuleRuleExpressionItem> differenceRuleExpressionList;

    }

    @Data
    public static class DifferenceRuleRuleExpressionItem {
        /**
         * 开启条件
         */
        private String openConditionRuleExpression;
        private String openConditionRuleParams;

        /**
         * 规则项
         */
        private List<ScoreRuleItemExpression> scoreRuleList;
    }
}