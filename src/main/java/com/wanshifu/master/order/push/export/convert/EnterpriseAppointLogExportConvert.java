package com.wanshifu.master.order.push.export.convert;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.dto.EnterpriseAppointPushMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.export.exceldto.EnterpriseAppointPushMatchLogExcelDto;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 11:08
 */
public class EnterpriseAppointLogExportConvert implements PushMatchLogExportConvert<EnterpriseAppointPushMatchLogDto, EnterpriseAppointPushMatchLogExcelDto> {


    @Override
    public EnterpriseAppointPushMatchLogExcelDto convert(EnterpriseAppointPushMatchLogDto source) {
        EnterpriseAppointPushMatchLogExcelDto target = new EnterpriseAppointPushMatchLogExcelDto();

        target.setMatchType(PushLogMatchType.ENTERPRISE_APPOINT.getDesc());
        target.setOrderCityName(source.getOrderCityName());
        target.setOrderNo(source.getOrderNo());
        target.setUserId(Objects.isNull(source.getUserId()) ? "" : source.getUserId().toString());
        target.setOrderCreateTime(Objects.isNull(source.getOrderCreateTime()) ? "-" : DateUtils.formatDateTime(source.getOrderCreateTime()));
        target.setOrderSource(source.getOrderSource());
        target.setServeTypeName(source.getServeTypeName());
        target.setMasterId(source.getMasterId().toString());
        target.setMasterName(source.getMasterName());
        target.setRecruitId(String.valueOf(source.getRecruitId()));

        if (Objects.isNull(source.getIsMatchSuccess())) {
            target.setIsMatchSuccess("-");
        } else {

            if (source.getIsMatchSuccess() == 1) {
                target.setIsMatchSuccess("�?);
            } else {
                target.setIsMatchSuccess("�?);
            }
        }
        target.setMatchFailReason(Strings.isNullOrEmpty(source.getMatchFailReason()) ? "" : source.getMatchFailReason());

        if (Objects.isNull(source.getIsCalculatePriceSuccess())) {
            target.setIsCalculatePriceSuccess("�?);
        } else {
            if (source.getIsCalculatePriceSuccess() == 1) {
                target.setIsCalculatePriceSuccess("�?);
            } else {
                target.setIsCalculatePriceSuccess("�?);
            }
        }

        target.setCalculatePriceFailReason(Strings.isNullOrEmpty(source.getCalculatePriceFailReason()) ? "" : source.getCalculatePriceFailReason());

        if (Objects.isNull(source.getIsFilter())) {
            target.setIsFilter("�?);
        } else {
            if (source.getIsFilter() == 1) {
                target.setIsFilter("�?);
            } else {
                target.setIsFilter("�?);
            }
        }

        target.setFilterReason(Strings.isNullOrEmpty(source.getFilterReason()) ? "" : source.getFilterReason());

        if (Objects.isNull(source.getIsDistribute())) {
            target.setIsDistribute("�?);

        } else {
            if (source.getIsDistribute() == 1) {

                target.setIsDistribute("�?);
            } else {
                target.setIsDistribute("�?);
            }
        }

        target.setDistributeRule(Strings.isNullOrEmpty(source.getDistributeRule()) ? "-" : source.getDistributeRule());

        target.setOrderVersion(source.getOrderVersion());

        target.setCreateTime(Objects.isNull(source.getCreateTime()) ? "-" : DateUtils.formatDateTime(source.getCreateTime()));

        return target;
    }
}
