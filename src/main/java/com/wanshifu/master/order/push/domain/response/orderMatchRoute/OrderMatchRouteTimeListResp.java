package com.wanshifu.master.order.push.domain.response.orderMatchRoute;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class OrderMatchRouteTimeListResp {

    private Integer routeTimeId;

    private String categoryName;

    private String appointType;

    private String settingType;

    private Integer settingTime;

    private Integer settingNum;

    private String lastUpdateAccountName;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
