package com.wanshifu.master.order.push.domain.response.orderDistributeStrategy;

import lombok.Data;

import java.util.Date;

@Data
public class GetOrderDistributeStrategyListResp {

    private Integer strategyId;

    private String strategyName;

    private String orderFrom;

    private String categoryNames;

    private String distributeType;

    private String cityNames;

    private Integer strategyStatus;

    private String lastUpdateAccountName;

    private Date createTime;

    private Date updateTime;

}
