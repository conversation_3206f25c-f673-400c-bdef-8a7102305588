package com.wanshifu.master.order.push.controller.baseSelectStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;
import com.wanshifu.master.order.push.service.baseSelectStrategy.BaseSelectStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 描述 :  初筛策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/baseSelectStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BaseSelectStrategyController {

    private final BaseSelectStrategyService baseSelectStrategyService;


    /**
     * 初筛策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return baseSelectStrategyService.list(rqt);
    }

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return baseSelectStrategyService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return baseSelectStrategyService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return baseSelectStrategyService.detail(rqt);
    }

    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return baseSelectStrategyService.enable(rqt);
    }


    /**
     * 删除初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return baseSelectStrategyService.delete(rqt);
    }
}