package com.wanshifu.master.order.push.controller.oauth;

import com.wanshifu.master.order.push.domain.request.common.GetAccountListRqt;
import com.wanshifu.master.order.push.domain.response.common.AccountResp;
import com.wanshifu.master.order.push.service.common.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/account")
public class AccountController {

    @Resource
    private AccountService accountService;

    @PostMapping("/list")
    public List<AccountResp> list(@Valid @RequestBody GetAccountListRqt rqt) {
        return accountService.list(rqt);
    }

}
