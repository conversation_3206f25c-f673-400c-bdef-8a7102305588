package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-28 15:58
 */
@Data
public class DifferenceRuleVo{

    /**
     * 整体权重 (0,1]
     */
    @DecimalMin(inclusive = false, value = "0")
    @DecimalMax(value = "1")
    @NotNull
    private BigDecimal weight;

    @NotEmpty
    @Valid
    private List<SortRule> differenceRuleList;
}