package com.wanshifu.master.order.push.domain.vo.strategyCombination;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  组合策略-备用策略Vo.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-13 10:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AlternateStrategyVo extends PriorityStrategyVo {

    /**
     * 开启条�?
     */
    @NotNull
    @Valid
    private OpenCondition openCondition;

    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         * 开启条件项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem {

        /**
         * 条件项名�?canPushNumber:可推送人�?
         */
        @NotEmpty
        @ValueIn("canPushNumber,orderMasterDistance")
        private String itemName;

        /**
         * 符号 <
         */
        @NotEmpty
        @ValueIn("<")
        private String term;

        /**
         * 规则项�?
         */
        @NotNull
        private Integer itemValue;
    }
}
