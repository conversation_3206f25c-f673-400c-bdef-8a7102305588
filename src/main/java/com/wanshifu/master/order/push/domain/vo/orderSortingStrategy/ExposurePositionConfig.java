package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25 14:15
 */
@Data
public class ExposurePositionConfig {

    /**
     * 干预位置类型
     * 1：固定位置，2：动态位�?
     */
    @NotNull
    @ValueIn("1,2")
    private Integer positionType;

    /**
     * 动态位置参数，每？个单
     */
    private Integer dynamicEveryFewSize;

    /**
     * 动态位置参数，第？个位�?
     */
    private Integer dynamicPositionIndex;

}
