package com.wanshifu.master.order.push.domain.vo.pushNotice;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class OrderNoticeMonitorListExcelVo {

    /**
     * 推单时间
     */
    private Date date;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 城市
     */
    private String lv2Division;

    /**
     * 区县
     */
    private String lv3Division;

    /**
     * 街道
     */
    private String lv4Division;

    /**
     * 服务类目
     */
    private String category;

//    /**
//     * 一级服务类目
//     */
//    @Excel(name = "一级服务类目", width = 15,orderNum = "6")
//    private String level1NameServeCategory;
//
//    /**
//     * 二级服务类目
//     */
//    @Excel(name = "二级服务类目", width = 15,orderNum = "7")
//    private String level2NameServeCategory;
//
//    /**
//     * 三级服务类目
//     */
//    @Excel(name = "三级服务类目", width = 15,orderNum = "8")
//    private String level3NameServeCategory;

    /**
     * 服务类目
     */
    private String serveType;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 下单模式
     */
    private String appointType;

    /**
     * 命中触达组合
     */
    private String noticeStrategyCombination;

    /**
     * 命中触达策略
     */
    private String noticeStrategyName;

    /**
     * 触达渠道
     */
    private String pushNoticeChannel;

    /**
     * 触达师傅人数
     */
    private Integer pushNoticeNum;

    /**
     * 触达师傅拉取人数
     */
    private Integer pullMasterNum;

    /**
     * 触达师傅查看人数
     */
    private Integer viewMasterNum;

    /**
     * 触达师傅报价人数
     */
    private Integer offerMasterNum;



}
