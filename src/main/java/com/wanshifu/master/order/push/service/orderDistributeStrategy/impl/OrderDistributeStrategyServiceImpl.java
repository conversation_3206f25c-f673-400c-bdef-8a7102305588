package com.wanshifu.master.order.push.service.orderDistributeStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CompensateDistributeApi;
import com.wanshifu.master.order.push.api.OrderDistributeStrategyApi;
import com.wanshifu.master.order.push.api.OrderRoutingStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.DistributeType;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;
import com.wanshifu.master.order.push.domain.enums.OrderFromEnum;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderDistributeStrategy.GetOrderDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.DetailRqt;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.orderDistributeStrategy.OrderDistributeStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderDistributeStrategyServiceImpl implements OrderDistributeStrategyService {

    @Resource
    private OrderDistributeStrategyApi orderDistributeStrategyApi;


    @Resource
    private CompensateDistributeApi compensateDistributeApi;


    @Resource
    private OrderRoutingStrategyApi orderRoutingStrategyApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private ServeCommonService serveCommonService;


    @Override
    public Integer create(CreateOrderDistributeStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setCreateAccountId(loginUserId);
        rqt.setUpdateAccountId(loginUserId);
        return orderDistributeStrategyApi.create(rqt);
    }

    @Override
    public Integer update(UpdateOrderDistributeStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderDistributeStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableOrderDistributeStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderDistributeStrategyApi.enable(rqt);
    }

    @Override
    public GetOrderDistributeStrategyDetailResp detail(OrderDistributeStrategyDetailRqt rqt){

        GetOrderDistributeStrategyDetailResp resp = orderDistributeStrategyApi.detail(rqt);

        if(resp == null){
            return null;
        }

        List<GetOrderDistributeStrategyDetailResp.DistributeStrategyVo> distributeStrategyVoList = resp.getDistributeStrategyList();


        Set<Long> serveIds = distributeStrategyVoList.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;
        distributeStrategyVoList.forEach(distributeStrategyVo -> Optional.ofNullable(distributeStrategyVo.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

            List<List<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));


        List<GetOrderDistributeStrategyDetailResp.CompensateDistributeVo> compensateDistributeVoList = resp.getCompensateDistributeList();


        if(CollectionUtils.isNotEmpty(compensateDistributeVoList)){
            compensateDistributeVoList.forEach(compensateDistributeVo -> {
                if(Objects.nonNull(compensateDistributeVo.getCompensateDistributeId())){
                    DetailRqt detailRqt = new DetailRqt();
                    detailRqt.setDistributeId(compensateDistributeVo.getCompensateDistributeId());
                    CompensateDistribute compensateDistribute = compensateDistributeApi.detail(detailRqt);
                    compensateDistributeVo.setCompensateDistributeName(compensateDistribute.getStrategyName());
                }

                if(Objects.nonNull(compensateDistributeVo.getOrderRoutingStrategyId())){
                    com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt();
                    detailRqt.setStrategyId(compensateDistributeVo.getOrderRoutingStrategyId());
                    OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyApi.detail(detailRqt);
                    compensateDistributeVo.setOrderRoutingStrategyName(orderRoutingStrategy.getStrategyName());
                }
            });

        }
        return resp;

    }

    @Override
    public SimplePageInfo<GetOrderDistributeStrategyListResp> list(GetOrderDistributeStrategyListRqt rqt){

        SimplePageInfo<OrderDistributeStrategy> simplePageInfo = orderDistributeStrategyApi.list(rqt);


        SimplePageInfo<GetOrderDistributeStrategyListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderDistributeStrategy> orderDistributeStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = orderDistributeStrategyList.stream().map(OrderDistributeStrategy::getCategoryIds).flatMap(it -> Arrays.stream(it.split(","))).distinct().filter(it -> !StringUtils.equals("all", it)).map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));


        //类目名称
        String cityIdsStr = orderDistributeStrategyList.stream().map(OrderDistributeStrategy::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> StringUtils.isNotBlank(it))
                .collect(Collectors.joining(","));

        List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, String> addressNameMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));

        List<Long> updateAccountIds = orderDistributeStrategyList.stream().map(OrderDistributeStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<GetOrderDistributeStrategyListResp> listResps = BeanCopyUtil.copyListProperties(orderDistributeStrategyList, GetOrderDistributeStrategyListResp.class, (s, t) -> {
            //规则条数
            if (StringUtils.equals(s.getOpenCityMode(), "all")) {
                t.setCityNames("全国");
            } else {
                t.setCityNames(Arrays.stream(s.getCityIds().split(",")).map(it -> addressNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            DistributeType distributeType = DistributeType.asValue(s.getDistributeType());
            OrderFromEnum orderFromEnum = OrderFromEnum.asValue(s.getOrderFrom());
            t.setOrderFrom(orderFromEnum != null ? orderFromEnum.name : "-");
            t.setDistributeType(distributeType != null ? distributeType.getDesc() : "-");
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;

    }

    @Override
    public Integer delete(DeleteOrderDistributeStrategyRqt rqt){
        return orderDistributeStrategyApi.delete(rqt);
    }

}
