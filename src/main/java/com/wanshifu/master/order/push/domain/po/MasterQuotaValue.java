package com.wanshifu.master.order.push.domain.po;

import javax.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 师傅指标枚举值表
 */
@Data
@ToString
@Table(name = "master_quota_value")
public class MasterQuotaValue {

    /**
     * 指标枚举值id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "value_id")
    private Long valueId;

    /**
     * 指标id
     */
    @Column(name = "master_quota_id")
    private Long masterQuotaId;

    /**
     * 指标枚举值编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 指标枚举值名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}