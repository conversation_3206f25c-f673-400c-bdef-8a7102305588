package com.wanshifu.master.order.push.controller.pushRule;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.pushRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;
import com.wanshifu.master.order.push.service.pushRule.PushRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/pushRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushRuleController {

    @Resource
    private PushRuleService pushRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return pushRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return pushRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return pushRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return pushRuleService.list(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return pushRuleService.delete(rqt);
    }




}
