package com.wanshifu.master.order.push.export.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 17:32
 */
@Component
public class PushMatchLogExportFactory {

    private static final Map<String, PushMatchLogExportStrategy> STRATEGIES = new HashMap<>();


    @Autowired
    public PushMatchLogExportFactory(List<PushMatchLogExportStrategy> strategyList) {
        for (PushMatchLogExportStrategy strategy : strategyList) {
            // 每个策略实现需返回支持的exportType
            STRATEGIES.put(strategy.getExportType(), strategy);
        }
    }


    public static PushMatchLogExportStrategy getStrategy(String exportType) {
        PushMatchLogExportStrategy strategy = STRATEGIES.get(exportType);
        if (strategy == null) {
            throw new IllegalArgumentException("未知的导出类�?获取导出策略失败�?);
        }
        return strategy;
    }
}
