package com.wanshifu.master.order.push.service.exclusiveScheduler;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import com.wanshifu.master.order.push.domain.response.exclusiveScheduler.ListResp;
import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExclusiveOrderSchedulerService {
    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    ExclusiveOrderScheduler detail(DetailRqt rqt);

    int enable(EnableRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    int delete(DeleteRqt rqt);

    List<ExclusiveOrderScheduler> selectBySnapshotIdList(@Valid List<Long> snapshotIdList);
}
