package com.wanshifu.master.order.push.domain.response.longTailStrategyGroup;
import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  长尾策略列表Resp.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class ListResp {
    /**
     * 策略id
     */
    private Integer longTailStrategyGroupId;

    /**
     * 策略名称
     */
    private String longTailStrategyGroupName;

    private String longTailStrategyGroupDesc;

    private String categoryIds;

    private String category;

    private String openCityMode;

    private String city;

    private String pushType;


    /**
     * 状�?
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer isActive;

    /**
     * 策略状态中�?
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
