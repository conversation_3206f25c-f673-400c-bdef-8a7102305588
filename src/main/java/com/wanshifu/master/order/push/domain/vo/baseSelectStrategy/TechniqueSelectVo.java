package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  技能初筛.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 16:01
 */
@Data
public class TechniqueSelectVo {

    /**
     * 技能类型 filter_out_no_match_technique：过滤不匹配技能的师傅
     */
    @NotEmpty
    @ValueIn("filter_out_no_match_technique,filter_out_no_match_ikea")
    private String techniqueType;
}