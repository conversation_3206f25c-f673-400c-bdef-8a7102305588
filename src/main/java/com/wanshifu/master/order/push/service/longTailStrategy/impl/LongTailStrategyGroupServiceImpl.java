package com.wanshifu.master.order.push.service.longTailStrategy.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.LongTailStrategyApi;
import com.wanshifu.master.order.push.api.LongTailStrategyGroupApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.LongTailPushType;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.longTailStrategy.LongTailStrategyGroupService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyGroupServiceImpl implements LongTailStrategyGroupService {

    private final LongTailStrategyGroupApi longTailStrategyGroupApi;

    private final LongTailStrategyApi longTailStrategyApi;

    private final IopAccountApi iopAccountApi;

//    private final AuthHandler authHandler;

    private final AddressCommonService addressCommon;
    private final GoodsCommonService goodsCommon;

    private final ServeCommonService serveCommonService;

    /**
     * 策略组管理-获取列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        final SimplePageInfo<LongTailStrategyGroup> strategySimplePageInfo = longTailStrategyGroupApi.list(rqt);
        final List<LongTailStrategyGroup> strategySimplePageInfoList = strategySimplePageInfo.getList();

        //账号名
        List<Long> updateAccountIds = strategySimplePageInfoList.stream()
                .map(LongTailStrategyGroup::getUpdateAccountId).distinct()
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        String cityIdsStr = strategySimplePageInfoList.stream().map(LongTailStrategyGroup::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        //类目名称
        List<Long> goodsIds = strategySimplePageInfoList.stream().map(LongTailStrategyGroup::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));
        goodsNameMap.put(0L,"全部(不限类目)");


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(
                strategySimplePageInfoList, ListResp.class, (s, t) -> {
                    //省份
                    String cityIds = s.getCityIds();
                    if (StringUtils.equals(cityIds, "all")) {
                        t.setCity("全国");
                    } else {
                        List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                        t.setCity(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
                    }
                    if (StringUtils.equals(s.getCategoryIds(), "all")) {
                        t.setCategory("全部(不限类目)");
                    } else {
                        t.setCategory(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
                    }

                    t.setLastUpdateAccountName(
                            Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));

                    t.setPushType(LongTailPushType.asValue(s.getPushType()).getName());
                    InterprectChineseUtil.reflexEnum(t);
                });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(strategySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(strategySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(strategySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(strategySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    /**
     * 策略组管理-创建长尾单策略
     *
     * @param createRqt
     * @return
     */
    @Override
    public int create(CreateRqt createRqt) {
//        createRqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        createRqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyGroupApi.create(createRqt);
    }

    /**
     * 策略组管理-修改长尾单策略
     *
     * @param updateRqt
     * @return
     */
    @Override
    public int update(UpdateRqt updateRqt) {
        updateRqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyGroupApi.update(updateRqt);
    }

    /**
     * 策略组管理-更新策略状态（启用/禁用）
     *
     * @param enableRqt
     * @return
     */
    @Override
    public int updateStatus(EnableRqt enableRqt) {
        enableRqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyGroupApi.updateStatus(enableRqt);
    }

    /**
     * 策略组管理-删除长尾单策略
     *
     * @param deleteRqt
     * @return
     */
    @Override
    public int delete(DeleteRqt deleteRqt) {
        return longTailStrategyGroupApi.delete(deleteRqt);
    }

    /**
     * 策略组管理-策略详情
     *
     * @param detailRqt
     * @return
     */
    @Override
    public DetailResp detail(DetailRqt detailRqt) {
        final LongTailStrategyGroup detail = longTailStrategyGroupApi.detail(detailRqt);
        DetailResp detailResp=new DetailResp();
        BeanCopyUtil.copyProperties(detail, detailResp);
        JSONArray ruleJson = JSONObject.parseArray(detail.getStrategyJson());
        final List<DetailResp.LongTailGroupRuleResp> longTailGroupRuleRespList = JSONObject.parseArray(detail.getStrategyJson(), DetailResp.LongTailGroupRuleResp.class);
//        translateLongTailStrategyId(ruleJson);

        Set<Long> serveIds = longTailGroupRuleRespList.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }


        List<Long> longTailStrategyIdList = longTailGroupRuleRespList.stream().map(DetailResp.LongTailGroupRuleResp::getLongTailStrategyId).collect(Collectors.toList());
        //查询
        final com.wanshifu.master.order.push.domain.rqt.longTailStrategy.ListRqt
                listRqt = new com.wanshifu.master.order.push.domain.rqt.longTailStrategy.ListRqt();
        listRqt.setPageNum(1);
        listRqt.setPageSize(100);
        listRqt.setLongTailStrategyIdList(longTailStrategyIdList);

        //组合
        final SimplePageInfo<LongTailStrategy> longTailStrategySimplePageInfo = longTailStrategyApi.list(listRqt);
        final HashMap<Long, String> strategyMap = new HashMap<>();
        longTailStrategySimplePageInfo.getList().stream().forEach(
                row->{
                    strategyMap.put(row.getLongTailStrategyId(),row.getLongTailStrategyName());
                }
        );


        Map<Long, String> finalServeInfoMap = serveInfoMap;
        longTailGroupRuleRespList.forEach(sortRule -> Optional.ofNullable(sortRule.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {
            sortRule.setLongTailStrategyName(strategyMap.getOrDefault(sortRule.getLongTailStrategyId(),""));
            List<List<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));

        detailResp.setLongTailGroupRuleList(longTailGroupRuleRespList);
        return detailResp;
    }

    /**
     * 翻译策略id中文
     */
    private void translateLongTailStrategyId(JSONArray ruleJson){
        final List<Long> longTailStrategyIdList = ruleJson.stream().map(row -> {
            final JSONObject strategy = (JSONObject) row;
            return strategy.getLong("longTailStrategyId");
        }).distinct().collect(Collectors.toList());

        //查询
        final com.wanshifu.master.order.push.domain.rqt.longTailStrategy.ListRqt
                listRqt = new com.wanshifu.master.order.push.domain.rqt.longTailStrategy.ListRqt();
        listRqt.setPageNum(1);
        listRqt.setPageSize(100);
        listRqt.setLongTailStrategyIdList(longTailStrategyIdList);

        //组合
        final SimplePageInfo<LongTailStrategy> longTailStrategySimplePageInfo = longTailStrategyApi.list(listRqt);
        final HashMap<Long, String> strategyMap = new HashMap<>();
        longTailStrategySimplePageInfo.getList().stream().forEach(
                row->{
                    strategyMap.put(row.getLongTailStrategyId(),row.getLongTailStrategyName());
                }
        );
        //赋值
        ruleJson.stream().forEach(row->{
            final JSONObject strategy = (JSONObject) row;
            final Long longTailStrategyId = strategy.getLong("longTailStrategyId");
            strategy.put("longTailStrategyName",strategyMap.get(longTailStrategyId));
        });

    }
}
