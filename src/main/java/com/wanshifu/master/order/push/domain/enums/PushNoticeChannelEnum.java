package com.wanshifu.master.order.push.domain.enums;

import com.wanshifu.master.notice.domains.enums.PushNoticeChannel;

import java.util.HashMap;
import java.util.Map;

public enum PushNoticeChannelEnum {


    /**
     * push通知
     */
    PUSH("push","PUSH通知"),


    /**
     * 短信
     */
    SMS("sms","短信"),

    /**
     * 站内信
     */
    INBOX_MESSAGE("inbox_message","站内信"),

    /**
     * 微信公众号
     */
    WECHAT_OFFICIAL_ACCOUNTS("wechat_official_accounts","微信公众号"),

    /**
     * 语音外呼
     */
    VOICE_OUTBOUND_CALL("voice_outbound_call","语音外呼"),


    /**
     * 首页动态标签
     */
    ORDER_LABEL("order_label","首页动态标签"),


    /**
     * 待报价列表竞争少榜单
     */
    POPUP_WINDOW("popup_window","首页榜单");



    public final String code;

    public final String desc;

    PushNoticeChannelEnum(String code, String name) {
        this.code = code;
        this.desc = name;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, PushNoticeChannelEnum> valueMapping = new HashMap<>((int) (PushNoticeChannelEnum.values().length / 0.75));



    static {
        for (PushNoticeChannelEnum instance : PushNoticeChannelEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static PushNoticeChannelEnum asCode(String code) {
        return valueMapping.get(code);
    }

}
