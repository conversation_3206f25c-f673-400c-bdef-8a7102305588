package com.wanshifu.master.order.push.domain.response.baseSelectStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import com.wanshifu.master.order.push.domain.enums.RangeTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  初筛策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp{

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 订单标识
     */
    @TranslateEnum(enumClass= OrderFlagEnum.class)
    private String orderFlag;

    /**
     * 范围类型
     */
    @TranslateEnum(enumClass= RangeTypeEnum.class)
    private String rangeType;

    /**
     * 范围规则
     */
    private String rangeRule;

    /**
     * 策略状态
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer strategyStatus;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}