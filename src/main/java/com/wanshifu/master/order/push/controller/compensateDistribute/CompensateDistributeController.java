package com.wanshifu.master.order.push.controller.compensateDistribute;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.CompensateDistributeApi;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.DetailResp;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.ListResp;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;
import com.wanshifu.master.order.push.service.compensateDistribute.CompensateDistributeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/compensateDistribute")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CompensateDistributeController {

    @Resource
    private CompensateDistributeService compensateDistributeService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return compensateDistributeService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return compensateDistributeService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return compensateDistributeService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return compensateDistributeService.list(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return compensateDistributeService.delete(rqt);
    }

}
