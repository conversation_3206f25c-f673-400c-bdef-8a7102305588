package com.wanshifu.master.order.push.service.normalOrderDistributeStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.NormalOrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.api.CompensateDistributeApi;
import com.wanshifu.master.order.push.api.OrderMatchRoutingApi;
import com.wanshifu.master.order.push.api.OrderRoutingStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.normalOrderDistributeStrategy.NormalOrderDistributeStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NormalOrderDistributeStrategyServiceImpl implements NormalOrderDistributeStrategyService {


    @Resource
    private NormalOrderDistributeStrategyApi normalOrderDistributeStrategyApi;


    @Resource
    private OrderMatchRoutingApi orderMatchRoutingApi;


    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private ServeCommonService serveCommonService;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private CompensateDistributeApi compensateDistributeApi;

    @Resource
    private OrderRoutingStrategyApi orderRoutingStrategyApi;


    @Override
    @Transactional
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return normalOrderDistributeStrategyApi.create(rqt);
    }


    @Override
    @Transactional
    public Integer update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return normalOrderDistributeStrategyApi.update(rqt);
    }



    @Override
    @Transactional
    public Integer delete(DeleteRqt rqt) {
       return normalOrderDistributeStrategyApi.delete(rqt);
    }

    @Override
    @Transactional
    public Integer enable(EnableRqt rqt) {
        return normalOrderDistributeStrategyApi.enable(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {

        DetailResp detailResp = new DetailResp();

        NormalOrderDistributeStrategy normalOrderDistributeStrategy = normalOrderDistributeStrategyApi.detail(rqt);
        if (normalOrderDistributeStrategy != null) {
            BeanCopyUtil.copyProperties(normalOrderDistributeStrategy, detailResp);

//            List<DetailResp.MatchRoutingRule> matchRoutingRuleList = JSON.parseArray(normalOrderDistributeStrategy.getCompensateDistributeStrategyList(), DetailResp.MatchRoutingRule.class);
//
//
//            matchRoutingRuleList.forEach(matchRoutingRule -> {
//                com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt();
//                detailRqt.setRoutingId(matchRoutingRule.getMatchRoutingId());
//                OrderMatchRouting orderMatchRouting = orderMatchRoutingApi.detail(detailRqt);
//                if(orderMatchRouting != null){
//                    matchRoutingRule.setMatchRoutingName(orderMatchRouting.getRoutingName());
//                }
//
//            });
//            detailResp.setCompensateDistributeStrategyList(matchRoutingRuleList);
            if(StringUtils.isNotBlank(normalOrderDistributeStrategy.getCompensateDistributeList())){
                detailResp.setCompensateDistributeList(JSON.parseArray(normalOrderDistributeStrategy.getCompensateDistributeList(),DetailResp.CompensateDistributeVo.class));

                if(CollectionUtils.isNotEmpty(detailResp.getCompensateDistributeList())){
                    detailResp.getCompensateDistributeList().forEach(compensateDistributeVo -> {
                        if(Objects.nonNull(compensateDistributeVo.getCompensateDistributeId())){
                            com.wanshifu.master.order.push.domain.rqt.compensateDistribute.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.compensateDistribute.DetailRqt();
                            detailRqt.setDistributeId(compensateDistributeVo.getCompensateDistributeId());
                            CompensateDistribute compensateDistribute = compensateDistributeApi.detail(detailRqt);
                            compensateDistributeVo.setCompensateDistributeName(compensateDistribute.getStrategyName());
                        }

                        if(Objects.nonNull(compensateDistributeVo.getOrderRoutingStrategyId())){
                            com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.DetailRqt();
                            detailRqt.setStrategyId(compensateDistributeVo.getOrderRoutingStrategyId());
                            OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyApi.detail(detailRqt);
                            compensateDistributeVo.setOrderRoutingStrategyName(orderRoutingStrategy.getStrategyName());
                        }
                    });

                }
            }
        }
        return detailResp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {

        SimplePageInfo<NormalOrderDistributeStrategy> simplePageInfo = normalOrderDistributeStrategyApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<NormalOrderDistributeStrategy> normalOrderDistributeStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = normalOrderDistributeStrategyList.stream().map(NormalOrderDistributeStrategy::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(",")))
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        //城市地址
        String cityIdsStr = normalOrderDistributeStrategyList.stream().map(NormalOrderDistributeStrategy::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        List<Address> divisionInfoListByDivisionIds = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        List<Long> updateAccountIds = normalOrderDistributeStrategyList.stream().map(NormalOrderDistributeStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(normalOrderDistributeStrategyList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(","))
                    .map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it))
                    .collect(Collectors.joining(",")));

            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

}
