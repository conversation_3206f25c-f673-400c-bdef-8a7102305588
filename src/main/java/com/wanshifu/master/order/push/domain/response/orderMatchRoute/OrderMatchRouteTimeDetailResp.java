package com.wanshifu.master.order.push.domain.response.orderMatchRoute;

import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class OrderMatchRouteTimeDetailResp {

    private Integer businessLineId;

    private String categoryIds;

    private String appointTypes;

    private String settingType;

    private Integer settingTime;

    private Integer settingNum;

    private Long createAccountId;



}
