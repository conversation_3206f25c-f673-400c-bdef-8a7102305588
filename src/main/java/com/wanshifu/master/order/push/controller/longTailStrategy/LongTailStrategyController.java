package com.wanshifu.master.order.push.controller.longTailStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.longTailStrategy.*;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;
import com.wanshifu.master.order.push.service.longTailStrategy.LongTailStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 描述 :  策略组合.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@RestController
@RequestMapping("/longTailStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyController {

    private final LongTailStrategyService longTailStrategyService;

    /**
     * 策略管理-获取列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return longTailStrategyService.list(rqt);
    }

    /**
     *  策略管理-创建长尾单策�?
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return longTailStrategyService.create(rqt);
    }

    /**
     * 策略管理-修改长尾单策�?
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return longTailStrategyService.update(rqt);
    }

    /**
     *  策略管理-更新策略状态（启用/禁用�?
     * @param rqt
     * @return
     */
    @PostMapping("/updateStatus")
    public int updateStatus(@RequestBody @Valid EnableRqt rqt) {
        return longTailStrategyService.updateStatus(rqt);
    }

    /**
     *  策略管理-删除长尾单策�?
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return longTailStrategyService.delete(rqt);
    }

    /**
     * 策略管理-策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return longTailStrategyService.detail(rqt);
    }
}
