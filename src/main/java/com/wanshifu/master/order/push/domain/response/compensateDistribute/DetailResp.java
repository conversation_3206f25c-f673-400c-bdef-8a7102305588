package com.wanshifu.master.order.push.domain.response.compensateDistribute;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import java.util.Date;

@Data
public class DetailResp {

    private Integer distributeId;

    private Integer businessLineId;

    private String strategyName;

    private String strategyDesc;

    private String categoryIds;

    private String appointTypes;

    private String orderPushFlag;

    private Integer hasPrice;

    private Integer hasCooperationUser;

    private String compensateType;

    private Integer intervalTime;

    private Integer triggerNum;


}
