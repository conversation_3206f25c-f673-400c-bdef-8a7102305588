package com.wanshifu.master.order.push.service.orderPushMonitor.impl;

import com.alibaba.fastjson.JSON;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.OrderDynamicRoundsPushApi;
import com.wanshifu.master.order.push.domain.enums.AppointTypeEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFromTypeEnum;
import com.wanshifu.master.order.push.domain.enums.ServeTypeEnum;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.OrderDynamicRoundsPushListResp;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;

import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.orderPushMonitor.OrderDynamicRoundsPushService;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class OrderDynamicRoundsPushServiceImpl implements OrderDynamicRoundsPushService {

    @Resource
    private OrderDynamicRoundsPushApi orderDynamicRoundsPushApi;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private ServeCommonService serveCommonService;

    @Override
    public SimplePageInfo<OrderDynamicRoundsPushListResp> list(ListRqt rqt){

        SimplePageInfo<OrderDynamicRoundsPush> simplePageInfo = orderDynamicRoundsPushApi.list(rqt);
        List<OrderDynamicRoundsPush> orderDynamicRoundsPushList = simplePageInfo.getList();

        List<OrderDynamicRoundsPushListResp> respList = null;

        if(CollectionUtils.isNotEmpty(orderDynamicRoundsPushList)){
            respList = this.buildOrderDynamicRoundsPushResp(orderDynamicRoundsPushList);
        }

        SimplePageInfo<OrderDynamicRoundsPushListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(respList);
        listRespSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        listRespSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        listRespSimplePageInfo.setTotal(simplePageInfo.getTotal());
        listRespSimplePageInfo.setPages(simplePageInfo.getPages());
        return listRespSimplePageInfo;

    }


    private List<OrderDynamicRoundsPushListResp> buildOrderDynamicRoundsPushResp(List<OrderDynamicRoundsPush> orderDynamicRoundsPushList){


        Set<Long> secondDivisionIdList = orderDynamicRoundsPushList.stream().map(OrderDynamicRoundsPush::getSecondDivisionId).collect(Collectors.toSet());
        List<Address> cityAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(secondDivisionIdList,","));
        Map<Long, String> cityDivisionNameMap = cityAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));


        Set<Long> thirdDivisionIdList = orderDynamicRoundsPushList.stream().map(OrderDynamicRoundsPush::getThirdDivisionId).collect(Collectors.toSet());
        List<Address> districtAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(thirdDivisionIdList,","));
        Map<Long, String> districtDivisionNameMap = districtAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));

        Set<Long> fourthDivisionIdList = orderDynamicRoundsPushList.stream().map(OrderDynamicRoundsPush::getFourthDivisionId).collect(Collectors.toSet());
        fourthDivisionIdList.remove(0L);
        fourthDivisionIdList.remove(null);
        Map<Long, String>  tempStreetDivisionNameMap = null;
        if(CollectionUtils.isNotEmpty(fourthDivisionIdList)){
            List<Address> streetAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(fourthDivisionIdList,","));
            tempStreetDivisionNameMap = streetAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));
        }

        List<Long> categoryIdSet = orderDynamicRoundsPushList.stream().map(OrderDynamicRoundsPush::getCategoryId).distinct().collect(Collectors.toList());


        List<Goods> goodsList = goodsCommonService.queryBatch(categoryIdSet);

        Map<Long, Goods> goodsMap = goodsList.stream()
                .filter(goods -> goods.getGoodsId() != null)
                .collect(Collectors.toMap(Goods::getGoodsId, Function.identity()));


        Map<Long,String> streetDivisionNameMap = tempStreetDivisionNameMap;


        Map<Integer,String> businessLineMap = new HashMap<>();

        businessLineMap.put(1,"成品业务线");
        businessLineMap.put(2,"家庭业务线");
        businessLineMap.put(3,"创新业务线");



        List<OrderDynamicRoundsPushListResp> respList = new ArrayList<>();
        orderDynamicRoundsPushList.forEach(orderDynamicRoundsPush -> {
            try{
                OrderDynamicRoundsPushListResp resp = new OrderDynamicRoundsPushListResp();
                resp.setId(orderDynamicRoundsPush.getId());
                resp.setPushTime(orderDynamicRoundsPush.getPushTime());
//                resp.setBusinessLine(businessLineMap.get(orderDynamicRoundsPush.getBusinessLineId()));
                resp.setOrderNo(orderDynamicRoundsPush.getOrderNo());
                resp.setOrderFrom(OrderFromTypeEnum.asValue(orderDynamicRoundsPush.getOrderFromType()).name);
                resp.setCity(cityDivisionNameMap.get(orderDynamicRoundsPush.getSecondDivisionId()));
                resp.setDistrict(districtDivisionNameMap.get(orderDynamicRoundsPush.getThirdDivisionId()));
                resp.setStreet((orderDynamicRoundsPush.getFourthDivisionId() != null && orderDynamicRoundsPush.getFourthDivisionId() > 0 ) ? streetDivisionNameMap.get(orderDynamicRoundsPush.getFourthDivisionId()) : "");
                resp.setServeType(ServeTypeEnum.getDesc(orderDynamicRoundsPush.getServeTypeId()));
                resp.setServeCategory(goodsMap.containsKey(orderDynamicRoundsPush.getCategoryId()) ? goodsMap.get(orderDynamicRoundsPush.getCategoryId()).getGoodsName() : "");
                resp.setAppointType(AppointTypeEnum.asValue(orderDynamicRoundsPush.getAppointType()).name);
                resp.setPushMasterNum(orderDynamicRoundsPush.getPushMasterNum());
                resp.setPushMasterDetailList(StringUtils.isNotBlank(orderDynamicRoundsPush.getPushMasterDetailList()) ? JSON.parseArray(orderDynamicRoundsPush.getPushMasterDetailList()) : null);
                respList.add(resp);
            }catch(Exception e){
                e.printStackTrace();
            }

        });

        return respList;
    }


    private void parseParams(ListRqt rqt){

        Map<Integer,String> businessLineMap = new HashMap<>();

        businessLineMap.put(1,"成品业务线");
        businessLineMap.put(2,"家庭业务线");
        businessLineMap.put(3,"创新业务线");

        if(StringUtils.isNotBlank(rqt.getOrderFrom())){
            rqt.setOrderFromList(
                    Arrays.stream(Optional.ofNullable(rqt.getOrderFrom())
                            .orElse("0").split(",")).map(String::valueOf)
                            .collect(Collectors.toList()));
        }


        if(StringUtils.isNotBlank(rqt.getAppointType())){
            rqt.setAppointTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getAppointType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getServeTypeId())){
            rqt.setServeTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getServeTypeId())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getCategoryId())){
            rqt.setCategoryList(
                    Arrays.stream(Optional.ofNullable(rqt.getCategoryId())
                            .orElse("0").split(",")).map(Long::parseLong)
                            .collect(Collectors.toList()));
        }

    }

//    @Override
//    public Integer exportMasterLackList(ListRqt rqt, HttpServletResponse httpServletResponse){
//
//            try{
//
//                parseParams(rqt);
//
//                List<MasterLackListExcelVo> voList = new ArrayList<>();
//
//                int pageNum = 1;
//                int pageSize = rqt.getPageSize();
//                rqt.setPageSize(pageSize);
//
//                while(true){
//                    rqt.setPageNum(pageNum);
//                    SimplePageInfo<OrderDynamicRoundsPush> simplePageInfo = orderDynamicRoundsPushApi.list(rqt);
//                    if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){
//                        List<OrderDynamicRoundsPushListResp> respList = buildOrderDynamicRoundsPushResp(simplePageInfo.getList());
//                        respList.forEach(resp -> {
//                            MasterLackListExcelVo excelVo = new MasterLackListExcelVo();
//                            BeanUtils.copyProperties(resp,excelVo);
//                            voList.add(excelVo);
//                        });
//                        pageNum++;
//                    }else{
//                        break;
//                    }
//
//                    if(simplePageInfo.getPageNum() >= simplePageInfo.getPages()){
//                        break;
//                    }
//                }
//
//                ExportParams exportParams = new ExportParams();
//                exportParams.setTitle("运力不足明细");
//                exportParams.setSheetName("运力不足明细");
//
//                Workbook workbook;
//                ExcelExportServer excelExportServer = new ExcelExportServer();
//                if (ExcelType.HSSF.equals(exportParams.getType())) {
//                    workbook = new HSSFWorkbook();
//                } else if (voList.size() < 1000) {
//                    workbook = new XSSFWorkbook();
//                } else {
//                    workbook = new SXSSFWorkbook();
//                }
//                excelExportServer.createSheet(workbook, exportParams, MasterLackListExcelVo.class, voList);
//
//                String fileName = "运力不足明细";
//                httpServletResponse.setHeader("Content-Disposition", "attachment;Filename=" + fileName + System.currentTimeMillis() + ".xls");
//                ServletOutputStream outputStream = httpServletResponse.getOutputStream();
//                workbook.write(outputStream);
//                outputStream.close();
//                return 1;
//            }catch(Exception e){
//                e.printStackTrace();
//            }
//
//            return 0;
//        }


    @Override
       public Integer exportMasterLackList(ListRqt rqt, HttpServletResponse httpServletResponse){

                parseParams(rqt);

                List<List<Object>> respList = new ArrayList<>();

        Map<Integer,String> businessLineMap = new HashMap<>();

        businessLineMap.put(1,"成品业务线");
        businessLineMap.put(2,"家庭业务线");
        businessLineMap.put(3,"创新业务线");

                int pageNum = 1;
                int pageSize = rqt.getPageSize();
                rqt.setPageSize(pageSize);
                List<OrderDynamicRoundsPushListResp.PushMasterDetail> pushMasterDetailList = new ArrayList<>();

                while(true){
                    rqt.setPageNum(pageNum);
                    SimplePageInfo<OrderDynamicRoundsPush> simplePageInfo = orderDynamicRoundsPushApi.list(rqt);
                    if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){

                        Set<Long> serveIdSet = new HashSet<>();
                        simplePageInfo.getList().forEach(orderDynamicRoundsPush -> {
                            serveIdSet.addAll(Arrays.asList(orderDynamicRoundsPush.getServeLevel1Ids().split(",")).stream().map(Long::valueOf).collect(Collectors.toSet()));
                        });

                        List<ServeBaseInfoResp> serveBaseInfoRespList = serveCommonService.getServeBaseInfoByServeIdSet(serveIdSet);

                        Map<Long, ServeBaseInfoResp> serveBaseInfoRespMap = serveBaseInfoRespList.stream()
                                .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, serve -> serve));
                        

                        for(OrderDynamicRoundsPush orderDynamicRoundsPush : simplePageInfo.getList()){
                            List<Object> objectList = new ArrayList<>();
                            objectList.add(DateUtils.formatDateTime(orderDynamicRoundsPush.getPushTime()));
                            objectList.add(businessLineMap.get(orderDynamicRoundsPush.getBusinessLineId()));
                            Long serveId = Long.valueOf(orderDynamicRoundsPush.getServeLevel1Ids());
                            objectList.add(orderDynamicRoundsPush.getOrderNo());
                            objectList.add(serveBaseInfoRespMap.containsKey(serveId) ? serveBaseInfoRespMap.get(serveId).getName() : "");
                            objectList.add(orderDynamicRoundsPush.getCustomerAddress());
                            objectList.add(OrderFromTypeEnum.asValue(orderDynamicRoundsPush.getOrderFromType()).name);
                            objectList.add(AppointTypeEnum.asValue(orderDynamicRoundsPush.getAppointType()).name);
                            objectList.add(orderDynamicRoundsPush.getPushMasterNum());
                            pushMasterDetailList = JSON.parseArray(orderDynamicRoundsPush.getPushMasterDetailList(), OrderDynamicRoundsPushListResp.PushMasterDetail.class);
                            if(CollectionUtils.isNotEmpty(pushMasterDetailList)){
                                pushMasterDetailList.forEach(pushMasterDetail -> {
                                    objectList.add(pushMasterDetail.getRoundsPushNum());
                                    objectList.add(pushMasterDetail.getRoundsOfferRate());
                                });
                            }
                            respList.add(objectList);
                        }
                        pageNum++;
                    }else{
                        break;
                    }

                    if(simplePageInfo.getPageNum() >= simplePageInfo.getPages()){
                        break;
                    }
                }
       /* ExcelUtilXlsx excelUtilXlsx = new ExcelUtilXlsx();
                List<String> titleList = new ArrayList<>();
                        titleList.add("日期");
                        titleList.add("业务线");
                        titleList.add("订单编号");
                        titleList.add("订单服务");
        titleList.add("订单上门地址");
        titleList.add("订单来源");
        titleList.add("下单模式");
        titleList.add("总推送人数");

        pushMasterDetailList.forEach(pushMasterDetail -> {
            titleList.add(pushMasterDetail.getRoundsName() + "人数");
            titleList.add(pushMasterDetail.getRoundsName() + "报价率");
        });*/


//        excelUtilXlsx.exportExcel(httpServletResponse,"运力不足明细",titleList,respList);
        //  TODO: 2020/9/23 暂不支持大数据量同步导出，需要做异步导出！
        //由于之前做的同步导出使用的poi包是11年前的，现在做异步导出easyExcel的包也需要依赖poi包，且需要高版本的包，高版本的poi包对低版本是不兼容的，导致现在的运力不足明细的导出有问题，
        throw new BusException("暂不支持大数据量同步导出，需要做异步导出！");

       }



}
