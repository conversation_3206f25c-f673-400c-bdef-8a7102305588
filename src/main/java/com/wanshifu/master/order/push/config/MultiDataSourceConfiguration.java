package com.wanshifu.master.order.push.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.google.common.collect.Maps;
import com.wanshifu.framework.persistence.autoconfigure.properties.DataSourceProperties;
import com.wanshifu.framework.persistence.web.filter.DruidStatFilter;
import com.wanshifu.framework.persistence.web.servlet.DruidStatViewServlet;
import com.wanshifu.framework.utils.TransmittableContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.shardingjdbc.api.yaml.YamlShardingDataSourceFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.aop.support.Pointcuts;
import org.springframework.aop.support.annotation.AnnotationMatchingPointcut;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 11:41
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({DataSourceProperties.class,
        PushDataSourceProperties.class})
@ConditionalOnProperty(prefix = "wanshifu.multi.dataSource", name = "enable")
public class MultiDataSourceConfiguration {

    @Resource
    private DataSourceProperties dataSourceProperties;

    @Resource
    private  PushDataSourceProperties pushDataSourceProperties;

    /**
     * 默认数据源order_push_service
     * @return DataSource
     * @throws Exception
     */
    @Bean("defaultPushDataSource")
    public DataSource defaultPushDataSource() throws Exception {
        String url = pushDataSourceProperties.getUrl();
        String username = pushDataSourceProperties.getUsername();
        String password = pushDataSourceProperties.getPassword();
        try {

            DataSource datasource = initDataSource(TransmittableContext.DEFAULT_DATASOURCE, url, username, password);
            if(datasource != null){
                Map<String, DataSource> dataSourceMap = Maps.newHashMap();
                dataSourceMap.put("ds_order_push_service", datasource);
                DataSource dataSource = YamlShardingDataSourceFactory.createDataSource(dataSourceMap, getFile("/config/config-sharding.yaml"));
                log.info("create sharding dataSource : {}", dataSource);
                return dataSource;
            }
            return null;
        }catch (SQLException | IOException e) {
            log.error("create sharding dataSource error:", e);
            throw e;
        }
    }

    @Bean
    @ConditionalOnWebApplication
    @ConditionalOnProperty(prefix = DataSourceProperties.PREFIX, name = "servlet.enable", matchIfMissing = true)
    public ServletRegistrationBean druidStatViewServlet() {
        ServletRegistrationBean registration = new ServletRegistrationBean<>(new DruidStatViewServlet());
        registration.addUrlMappings(dataSourceProperties.getServlet().getUrlPatterns());
        registration.addInitParameter("allow", dataSourceProperties.getServlet().getAllow());
        registration.addInitParameter("deny", dataSourceProperties.getServlet().getDeny());
        registration.addInitParameter("loginUsername", dataSourceProperties.getServlet().getLoginUsername());
        registration.addInitParameter("loginPassword", dataSourceProperties.getServlet().getLoginPassword());
        registration.addInitParameter("resetEnable", dataSourceProperties.getServlet().isResetEnable() + "");
        registration.addInitParameter("sessionStatEnable", dataSourceProperties.getServlet().isSessionStatEnable() + "");
        registration.addInitParameter("isNeedLogin", dataSourceProperties.getServlet().isNeedLogin() + "");
        log.info("【wanshifu spring boot component】 'DruidDataSource servlet' init successful!");
        return registration;
    }


    @Bean
    @ConditionalOnWebApplication
    @ConditionalOnProperty(prefix = DataSourceProperties.PREFIX, name = "filter.enable", matchIfMissing = true)
    public FilterRegistrationBean druidStatFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setName(dataSourceProperties.getFilter().getFilterName());
        filterRegistrationBean.setUrlPatterns(Collections.singletonList(dataSourceProperties.getFilter().getUrlPatterns()));
        filterRegistrationBean.addInitParameter("exclusions", dataSourceProperties.getFilter().getExclusions());
        filterRegistrationBean.addInitParameter("sessionStatEnable", dataSourceProperties.getServlet().isSessionStatEnable() + "");
        filterRegistrationBean.setFilter(new DruidStatFilter());
        log.info("【wanshifu spring boot component】 'DruidDataSource filter' init successful!");
        return filterRegistrationBean;
    }


    /**
     * 动态数据源
     *
     * @return
     * @throws Exception
     */
    @Bean(name = "dynamicDataSource")
    public DataSource dynamicDataSource() throws Exception {
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put(DataSourceKey.DEFAULT_PUSH_DATASOURCE.value, defaultPushDataSource());
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        dynamicDataSource.setDefaultTargetDataSource(defaultPushDataSource());
        return dynamicDataSource;
    }


    /**
     * 数据源切面
     *
     * @return
     */
    @Bean
    public DefaultPointcutAdvisor dataSourcePointcutAdvisor() {
        DataSourceInterceptor interceptor = new DataSourceInterceptor();
        //@DataSource注解切面（类或方法）
        Pointcut pointcut = Pointcuts.union(new AnnotationMatchingPointcut(com.wanshifu.framework.persistence.annotation.DataSource.class),
                new AnnotationMatchingPointcut(null, com.wanshifu.framework.persistence.annotation.DataSource.class));
        // 配置增强类advisor
        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
        advisor.setPointcut(pointcut);
        advisor.setAdvice(interceptor);
        advisor.setOrder(-1);
        return advisor;
    }


    /**
     * 清除数据源切面
     *
     * @return
     */
    @Bean
    public DefaultPointcutAdvisor clearDataSourceKeyPointcutAdvisor() {
        ClearDataSourceKeyInterceptor interceptor = new ClearDataSourceKeyInterceptor();
        //@RestController,@Controller注解切面
        Pointcut controllerPointcut = Pointcuts.union(new AnnotationMatchingPointcut(Controller.class),
                new AnnotationMatchingPointcut(RestController.class));
        //@Async注解切面（类或方法）
        Pointcut asyncPointcut = Pointcuts.union(new AnnotationMatchingPointcut(Async.class),
                new AnnotationMatchingPointcut(null, Async.class));
        Pointcut pointcut = Pointcuts.union(controllerPointcut, asyncPointcut);
        // 配置增强类advisor
        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
        advisor.setPointcut(pointcut);
        advisor.setAdvice(interceptor);
        advisor.setOrder(-2);
        return advisor;
    }


    @Bean
    @Primary
    public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("dynamicDataSource") DataSource dynamicDataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        // 配置数据源，此处配置为关键配置，如果没有将 dynamicDataSource作为数据源则不能实现切换
        sessionFactory.setDataSource(dynamicDataSource);
        // 扫描映射文件
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mappers/*.xml"));
        sessionFactory.setConfiguration(new org.apache.ibatis.session.Configuration());
        sessionFactory.setTypeAliasesPackage("com.wanshifu.master.order.push.domain.po");
        return sessionFactory;
    }

    @Bean
    public PlatformTransactionManager transactionManager() throws Exception {
        // 配置事务管理, 使用事务时在方法头部添加@Transactional注解即可
        return new DataSourceTransactionManager(dynamicDataSource());
    }

    public DataSource initDataSource(String dataBaseNo, String url, String username, String password) {
        DruidDataSource datasource = new DruidDataSource();
        try {
            datasource.setUrl(url);
            datasource.setUsername(username);
            datasource.setPassword(password);
            datasource.setDriverClassName(dataSourceProperties.getDriverClassName());
            //configuration
            datasource.setInitialSize(pushDataSourceProperties.getInitialSize());
            datasource.setMinIdle(pushDataSourceProperties.getMinIdle());
            datasource.setMaxActive(pushDataSourceProperties.getMaxActive());
            datasource.setMaxWait(dataSourceProperties.getMaxWait());
            datasource.setTimeBetweenEvictionRunsMillis(dataSourceProperties.getTimeBetweenEvictionRunsMillis());
            datasource.setMinEvictableIdleTimeMillis(dataSourceProperties.getMinEvictableIdleTimeMillis());
            datasource.setValidationQuery(dataSourceProperties.getValidationQuery());
            datasource.setTestWhileIdle(pushDataSourceProperties.isTestWhileIdle());
            datasource.setTestOnBorrow(pushDataSourceProperties.isTestOnBorrow());
            datasource.setTestOnReturn(pushDataSourceProperties.isTestOnReturn());
            datasource.setPoolPreparedStatements(dataSourceProperties.isPoolPreparedStatements());
            datasource.setMaxPoolPreparedStatementPerConnectionSize(dataSourceProperties.getMaxPoolPreparedStatementPerConnectionSize());
            datasource.setFilters(dataSourceProperties.getFilters());
            datasource.setConnectionProperties(dataSourceProperties.getConnectionProperties());
            if (pushDataSourceProperties.getConnectionInitSqls() != null) {
                datasource.setConnectionInitSqls(Collections.singletonList(pushDataSourceProperties.getConnectionInitSqls()));
            }
            datasource.init();
        } catch (Exception e) {
            log.error(String.format("数据源链接异常，databaseNo=【%s】，dsUrl=【%s】", dataBaseNo, url), e);
            throw new IllegalStateException("[" + dataBaseNo + "]" + "：数据源连接不上，可能是连接参数有误！");
        }
        log.warn(String.format("数据源初始化成功，databaseNo=【%s】，dsUrl=【%s】", dataBaseNo, url));
        return datasource;
    }

    private static File getFile(final String fileName) throws IOException {
        org.springframework.core.io.Resource resource =  new ClassPathResource(fileName);
        return resource.getFile();
    }

}
