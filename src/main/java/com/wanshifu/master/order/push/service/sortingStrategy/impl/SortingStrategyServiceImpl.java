package com.wanshifu.master.order.push.service.sortingStrategy.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.SortingStrategyApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.ItemTypeEnum;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.GetGroupByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.sortingStrategy.DetailV2Resp;
import com.wanshifu.master.order.push.domain.response.sortingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.common.StrategyRuleExpressionService;
import com.wanshifu.master.order.push.service.sortingStrategy.SortingStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 描述 :  精排策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-09 17:46
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SortingStrategyServiceImpl implements SortingStrategyService {

    private final GoodsCommonService goodsCommonApi;
//    private final AuthHandler authHandler;
    private final IopAccountApi iopAccountApi;
    private final MasterBigDataOpenApi masterBigDataOpenApi;
    private final SortingStrategyApi sortingStrategyApi;

    private final ServeCommonService serveCommonService;




    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return sortingStrategyApi.enable(rqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {

        String categoryIds = rqt.getCategoryIds();

        SimplePageInfo<SortingStrategy> strategySimplePageInfo = sortingStrategyApi.list(rqt);

        List<SortingStrategy> sortingStrategies = strategySimplePageInfo.getList();
        //类目名称
        List<Long> goodsIds = sortingStrategies.stream().map(SortingStrategy::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommonApi.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        List<Long> updateAccountIds = sortingStrategies.stream().map(SortingStrategy::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(sortingStrategies, ListResp.class, (s, t) -> {
            //规则条数
            t.setItemNum(JSON.parseArray(s.getSortingRule()).size());
            if (StringUtils.equals(s.getCategoryIds(), "all")) {
                t.setCategoryName("全部(不限类目)");
            } else {
                t.setCategoryName(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });
        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(strategySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(strategySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(strategySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(strategySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(DeleteRqt rqt) {
        return sortingStrategyApi.delete(rqt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createV2(CreateV2Rqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return sortingStrategyApi.createV2(rqt);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateV2(UpdateV2Rqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return sortingStrategyApi.updateV2(rqt);
    }



    @Override
    public DetailV2Resp detailV2(DetailRqt rqt) {
        SortingStrategy sortingStrategy = sortingStrategyApi.detailV2(rqt);
        DetailV2Resp detailResp = new DetailV2Resp();
        BeanCopyUtil.copyProperties(sortingStrategy, detailResp);
        List<SortRule> sortRuleList = JSON.parseArray(sortingStrategy.getSortingRule(),SortRule.class);


        Set<Long> serveIds = sortRuleList.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;
        sortRuleList.forEach(sortRule -> Optional.ofNullable(sortRule.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

            List<List<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));

        List<RuleItem> ruleItemList = new ArrayList<>();
        sortRuleList.forEach(sortRule -> ruleItemList.addAll(sortRule.getItemList()));
        ruleItemList.forEach(ruleItem -> {
            if(StringUtils.isBlank(ruleItem.getItemType())){
                ruleItem.setItemType(ItemTypeEnum.MASTER_QUOTA.getCode());
            }
        });

        detailResp.setRuleList(sortRuleList);

        List<RuleItem> masterGroupItemList = ruleItemList.stream().filter(ruleItem -> ruleItem.getItemType().equals(ItemTypeEnum.MASTER_GROUP.getCode())).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(masterGroupItemList)){
            List<RuleItem.ScoreItem> scoreItemList = new ArrayList<>();
            masterGroupItemList.forEach(ruleItem -> scoreItemList.addAll(ruleItem.getScoreList()));
            List<Long> groupIdList =  scoreItemList.stream().map(RuleItem.ScoreItem::getValue).map(Long::parseLong).collect(Collectors.toList());
            detailResp.setMasterGroupList(Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(groupIdList, 2)))
                    .map(BigdataGetAllGroupListForPageResp::getData)
                    .orElse(Collections.emptyList()).stream().
                    map(it -> new GetGroupByGroupIdsResp(it.getGroupId(), it.getGroupName())).collect(Collectors.toList()));
        }

        return detailResp;
    }
}