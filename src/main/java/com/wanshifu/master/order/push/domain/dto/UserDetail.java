//package com.wanshifu.master.order.push.domain.dto;
//
//import org.springframework.security.core.GrantedAuthority;
//import org.springframework.security.core.userdetails.User;
//
//import java.util.Collection;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> xinze<PERSON>@wshifu.com
// * @date : 2023-02-25 18:18
// */
//public class UserDetail extends User {
//    private Long userId;
//
//    public UserDetail(String username, String password, Collection<? extends GrantedAuthority> authorities, Long userId) {
//        super(username, password, authorities);
//        this.userId = userId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//}