package com.wanshifu.master.order.push.domain.po;

import jakarta.persistence.*;
import java.util.Date;


import lombok.Data;
import lombok.ToString;


/**
 * 
 */
@Data
@ToString
@Table(name = "simulate_push")
public class SimulatePush {

    /**
     * 测算推id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "simulate_push_id")
    private Long simulatePushId;

    @Column(name = "simulate_id")
    private Long simulateId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 策略名称集合
     */
    @Column(name = "strategy_list_name")
    private String strategyListName;

    /**
     * 是否启用备用路由
     */
    @Column(name = "is_alternate_strategy")
    private Integer isAlternateStrategy;

    /**
     * 推送师傅数
     */
    @Column(name = "push_master_num")
    private Integer pushMasterNum;

    /**
     * 推送轮数
     */
    @Column(name = "push_rounds")
    private Integer pushRounds;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}