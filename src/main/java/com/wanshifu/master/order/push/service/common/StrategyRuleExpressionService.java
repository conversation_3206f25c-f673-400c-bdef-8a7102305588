package com.wanshifu.master.order.push.service.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.ItemTypeEnum;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.rqt.common.GetScoreItemByCodesRqt;
import com.wanshifu.master.order.push.domain.vo.common.ExposureRuleItem;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.QlExpressUtil;
import com.wanshifu.master.order.sort.domains.api.request.common.GetOrderSortItemRqt;
import com.wanshifu.master.order.sort.service.api.CommonServiceApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-15 10:24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StrategyRuleExpressionService {

    @Lazy
    private final CommonServiceApi commonServiceApi;

    private CommonApi commonApi;

    public List<ScoreRuleExpressionDto> getRuleExpressions(List<RuleItem> itemList, String strategyType) {
        List<String> itemNames = itemList.stream().map(RuleItem::getItemName).collect(Collectors.toList());

        Map<String, String> scoreItemFeatureExpressionMap;
        if (StringUtils.equals(strategyType, "orderSort")) {
            scoreItemFeatureExpressionMap = commonServiceApi.getOrderSortItem(new GetOrderSortItemRqt(itemNames))
                    .stream().collect(Collectors.toMap(com.wanshifu.master.order.sort.domain.po.ScoreItem::getItemCode,
                            com.wanshifu.master.order.sort.domain.po.ScoreItem::getFeatureExpression));
        } else {
            //Map<item_code,feature_expression>
            GetScoreItemByCodesRqt getScoreItemByCodesRqt = new GetScoreItemByCodesRqt();
            getScoreItemByCodesRqt.setItemCodes(itemNames);
            scoreItemFeatureExpressionMap = commonApi.getScoreItemByCodes(getScoreItemByCodesRqt)
                    .stream().collect(Collectors.toMap(ScoreItem::getItemCode, ScoreItem::getFeatureExpression));
        }

        return itemList.stream().map(item -> {
            //部分匹配项隐藏的开启条件 例如：{"openConditionfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"},"filterfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"}}
            String featureExpressionJson = scoreItemFeatureExpressionMap.getOrDefault(item.getItemName(), "");
            //开启条件中的参数名称
            String openConditionRuleExpression = "";
            String openConditionRuleParams = "";
            String filterFeatureRuleExpressionParams = "";
            String filterFeatureRuleExpression = "";
            if (StringUtils.isNotBlank(featureExpressionJson)) {
                ScoreItemFeatureExpressionDto scoreItemFeatureExpressionDto = JSON.parseObject(featureExpressionJson, ScoreItemFeatureExpressionDto.class);
                openConditionRuleParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                openConditionRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");

                filterFeatureRuleExpressionParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                filterFeatureRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");
            }
            if ("orderSort".equals(strategyType) && "is_special_category".equals(item.getItemName())) {

                StringBuilder categoryIdsBuilder = new StringBuilder();

                if (CollectionUtil.isNotEmpty(item.getOneCategoryIds())) {
                    String oneCategoryIds = StrUtil.join(",", item.getOneCategoryIds());
                    categoryIdsBuilder.append(oneCategoryIds);
                }

                if (CollectionUtil.isNotEmpty(item.getTwoCategoryIds())) {
                    String twoCategoryIds = StrUtil.join(",", item.getTwoCategoryIds());
                    if (categoryIdsBuilder.length() > 0) {
                        categoryIdsBuilder.append(",");
                    }
                    categoryIdsBuilder.append(twoCategoryIds);
                }

                openConditionRuleExpression = "category_id in (".concat(categoryIdsBuilder.toString()).concat(")");
                openConditionRuleParams = "category_id";
            }

            List<RuleItem.ScoreItem> scoreList = item.getScoreList();
            List<IfExpressDto> ifExpressDtos = scoreList.stream().map(scoreItem -> {
                String returnRuleExpression = QlExpressUtil.transitionQlExpress(scoreItem.getScore().toPlainString(), "*", item.getWeight().toPlainString(), null);
                String ifRuleExpression;
                if (StringUtils.equals(item.getAssignMode(), "range_value")) {
                    //区间 例: order_master_distance >= 12 and order_master_distance <100.30
                    ifRuleExpression = QlExpressUtil.transitionQlExpress("and",
                            Lists.newArrayList(
                                    new QlExpressDto(item.getItemName(), ">=", scoreItem.getStartValue().toPlainString()),
                                    new QlExpressDto(item.getItemName(), "<", scoreItem.getEndValue().toPlainString())
                            ));
                } else {
                    //枚举 例: is_like_low_price_offer == 'A'
                    if(ItemTypeEnum.MASTER_GROUP.getCode().equals(item.getItemType())){
                        ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "contain", scoreItem.getValue(),null);
                    }else{

                        if ("orderSort".equals(strategyType) && "is_special_category".equals(item.getItemName())) {
                            ifRuleExpression = QlExpressUtil.transitionQlExpress("1", "=", scoreItem.getValue(),null);
                        } else {
                            ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "=", scoreItem.getValue(),null);
                        }
                    }
                }
                return new IfExpressDto(ifRuleExpression, returnRuleExpression);
            }).collect(Collectors.toList());
            //计算匹配项分值的表达式 例如：if(a+b==12) then {return 7*0.6;}   else if(a+b==11)then{return 2;}    else {return 0;}
            String filterRuleExpression = QlExpressUtil.transitionQlExpress(ifExpressDtos, "0");
            //评分项隐藏的条件 不满足该项隐藏条件时，该项评分得0
            if (StringUtils.isNotBlank(filterFeatureRuleExpression)) {
                filterRuleExpression = StrUtil.format("if(!({})) return 0; ", filterFeatureRuleExpression) + filterRuleExpression;
            }

            String filterRuleParams = item.getItemName();
            if (StringUtils.isNotBlank(filterFeatureRuleExpressionParams)) {
                filterRuleParams = StrUtil.format("{},{}", filterFeatureRuleExpressionParams, filterRuleParams);
            }
            return new ScoreRuleExpressionDto(item.getItemTitle(), item.getItemName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }

    public List<ScoreRuleExpressionDto> getExposureRuleExpressions(List<ExposureRuleItem> itemList) {
        List<String> itemNames = itemList.stream().map(ExposureRuleItem::getItemName).collect(Collectors.toList());

        Map<String, String> scoreItemFeatureExpressionMap = commonServiceApi.getOrderSortItem(new GetOrderSortItemRqt(itemNames))
                .stream().collect(Collectors.toMap(com.wanshifu.master.order.sort.domain.po.ScoreItem::getItemCode,
                        com.wanshifu.master.order.sort.domain.po.ScoreItem::getFeatureExpression));

        return itemList.stream().map(item -> {
            //部分匹配项隐藏的开启条件 例如：{"openConditionfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"},"filterfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"}}
            String featureExpressionJson = scoreItemFeatureExpressionMap.getOrDefault(item.getItemName(), "");
            //开启条件中的参数名称
            String openConditionRuleExpression = "";
            String openConditionRuleParams = "";
            String filterFeatureRuleExpressionParams = "";
            String filterFeatureRuleExpression = "";
            if (StringUtils.isNotBlank(featureExpressionJson)) {
                ScoreItemFeatureExpressionDto scoreItemFeatureExpressionDto = JSON.parseObject(featureExpressionJson, ScoreItemFeatureExpressionDto.class);
                openConditionRuleParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                openConditionRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");

                filterFeatureRuleExpressionParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                filterFeatureRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");
            }
            if ("is_special_category".equals(item.getItemName())) {

                StringBuilder categoryIdsBuilder = new StringBuilder();

                if (CollectionUtil.isNotEmpty(item.getOneCategoryIds())) {
                    String oneCategoryIds = StrUtil.join(",", item.getOneCategoryIds());
                    categoryIdsBuilder.append(oneCategoryIds);
                }

                if (CollectionUtil.isNotEmpty(item.getTwoCategoryIds())) {
                    String twoCategoryIds = StrUtil.join(",", item.getTwoCategoryIds());
                    if (categoryIdsBuilder.length() > 0) {
                        categoryIdsBuilder.append(",");
                    }
                    categoryIdsBuilder.append(twoCategoryIds);
                }

                openConditionRuleExpression = "category_id in (".concat(categoryIdsBuilder.toString()).concat(")");
                openConditionRuleParams = "category_id";
            }

            List<ExposureRuleItem.ScoreItem> scoreList = item.getScoreList();
            List<IfExpressDto> ifExpressDtos = scoreList.stream().map(scoreItem -> {
                String returnRuleExpression = "";
                if (item.getScoreAddType() == 1) {
                    //乘系数
                    returnRuleExpression = QlExpressUtil.transitionQlExpress("natural_sum_score", "*", scoreItem.getScore().toPlainString(), null);
                } else {
                  //叠加分数
                    returnRuleExpression = scoreItem.getScore().toPlainString();
                }
                String ifRuleExpression;
                if (StringUtils.equals(item.getAssignMode(), "range_value")) {
                    //区间 例: order_master_distance >= 12 and order_master_distance <100.30
                    ifRuleExpression = QlExpressUtil.transitionQlExpress("and",
                            Lists.newArrayList(
                                    new QlExpressDto(item.getItemName(), ">=", scoreItem.getStartValue().toPlainString()),
                                    new QlExpressDto(item.getItemName(), "<", scoreItem.getEndValue().toPlainString())
                            ));
                } else {
                    //枚举 例: is_like_low_price_offer == 'A'
                    if(ItemTypeEnum.MASTER_GROUP.getCode().equals(item.getItemType())){
                        ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "contain", scoreItem.getValue(),null);
                    }else{

                        if ("is_special_category".equals(item.getItemName())) {
                            ifRuleExpression = QlExpressUtil.transitionQlExpress("1", "=", scoreItem.getValue(),null);
                        } else {
                            ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "=", scoreItem.getValue(),null);
                        }
                    }
                }
                return new IfExpressDto(ifRuleExpression, returnRuleExpression);
            }).collect(Collectors.toList());
            //计算匹配项分值的表达式 例如：if(a+b==12) then {return 7*0.6;}   else if(a+b==11)then{return 2;}    else {return 0;}
            String filterRuleExpression = QlExpressUtil.transitionQlExpress(ifExpressDtos, "0");
            //评分项隐藏的条件 不满足该项隐藏条件时，该项评分得0
            if (StringUtils.isNotBlank(filterFeatureRuleExpression)) {
                filterRuleExpression = StrUtil.format("if(!({})) return 0; ", filterFeatureRuleExpression) + filterRuleExpression;
            }

            String filterRuleParams = item.getItemName();
            if (StringUtils.isNotBlank(filterFeatureRuleExpressionParams)) {
                filterRuleParams = StrUtil.format("{},{}", filterFeatureRuleExpressionParams, filterRuleParams);
            }
            return new ScoreRuleExpressionDto(item.getItemTitle(), item.getItemName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }


    public List<SortRuleExpressionDto> buildSortRuleExpressions(List<SortRule> ruleList, String strategyType) {
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = new ArrayList<>(ruleList.size());
        ruleList.forEach(sortRule -> {
            SortRuleExpressionDto sortRuleExpressionDto = new SortRuleExpressionDto();
            buildOpenConditionExpression(sortRuleExpressionDto, sortRule);
            List<RuleItem> ruleItemList = sortRule.getItemList();
            sortRuleExpressionDto.setScoreRuleList(this.getRuleExpressions(ruleItemList, strategyType));
            sortRuleExpressionDtoList.add(sortRuleExpressionDto);
        });
        return sortRuleExpressionDtoList;
    }


    private void buildOpenConditionExpression(SortRuleExpressionDto sortRuleExpressionDto, SortRule sortRule) {
        SortRule.OpenCondition openCondition = sortRule.getOpenCondition();

        //开启条件表达式
        String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                        .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                        .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                    //时效标签 和 用户人群 操作符符号转换
                    List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user");
                    if (specialTerms.contains(s.getItemName())) {
                        t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                    }
                }));


        //服务 的开启条件特殊处理
        List<SortRule.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

        List<String> serveExpression = serveItems.stream().map(it -> {
            List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
            String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
            String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

            List<List<Long>> serveIdList = it.getServeIdList();

            List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

            List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

            List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","), Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
            }
            return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
        }).collect(Collectors.toList());
        String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
        if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
            openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
        } else {
            openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
        }

        sortRuleExpressionDto.setOpenConditionRuleExpression(openConditionRuleExpression);

        //开启条件表达式参数
        List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(SortRule.OpenConditionItem::getItemName)
                .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serveItems)) {
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                openConditionRuleParamsList.add("lv1_serve_id");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                openConditionRuleParamsList.add("lv2_serve_ids");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                openConditionRuleParamsList.add("lv3_serve_ids");
        }
        //开启条件表达式参数
        String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));
        sortRuleExpressionDto.setOpenConditionRuleParams(openConditionRuleParams);
    }
}