package com.wanshifu.master.order.push.domain.response.baseSelectStrategy;

import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.*;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 描述 :  初筛策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 策略状态
     */
    private Integer strategyStatus;

    private String orderFlag;

//    /**
//     * 开启条件
//     */
//    private OpenConditionVo openCondition;

    /**
     * 范围初筛
     */
    private RangeSelectVo rangeSelect;

    /**
     * 技能初筛
     */
    private TechniqueSelectVo techniqueSelect;

    /**
     * 状态初筛
     */
    private StatusSelectVo statusSelect;

    /**
     * 服务数据初筛
     */
    private ServeDataSelectVo serveDataSelect;
}