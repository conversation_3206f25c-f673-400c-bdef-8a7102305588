package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.OrderPushShardingConfig;
import com.wanshifu.master.order.push.mapper.OrderPushShardingConfigMapper;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 16:22
 */
@Repository
public class OrderPushShardingConfigRepository extends BaseRepository<OrderPushShardingConfig> {

    @Resource
    private OrderPushShardingConfigMapper orderPushShardingConfigMapper;

    @Override
    public List<OrderPushShardingConfig> selectAll() {
        return orderPushShardingConfigMapper.selectAllConfigs();
    }
}
