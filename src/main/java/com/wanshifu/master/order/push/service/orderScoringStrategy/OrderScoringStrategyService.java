package com.wanshifu.master.order.push.service.orderScoringStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.GetOrderScoringStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.OrderScoringStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;

import java.util.List;

public interface OrderScoringStrategyService {

    Integer create(CreateOrderScoringStrategyRqt rqt);


    Integer update(UpdateOrderScoringStrategyRqt rqt);

    Integer enable(EnableOrderScoringStrategyRqt rqt);

    OrderScoringStrategyDetailResp detail(OrderScoringStrategyDetailRqt rqt);

    SimplePageInfo<GetOrderScoringStrategyListResp> list(GetOrderScoringStrategyListRqt rqt);

    Integer delete(DeleteOrderScoringStrategyRqt rqt);

    List<OrderScoringItemListResp> scoringItemList(String itemName);
}
