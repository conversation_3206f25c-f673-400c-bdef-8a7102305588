package com.wanshifu.master.order.push.service.pushNotice.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.notice.domains.dto.AbTestStrategyRelationDto;
import com.wanshifu.master.notice.domains.po.PushNoticeStrategyCombination;
import com.wanshifu.master.notice.domains.request.bigdata.NoticeAbTestGroupInfoRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.CreateRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.DeleteRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.DetailRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.EnableRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.ListRqt;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.UpdateRqt;
import com.wanshifu.master.notice.domains.response.bigdata.NoticeAbTestGroupInfoResp;
import com.wanshifu.master.notice.service.api.PushNoticeStrategyCombinationApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.request.pushnotice.AbTestGroupInfoRqt;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.AbTestGroupInfoResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.ListResp;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeStrategyCombinationService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PushNoticeStrategyCombinationServiceImpl implements PushNoticeStrategyCombinationService {

    @Resource
    private PushNoticeStrategyCombinationApi pushNoticeStrategyCombinationApi;

    @Resource
    private ServeCommonService serveCommonService;

    @Resource
    private GoodsCommonService goodsCommonApi;

    @Resource
    private IopAccountApi iopAccountApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private AddressCommonService addressCommon;

    @Override
    public Integer create(CreateRqt rqt) {
        rqt.setOperateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyCombinationApi.create(rqt);
    }

    @Override
    public Integer update(UpdateRqt rqt) {
        rqt.setOperateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyCombinationApi.update(rqt);
    }

    @Override
    public Integer enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyCombinationApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyCombinationApi.delete(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {
        com.wanshifu.master.notice.domains.response.pushNoticeCombination.DetailResp detail = pushNoticeStrategyCombinationApi.detail(rqt);
        if (detail != null) {
            DetailResp detailResp = new DetailResp();
            BeanCopyUtil.copyProperties(detail, detailResp);

            List<DetailResp.RuleItem> ruleItems = JSON.parseArray(detail.getStrategyRule(), DetailResp.RuleItem.class);

            Set<Long> serveIds = ruleItems.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                    .filter(it -> CollectionUtils.isNotEmpty(it.getServeIdList()))
                    .flatMap(it -> it.getServeIdList().stream())
                    .collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

            Map<Long, String> serveInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(serveIds)) {
                serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                        .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
            }
            Map<Long, String> finalServeInfoMap = serveInfoMap;
            ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

                List<LinkedList<Long>> serveIdListList = item.getServeIdList();
                if (CollectionUtils.isNotEmpty(serveIdListList)) {
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    item.setServeInfoList(serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
            })));

            Map<Integer, com.wanshifu.master.notice.domains.response.pushNoticeCombination.DetailResp.NoticeStrategy> noticeStrategyMap =
                    detail.getNoticeStrategyList().stream()
                            .collect(Collectors.toMap(com.wanshifu.master.notice.domains.response.pushNoticeCombination.DetailResp.NoticeStrategy::getStrategyId,
                                    Function.identity()));

            ruleItems.forEach(ruleItem -> {
                List<com.wanshifu.master.notice.domains.response.pushNoticeCombination.DetailResp.NoticeStrategy> noticeStrategys
                        = Arrays.stream(ruleItem.getNoticeStrategyIds().split(","))
                        .map(it -> noticeStrategyMap.getOrDefault(Integer.parseInt(it), null))
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                ruleItem.setNoticeStrategyList(noticeStrategys);

            });
            detailResp.setRuleList(ruleItems);
            return detailResp;
        }
        return null;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        SimplePageInfo<PushNoticeStrategyCombination> simplePageInfo = pushNoticeStrategyCombinationApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PushNoticeStrategyCombination> pushNoticeStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = pushNoticeStrategyList.stream().map(PushNoticeStrategyCombination::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(",")))
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Goods> goods = goodsCommonApi.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        //城市地址
        String cityIdsStr = pushNoticeStrategyList.stream().map(PushNoticeStrategyCombination::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        List<Long> updateAccountIds = pushNoticeStrategyList.stream().map(PushNoticeStrategyCombination::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(pushNoticeStrategyList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(","))
                    .map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it))
                    .collect(Collectors.joining(",")));

            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }
            if (!Strings.isNullOrEmpty(s.getStrategyRelationInfo())) {
                t.setStrategyRelationList(JSON.parseArray(s.getStrategyRelationInfo(), AbTestStrategyRelationDto.class));
            }

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

    @Override
    public List<AbTestGroupInfoResp> listAbTestGroupInfoByTestId(AbTestGroupInfoRqt rqt) {
        UserInfoUtils.getCurrentLoginAccountId();
        NoticeAbTestGroupInfoRqt req = BeanUtil.toBean(rqt, NoticeAbTestGroupInfoRqt.class);
        List<NoticeAbTestGroupInfoResp> noticeAbTestGroupInfoRespList = pushNoticeStrategyCombinationApi.listAbTestGroupInfoByTestId(req);
        if (CollectionUtil.isEmpty(noticeAbTestGroupInfoRespList)) {
            return Collections.emptyList();
        }
        return noticeAbTestGroupInfoRespList.stream().map(it -> BeanUtil.toBean(it, AbTestGroupInfoResp.class)).collect(Collectors.toList());
    }


}
