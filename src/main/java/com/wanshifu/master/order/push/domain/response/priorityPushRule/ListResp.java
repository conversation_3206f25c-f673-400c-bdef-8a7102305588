package com.wanshifu.master.order.push.domain.response.priorityPushRule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 15:28
 */
@Data
public class ListResp {
    /**
     *策略组合id
     */
    private Integer  ruleId;
    /**
     * 策略名称
     */
    private String  ruleName;
    /**
     * 类目名称
     */
    private String  ruleDesc;

    private String orderFrom;

    private String appointType;

    private String categoryNames;

    private String cityNames;

    private Integer ruleStatus;


    private String pushGroups;


    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}