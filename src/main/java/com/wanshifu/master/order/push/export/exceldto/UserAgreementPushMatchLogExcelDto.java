package com.wanshifu.master.order.push.export.exceldto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 16:39
 */
@Data
public class UserAgreementPushMatchLogExcelDto {


    /**
     * 派单模式
     */
    @ExcelProperty("派单模式")
    private String matchType;

    /**
     * 订单城市
     */
    @ExcelProperty("订单所在城市")
    private String orderCityName;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String orderNo;

    /**
     * 商家id
     */
    @ExcelProperty("商家id")
    private String userId;

    /**
     * 订单下单时间
     */
    @ExcelProperty("订单下单时间")
    private String orderCreateTime;

    /**
     * 订单来源
     */
    @ExcelProperty("订单来源")
    private String orderSource;

    /**
     * 服务名称
     */
    @ExcelProperty("订单服务")
    private String serveTypeName;

    /**
     * 师傅id
     */
    @ExcelProperty("师傅ID")
    private String masterId;

    /**
     * 师傅姓名
     */
    @ExcelProperty("师傅姓名")
    private String masterName;

    /**
     * 协议师傅招募id
     */
    @ExcelProperty("招募ID")
    private String recruitId;

    /**
     * 是否匹配成功，1：匹配成功，0：匹配失败
     */
    @ExcelProperty("是否匹配成功")
    private String isMatchSuccess;

    /**
     * 匹配失败原因
     */
    @ExcelProperty("匹配失败原因")
    private String matchFailReason;


    /**
     * 是否计算价格成功，1：计价成功，0：计价失败
     */
    @ExcelProperty("是否计价成功")
    private String isCalculatePriceSuccess;


    /**
     * 计价失败原因
     */
    @ExcelProperty("计价失败原因")
    private String calculatePriceFailReason;


    /**
     * 是否过滤，1：过滤，0：未过滤
     */
    @ExcelProperty("是否二次过滤")
    private String isFilter;

    /**
     * 过滤原因
     */
    @ExcelProperty("二次过滤原因")
    private String filterReason;

    /**
     * 是否调度(是否分配成功)
     * 1：已调度
     * 0或null：未调度
     */
    @ExcelProperty("是否分配成功")
    private String isDistribute;

    /**
     * 调度规则
     */
    @ExcelProperty("分配规则")
    private String distributeRule;

    /**
     * 是否自动抢单成功，1：成功，0：失败
     */
    @ExcelProperty("是否抢单成功")
    private String isAutoGrabSuccess;

    /**
     * 自动抢单失败原因
     */
    @ExcelProperty("抢单失败原因")
    private String autoGrabFailReason;

    /**
     * 版本号(标志同一次推单)
     */
    @ExcelProperty("版本号")
    private String orderVersion;

    /**
     * 创建时间(匹配时间)
     */
    @ExcelProperty("创建时间")
    private String createTime;
}
