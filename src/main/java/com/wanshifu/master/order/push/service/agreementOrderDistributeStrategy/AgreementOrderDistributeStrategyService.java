package com.wanshifu.master.order.push.service.agreementOrderDistributeStrategy;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface AgreementOrderDistributeStrategyService {


    int create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);


}
