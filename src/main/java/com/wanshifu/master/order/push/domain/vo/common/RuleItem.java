package com.wanshifu.master.order.push.domain.vo.common;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 : 策略匹配项赋值.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-03 11:36
 */
@Data
public class RuleItem {

    /**
     * 匹配项名称
     */
    @NotEmpty
    private String itemTitle;

    //@NotEmpty
    //@ValueIn("master_group,master_quota")
    private String itemType;

    /**
     * 匹配项
     */
    @NotEmpty
    private String itemName;

    /**
     * 是否特殊类目，企业类目id
     */
    private List<Long> oneCategoryIds;

    /**
     * 是否特殊类目，家庭类目id
     */
    private List<Long> twoCategoryIds;

    /**
     * 匹配项权重 (0,1]
     */
    @DecimalMin(inclusive = false, value = "0")
    @DecimalMax(value = "1")
    @NotNull
    private BigDecimal weight;

    /**
     * 赋值方式：range_value: 区间，enum_value: 枚举
     */
    @NotEmpty
    @ValueIn("range_value,enum_value")
    private String assignMode;

    /**
     * 评分项列表
     */
    @NotEmpty
    @Valid
    private List<ScoreItem> scoreList;

    @Data
    public static class ScoreItem {

        /**
         * 起始值 assignMode="range_value" 时使用
         */
        private BigDecimal startValue;

        /**
         * 终止值 assignMode="range_value" 时使用
         */
        private BigDecimal endValue;

        /**
         * 枚举值 assignMode="enum_value" 时使用
         */
        private String value;

        /**
         * 赋分
         */
        @NotNull
        //@DecimalMin(inclusive = false, value = "0", message = "分值必须大于0")
        private BigDecimal score;
    }
}