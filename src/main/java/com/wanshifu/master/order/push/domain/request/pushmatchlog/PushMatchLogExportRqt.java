package com.wanshifu.master.order.push.domain.request.pushmatchlog;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/14 17:23
 */
@Data
public class PushMatchLogExportRqt {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 下单时间开始
     */
    private Date orderCreateTimeStart;

    /**
     * 下单时间结束
     */
    private Date orderCreateTimeEnd;

    /**
     * 派单模式
     * enterprise_appoint:总包直接指派
     * user_agreement:平台协议派单
     * cooperation_business:合作经营派单
     * new_model_city:样板城市派单
     */
    @NotEmpty
    @ValueIn("enterprise_appoint,user_agreement,cooperation_business,new_model_city,full_time_master")
    private String matchType;
}
