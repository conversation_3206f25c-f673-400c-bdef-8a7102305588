package com.wanshifu.master.order.push.domain.response.export;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 16:14
 */
@Data
public class PushExportTaskResp {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 导出数据类型名称
     */
    private String exportDataTypeName;

    /**
     * 导出数据名称
     */
    private String exportDataName;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 导出任务状态，process:处理中，fail:失败，success:成功
     */
    private String status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建时间
     */
    private Date createTime;
}
