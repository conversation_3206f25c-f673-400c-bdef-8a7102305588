package com.wanshifu.master.order.push.domain.response.pushLimitRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.CreateRqt;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 10:44
 */
@Data
public class DetailResp {

    /**
     * 规则
     */
    private Integer ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;


    /**
     * 城市id集合
     */
    private String cityIds;

    /**
     * 人群类型，crowd_label: 标签，crowd_group: 人群组
     */
    private String crowdType;

    /**
     * 人群标签或人群组
     */
    private String crowdLabel;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 限制范围, all: 全部，serve: 按服务限制
     */
    private String limitRange;

    /**
     * 限制服务规则
     */
    private LimitServeRule limitServeRule;

    /**
     * 例外规则
     */
    private ExclusiveRule exclusiveRule;

    /**
     * 限制推送规则，stop_pushing_immediately: 立即停止推送，decrease_push_by_percent: 按比例递减推送
     */
    private String limitRule;

    /**
     * 递减周期
     */
    private Integer decreaseDays;

    /**
     * 递减百分比
     */
    private Integer decreasePercent;


    /**
     * 固定递减百分比
     */
    private Integer fixedDecreasePercent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    private CrowdGroup crowdGroup;



    @Data
    public static class LimitServeRule{

        private String condition;

        private List<ServeRuleItem> itemList;
    }


    @Data
    public static class ExclusiveRule{

        private String condition;

        private List<CreateRqt.Item> itemList;
    }


    @Data
    public static class CrowdGroup{

        private String condition;

        private List<Item> itemList;
    }

    @Data
    public static class ServeRuleItem{

        @NotBlank
        private String itemName;

        @NotBlank
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;


        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;

    }


    @Data
    public static class Item {

        private String itemName;

        private String term;

        private String itemValue;

        private String itemType;

        private String itemTitle;

        private List<TermItem> termList;

        private List<ValueItem> valueList;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }




}