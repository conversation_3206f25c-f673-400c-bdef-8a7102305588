package com.wanshifu.master.order.push.service.pushNotice.impl;

import com.wanshifu.master.notice.domains.request.pushNotice.SendOfferSmsRqt;
import com.wanshifu.master.notice.service.api.PushNoticeApi;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class PushNoticeServiceImpl implements PushNoticeService {


    @Resource
    private PushNoticeApi pushNoticeApi;

    @Override
    public int sendOfferSms(SendOfferSmsRqt rqt){
        return pushNoticeApi.sendOfferSms(rqt);
    }
}
