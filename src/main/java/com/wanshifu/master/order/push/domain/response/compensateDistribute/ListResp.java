package com.wanshifu.master.order.push.domain.response.compensateDistribute;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  召回策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     * 策略id
     */
    private Integer distributeId;

    /**
     * 策略名称
     */
    private String categoryNames;

    private String strategyName;

    private String strategyDesc;

    /**
     * 类目名称
     */
    private String appointTypes;

    /**
     * 策略描述
     */
    private String orderPushFlag;

    /**
     * 规则条数
     */
    private String hasPrice;

    private String hasCooperationUser;

    private String compensateType;

    private Integer intervalTime;

    private Integer triggerNum;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
