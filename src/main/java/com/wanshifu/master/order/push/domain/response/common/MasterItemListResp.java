package com.wanshifu.master.order.push.domain.response.common;

import lombok.Data;

import java.util.List;

/**
 * 描述 :  查询匹配项.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-21 14:36
 */
@Data
public class MasterItemListResp {
    private String itemCode;
    private String itemName;
    private String itemDesc;
    private String valueType;

    public List<EnumValue> valueList;

    @Data
    public static class EnumValue {
        private String name;
        private String value;


    }

    public MasterItemListResp(String itemCode, String itemName,String itemDesc, String valueType) {
        this.itemCode = itemCode;
        this.itemName = itemName;
        this.itemDesc = itemDesc;
        this.valueType = valueType;
    }
}