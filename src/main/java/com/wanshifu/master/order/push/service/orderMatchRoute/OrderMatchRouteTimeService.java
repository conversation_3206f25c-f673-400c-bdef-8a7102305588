package com.wanshifu.master.order.push.service.orderMatchRoute;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteTimeRqt;

public interface OrderMatchRouteTimeService {

    Integer create(CreateOrderMatchRouteTimeRqt rqt);


    Integer update(UpdateOrderMatchRouteTimeRqt rqt);


    OrderMatchRouteTimeDetailResp detail(OrderMatchRouteTimeDetailRqt rqt);

    SimplePageInfo<OrderMatchRouteTimeListResp> list(OrderMatchRouteTimeListRqt rqt);



}
