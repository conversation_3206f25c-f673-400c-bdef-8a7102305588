package com.wanshifu.master.order.push.service.permission.impl;

import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.RoleApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.role.RoleDetailResp;
import com.wanshifu.master.order.push.domain.resp.role.RoleListResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetRoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.permission.RoleService;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl implements RoleService {

    @Resource
    private RoleApi roleApi;

    @Resource
    private IopAccountApi iopAccountApi;

//    @Resource
//    private AuthHandler authHandler;


    @Override
    public int add(AddRoleRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return roleApi.add(rqt);
    }

    @Override
    public int update(UpdateRoleRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return roleApi.update(rqt);
    }

    @Override
    public SimplePageInfo<GetRoleListResp> list(GetRoleListRqt rqt){
        SimplePageInfo<RoleListResp> simplePageInfo = roleApi.list(rqt);
        List<RoleListResp> roleListRespList = simplePageInfo.getList();
        List<GetRoleListResp> getRoleListRespList = new ArrayList<>();

        List<Long> updateAccountIds = roleListRespList.stream().map(RoleListResp::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(updateAccountIds)){
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }

        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        roleListRespList.forEach(roleListResp -> {
            GetRoleListResp getRoleListResp = new GetRoleListResp();
            BeanUtils.copyProperties(roleListResp,getRoleListResp);
            getRoleListResp.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(roleListResp.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            getRoleListRespList.add(getRoleListResp);
        });

        SimplePageInfo<GetRoleListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(simplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(simplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(getRoleListRespList);
        return listRespSimplePageInfo;
    }

    @Override
    public int delete(DeleteRoleRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return roleApi.delete(rqt);
    }

    @Override
    public GetRoleDetailResp detail(GetRoleDetailRqt rqt){
        GetRoleDetailResp resp = null;
        RoleDetailResp roleDetailResp = roleApi.detail(rqt);
        if(roleDetailResp != null){
            resp = new GetRoleDetailResp();
            BeanUtils.copyProperties(roleDetailResp,resp);
            List<Long> accountIdList = roleDetailResp.getAccountIdList();
            if(CollectionUtils.isNotEmpty(accountIdList)){

                List<Long> updateAccountIdList = new ArrayList<>();
                updateAccountIdList.addAll(accountIdList);
                updateAccountIdList.add(roleDetailResp.getUpdateAccountId());
                List<GetRoleDetailResp.AccountInfo> accountInfoList = new ArrayList<>();

                updateAccountIdList= updateAccountIdList.stream().distinct().collect(Collectors.toList());

                Map<Long, IopAccountResp.IopAccount> iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIdList)))
                        .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                        .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));

                Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

                accountIdList.forEach(accountId -> {
                    GetRoleDetailResp.AccountInfo accountInfo = new GetRoleDetailResp.AccountInfo();
                    accountInfo.setAccountId(accountId);
                    accountInfo.setAccountName(Optional.ofNullable(finalIopAccountMap.get(accountId)).map(IopAccountResp.IopAccount::getUsername).orElse(""));

                    accountInfo.setOperateAccountName(Optional.ofNullable(finalIopAccountMap.get(roleDetailResp.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
                    accountInfo.setOperateTime(roleDetailResp.getUpdateTime());
                    accountInfoList.add(accountInfo);


                });


                resp.setAccountList(accountInfoList);

            }

        }
        return resp;
    }


    @Override
    @Transactional
    public int addAccount(AddAccountRqt rqt){
        return roleApi.addAccount(rqt);
    }

}
