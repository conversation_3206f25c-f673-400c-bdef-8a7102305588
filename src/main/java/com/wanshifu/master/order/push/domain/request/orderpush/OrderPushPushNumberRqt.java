package com.wanshifu.master.order.push.domain.request.orderpush;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/21 14:04
 */
@Data
public class OrderPushPushNumberRqt {

    @NotNull
    @Min(value = 1L)
    private Long masterId;

    @NotNull
    @Min(value = 1L)
    private Long masterDivisionId;
}
