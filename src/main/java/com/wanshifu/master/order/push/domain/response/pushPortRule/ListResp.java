package com.wanshifu.master.order.push.domain.response.pushPortRule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 15:28
 */
@Data
public class ListResp {
    /**
     *策略组合id
     */
    private Integer  ruleId;
    /**
     * 策略名称
     */
    private String  ruleName;

    /**
     * 类目名称
     */
    private String  ruleDesc;


    /**
     * 指派模式
     */
    private String appointType;


    /**
     * 一级服务名称
     */
    private String lv1ServeNames;

    /**
     * 订单标签
     */
    private String orderTag;


    /**
     * 城市
     */
    private String cityNames;

    /**
     * 规则状态
     */
    private Integer ruleStatus;

    /**
     * 推单后XXX时间
     */
    private Integer intervalTime;

    /**
     * 报价人数
     */
    private Integer offerNum;


    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;


    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}