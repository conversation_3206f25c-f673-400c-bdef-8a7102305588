package com.wanshifu.master.order.push.service.orderDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.orderDistributeStrategy.GetOrderDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;

public interface OrderDistributeStrategyService {

    Integer create(CreateOrderDistributeStrategyRqt rqt);


    Integer update(UpdateOrderDistributeStrategyRqt rqt);

    Integer enable(EnableOrderDistributeStrategyRqt rqt);

    GetOrderDistributeStrategyDetailResp detail(OrderDistributeStrategyDetailRqt rqt);

    SimplePageInfo<GetOrderDistributeStrategyListResp> list(GetOrderDistributeStrategyListRqt rqt);


    Integer delete(DeleteOrderDistributeStrategyRqt rqt);


}