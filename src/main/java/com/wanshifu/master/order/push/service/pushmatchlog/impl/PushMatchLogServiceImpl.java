package com.wanshifu.master.order.push.service.pushmatchlog.impl;

import cn.hutool.core.bean.BeanUtil;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushExportTaskApi;
import com.wanshifu.master.order.push.api.PushMatchLogApi;
import com.wanshifu.master.order.push.domain.dto.*;

import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;

import com.wanshifu.master.order.push.domain.request.pushmatchlog.PushMatchLogExportRqt;

import com.wanshifu.master.order.push.domain.rqt.pushexporttask.CreatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.export.impl.PushExportService;
import com.wanshifu.master.order.push.service.pushmatchlog.PushMatchLogService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;

import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 * @description
 * @date 2025/4/25 20:42
 */
@Service
public class PushMatchLogServiceImpl implements PushMatchLogService {

    @Resource
    private PushMatchLogApi pushMatchLogApi;

    @Resource
    private PushExportTaskApi pushExportTaskApi;

    @Resource
    private PushExportService pushExportService;

    @Resource(name = "asyncExportExecutor")
    private Executor executor;

    @Override
    public SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogApi.userAgreementPushMatchLogList(pushMatchLogRqt);
    }

    @Override
    public SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogApi.enterpriseAppointPushMatchLogList(pushMatchLogRqt);
    }

    @Override
    public SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogApi.cooperationBusinessPushMatchLogList(pushMatchLogRqt);
    }

    @Override
    public SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogApi.newModelCityPushMatchLogList(pushMatchLogRqt);
    }

    @Override
    public SimplePageInfo<OrderFullTimeMasterMatchLogDto> fullTimeMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogApi.fullTimeMatchLogList(pushMatchLogRqt);
    }

    @Override
    public Integer exportPushMatchLogList(PushMatchLogExportRqt rqt) {
        String exportType = rqt.getMatchType();

        // 1. 创建异步任务记录
        CreatePushExportTaskRqt taskRqt = new CreatePushExportTaskRqt();
        taskRqt.setExportDataTypeName("派单日志查询");
        taskRqt.setStatus("process");

        String fileName = "派单日志.xlsx";

        if (PushLogMatchType.USER_AGREEMENT.getCode().equals(exportType)) {
            fileName = "平台协议派单-" + DateFormatterUtil.getNow() + "-" + System.currentTimeMillis() / 1000 + ".xlsx";
            taskRqt.setExportDataName("平台协议派单");
            taskRqt.setFileName(fileName);
        } else if (PushLogMatchType.ENTERPRISE_APPOINT.getCode().equals(exportType)) {
            fileName = "总包直接指派-" + DateFormatterUtil.getNow() + "-" + System.currentTimeMillis() / 1000 + ".xlsx";
            taskRqt.setExportDataName("总包直接指派");
            taskRqt.setFileName(fileName);
        } else if (PushLogMatchType.COOPERATION_BUSINESS.getCode().equals(exportType)) {
            fileName = "合作经营派单-" + DateFormatterUtil.getNow() + "-" + System.currentTimeMillis() / 1000 + ".xlsx";
            taskRqt.setExportDataName("合作经营派单");
            taskRqt.setFileName(fileName);
        } else if (PushLogMatchType.NEW_MODEL_CITY.getCode().equals(exportType)) {
            fileName = "样板城市派单-" + DateFormatterUtil.getNow() + "-" + System.currentTimeMillis() / 1000 + ".xlsx";
            taskRqt.setExportDataName("样板城市派单");
            taskRqt.setFileName(fileName);
        }else if (PushLogMatchType.FULL_TIME_MASTER.getCode().equals(exportType)) {
            fileName = "全时师傅派单-" + DateFormatterUtil.getNow() + "-" + System.currentTimeMillis() / 1000 + ".xlsx";
            taskRqt.setExportDataName("全时师傅派单");
            taskRqt.setFileName(fileName);
        }

        Long taskId = pushExportTaskApi.createPushExportTask(taskRqt);


        int pageSize = 200;

        PushMatchLogRqt pushMatchLogRqt = BeanUtil.toBean(rqt, PushMatchLogRqt.class);
        pushMatchLogRqt.setPageSize(pageSize);

        // 2. 异步执行导出
        CompletableFuture.runAsync(() -> pushExportService.asyncExportPushMatchLogList(taskId, exportType, pushMatchLogRqt), executor);

        return 1;
    }
}
