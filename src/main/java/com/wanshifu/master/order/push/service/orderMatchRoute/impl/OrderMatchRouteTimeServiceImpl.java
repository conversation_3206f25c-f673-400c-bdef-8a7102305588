package com.wanshifu.master.order.push.service.orderMatchRoute.impl;

import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.OrderMatchRouteTimeApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.AppointTypeEnum;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.*;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.orderMatchRoute.OrderMatchRouteTimeService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderMatchRouteTimeServiceImpl implements OrderMatchRouteTimeService {

    @Resource
    private OrderMatchRouteTimeApi orderMatchRouteTimeApi;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private GoodsCommonService goodsCommon;

//    @Resource
//    private AuthHandler authHandler;


    @Override
    public Integer create(CreateOrderMatchRouteTimeRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderMatchRouteTimeApi.create(rqt);
    }


    @Override
    public Integer update(UpdateOrderMatchRouteTimeRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderMatchRouteTimeApi.update(rqt);
    }

    @Override
    public OrderMatchRouteTimeDetailResp detail(OrderMatchRouteTimeDetailRqt rqt){
        OrderMatchRouteTime orderMatchRouteTime = orderMatchRouteTimeApi.detail(rqt);
        if(orderMatchRouteTime == null){
            return null;
        }

        OrderMatchRouteTimeDetailResp resp = new OrderMatchRouteTimeDetailResp();
        BeanUtils.copyProperties(orderMatchRouteTime,resp);
        return resp;
    }

    @Override
    public SimplePageInfo<OrderMatchRouteTimeListResp> list(OrderMatchRouteTimeListRqt rqt){
        SimplePageInfo<OrderMatchRouteTime> orderMatchRouteTimeSimplePageInfo = orderMatchRouteTimeApi.list(rqt);

        List<OrderMatchRouteTime> orderMatchRouteTimeList = orderMatchRouteTimeSimplePageInfo.getList();
        //类目名称
        List<Long> goodsIds = orderMatchRouteTimeList.stream().map(OrderMatchRouteTime::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());

        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));


        List<Long> updateAccountIds = orderMatchRouteTimeList.stream().map(OrderMatchRouteTime::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<OrderMatchRouteTimeListResp> listResps = BeanCopyUtil.copyListProperties(orderMatchRouteTimeList, OrderMatchRouteTimeListResp.class, (s, t) -> {
            if (StringUtils.equals(s.getCategoryIds(), "all")) {
                t.setCategoryName("全部(不限类目)");
            } else {
                t.setCategoryName(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }

            if (StringUtils.equals(s.getAppointTypes(), "all")) {
                t.setAppointType("全部(不限类型)");
            } else {
                t.setAppointType(Arrays.stream(s.getAppointTypes().split(",")).map(it -> AppointTypeEnum.asValue(Integer.parseInt(it)).name).collect(Collectors.joining(",")));
            }
            t.setSettingType(s.getSettingType().equals("appointTime") ? "指派时间" : "接单时间");
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });


        SimplePageInfo<OrderMatchRouteTimeListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(orderMatchRouteTimeSimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(orderMatchRouteTimeSimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(orderMatchRouteTimeSimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(orderMatchRouteTimeSimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }
}
