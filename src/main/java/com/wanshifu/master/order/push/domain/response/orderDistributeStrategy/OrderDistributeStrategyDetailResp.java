package com.wanshifu.master.order.push.domain.response.orderDistributeStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.CreateOrderDistributeStrategyRqt;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

@Data
public class OrderDistributeStrategyDetailResp {

    private Integer strategyId;

    private Long businessLineId;

    private String strategyName;

    private String strategyDesc;

    private String categoryIds;

    private String openCityMode;

    private String cityIds;

    @NotEmpty
    private List<DistributeStrategyVo> distributeStrategyList;


    @Data
    public static class DistributeStrategyVo{

        private CreateOrderDistributeStrategyRqt.OpenCondition openCondition;

        private Integer orderSelectStrategyId;

        private String orderSelectStrategyName;

        private Integer orderScoringStrategyId;

        private String orderScoringStrategyName;

        private String distributeRule;

    }

    @Data
    public static class OpenCondition{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<CreateOrderDistributeStrategyRqt.OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,time_liness_tag,appoint_user")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }}
