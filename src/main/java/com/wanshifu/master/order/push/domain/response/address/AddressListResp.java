package com.wanshifu.master.order.push.domain.response.address;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-21 19:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddressListResp {
    private Long divisionId;
    private String divisionName;
    private List<AddressListResp> addressList;

    public AddressListResp(Long divisionId, String divisionName) {
        this.divisionId = divisionId;
        this.divisionName = divisionName;
    }
}
