package com.wanshifu.master.order.push.domain.response.repushPolicy;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  重推机制列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     * 重推机制id
     */
    private Long policyId;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 重推机制名称
     */
    private String policyName;

    /**
     * 状�?
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "policyStatusStr")
    private Integer policyStatus;

    /**
     * 状态中�?
     */
    private String policyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @TranslateEnum(enumClass= OrderFlagEnum.class)
    private String orderFlag;
}
