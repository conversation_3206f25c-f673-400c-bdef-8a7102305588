package com.wanshifu.master.order.push.service.specialGroupStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.SpecialGroupStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.specialGroupStrategy.SpecialGroupStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.JsonValueUtils;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SpecialGroupStrategyServiceImpl implements SpecialGroupStrategyService {

    @Resource
    private SpecialGroupStrategyApi specialGroupStrategyApi;
    @Resource
    private ServeCommonService serveCommonService;
    @Resource
    private IopAccountApi iopAccountApi;
    @Resource
    private AddressCommonService addressCommonService;

    @Override
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.create(rqt);
    }

    @Override
    public int update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {
        DetailResp resp = new DetailResp();
        SpecialGroupStrategy strategy = specialGroupStrategyApi.detail(rqt);
        if (strategy == null) {
            return null;
        }
        BeanUtils.copyProperties(strategy, resp);
        String serveIdArray = strategy.getServeIdArray();
        resp.setServeIds(serveIdArray);

        Set<Long> serveIdSet = JSON.parseObject(serveIdArray, new TypeReference<List<List<Long>>>(){})
                .stream().flatMap(List::stream).collect(Collectors.toSet());
        List<ServeBaseInfoResp> serveList = serveCommonService.getServeBaseInfoByServeIdSet(serveIdSet);
        if (CollectionUtils.isNotEmpty(serveList)) {
            List<String> serviceInfoList = new ArrayList<>();
            for (ServeBaseInfoResp serveResp : serveList) {
                String serveIdAndName = serveResp.getServeId() + ":" + serveResp.getName();
                serviceInfoList.add(serveIdAndName);
            }
            resp.setServiceInfoList(serviceInfoList);
        }
        return resp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        // 获取分页数据
        SimplePageInfo<SpecialGroupStrategy> pages = specialGroupStrategyApi.list(rqt);
        if (pages == null || CollectionUtils.isEmpty(pages.getList())) {
            return new SimplePageInfo<>();
        }

        List<SpecialGroupStrategy> strategies = pages.getList();

        // 批量获取关联数据
        Map<Long, String> accountNameMap = batchGetAccountNames(strategies);
        Map<Long, String> serveNameMap = batchGetServeNames(strategies);
        Map<String, String> cityNameMap = batchGetCityNames(strategies);

        // 转换数据
        List<ListResp> respList = BeanCopyUtil.copyListProperties(strategies, ListResp.class,
            (strategy, resp) -> enrichResponseData(strategy, resp, accountNameMap, serveNameMap, cityNameMap));

        // 构建分页结果
        return buildPageInfo(pages, respList);
    }



    /**
     * 批量获取账户名称映射
     */
    private Map<Long, String> batchGetAccountNames(List<SpecialGroupStrategy> strategies) {
        try {
            List<Long> updateAccountIds = strategies.stream()
                    .map(SpecialGroupStrategy::getUpdateAccountId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(updateAccountIds)) {
                return Collections.emptyMap();
            }

            return Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(
                            IopAccountResp.IopAccount::getAccountId,
                            account -> Optional.ofNullable(account.getChineseName()).orElse(""),
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量获取账户名称失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量获取服务名称映射
     */
    private Map<Long, String> batchGetServeNames(List<SpecialGroupStrategy> strategies) {
        try {
            Set<Long> allServeIds = strategies.stream()
                    .filter(strategy -> StringUtils.isNotBlank(strategy.getServeIds()))
                    .flatMap(strategy -> Arrays.stream(strategy.getServeIds().split(",")))
                    .filter(StringUtils::isNotBlank)
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(allServeIds)) {
                return Collections.emptyMap();
            }

            List<ServeBaseInfoResp> serveBaseInfoList = serveCommonService.getServeBaseInfoByServeIdSet(allServeIds);
            return Optional.ofNullable(serveBaseInfoList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(
                            ServeBaseInfoResp::getServeId,
                            serve -> Optional.ofNullable(serve.getName()).orElse(""),
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量获取服务名称失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量获取城市名称映射
     */
    private Map<String, String> batchGetCityNames(List<SpecialGroupStrategy> strategies) {
        try {
            List<String> allCityIds = strategies.stream()
                    .filter(strategy -> StringUtils.isNotBlank(strategy.getCityIds()))
                    .map(SpecialGroupStrategy::getCityIds)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(allCityIds)) {
                return Collections.emptyMap();
            }

            Map<String, String> cityNameMap = new HashMap<>();
            for (String cityIds : allCityIds) {
                List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(cityIds);
                if (CollectionUtils.isNotEmpty(addressList)) {
                    String cityNames = addressList.stream()
                            .map(Address::getDivisionName)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));
                    cityNameMap.put(cityIds, cityNames);
                }
            }
            return cityNameMap;
        } catch (Exception e) {
            log.error("批量获取城市名称失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 丰富响应数据
     */
    private void enrichResponseData(SpecialGroupStrategy strategy, ListResp resp,
                                  Map<Long, String> accountNameMap,
                                  Map<Long, String> serveNameMap,
                                  Map<String, String> cityNameMap) {
        try {
            // 设置最后修改人名称
            resp.setLastUpdateAccountName(accountNameMap.getOrDefault(strategy.getUpdateAccountId(), ""));

            // 设置服务名称
            if (StringUtils.isNotBlank(strategy.getServeIds())) {
                String serveNames = Arrays.stream(strategy.getServeIds().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(serveIdStr -> {
                            try {
                                Long serveId = Long.parseLong(serveIdStr);
                                return serveNameMap.getOrDefault(serveId, serveIdStr);
                            } catch (NumberFormatException e) {
                                log.warn("无效的服务ID: {}", serveIdStr);
                                return serveIdStr;
                            }
                        })
                        .collect(Collectors.joining(","));
                resp.setServeNames(serveNames);
            }

            // 设置城市名称
            if (StringUtils.isNotBlank(strategy.getCityIds())) {
                resp.setCityNames(cityNameMap.getOrDefault(strategy.getCityIds(), ""));
            }

            // 设置推送组信息
            if (StringUtils.isNotBlank(strategy.getPushGroups())) {
                try {
                    resp.setPushGroups(JsonValueUtils.extractAllValues(strategy.getPushGroups()));
                } catch (Exception e) {
                    log.warn("解析推送组信息失败: {}", strategy.getPushGroups(), e);
                    resp.setPushGroups("");
                }
            }
        } catch (Exception e) {
            log.error("丰富响应数据时发生异常，策略ID: {}", strategy.getStrategyId(), e);
        }
    }

    /**
     * 构建分页信息
     */
    private SimplePageInfo<ListResp> buildPageInfo(SimplePageInfo<SpecialGroupStrategy> sourcePage, List<ListResp> respList) {
        SimplePageInfo<ListResp> resultPage = new SimplePageInfo<>();
        resultPage.setPageNum(sourcePage.getPageNum());
        resultPage.setPageSize(sourcePage.getPageSize());
        resultPage.setPages(sourcePage.getPages());
        resultPage.setTotal(sourcePage.getTotal());
        resultPage.setList(respList);
        return resultPage;
    }

    @Override
    public Integer enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.delete(rqt);
    }
}
