package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;

/**
 * 描述 :  开启条�?
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 16:01
 */
@Data
public class OpenConditionVo {

    /**
     * 条件类型 orderFlag: 订单标识
     */
    @NotEmpty
    @ValueIn("orderFlag")
    private String conditionType;

    /**
     * 符号 in:包含  not_in:不包�?
     */
    @NotEmpty
    @ValueIn("in,not_in")
    private String term;

    /**
     * 条件值，多个以逗号拼接，normal: 普通订单，exclusive:专属订单,ikea:宜家订单,cooperate:合作商订�?
     */
    @NotEmpty
    private String conditionValue;
}
