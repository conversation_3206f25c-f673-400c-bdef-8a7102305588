package com.wanshifu.master.order.push.service.orderMatchRouting.impl;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.OrderMatchRoutingApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.OrderFromEnum;
import com.wanshifu.master.order.push.domain.enums.OrderPushFlagEnum;
import com.wanshifu.master.order.push.domain.enums.OrderTagEnum;
import com.wanshifu.master.order.push.domain.enums.RoutingTypeEnum;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.*;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.orderMatchRouting.OrderMatchRoutingService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderMatchRoutingServiceImpl implements OrderMatchRoutingService {

    @Resource
    private OrderMatchRoutingApi orderMatchRoutingApi;


    @Resource
    private IopAccountApi iopAccountApi;

//    @Resource
//    private AuthHandler authHandler;

    @Override
    public int create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        Integer result = orderMatchRoutingApi.create(rqt);
        if(result == 10010){
            throw new BusException("已存在相同的路由名称");
        }
        return 1;
    }

    @Override
    public int update(UpdateRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
       return orderMatchRoutingApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt){
        OrderMatchRouting orderMatchRouting = orderMatchRoutingApi.detail(rqt);
        if(orderMatchRouting == null){
            return null;
        }
        DetailResp resp = new DetailResp();
        BeanUtils.copyProperties(orderMatchRouting,resp);
        return resp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){

        SimplePageInfo<OrderMatchRouting> simplePageInfo = orderMatchRoutingApi.list(rqt);


        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderMatchRouting> orderMatchRoutingList = simplePageInfo.getList();


        List<Long> updateAccountIds = orderMatchRoutingList.stream().map(OrderMatchRouting::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(orderMatchRoutingList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            OrderTagEnum orderTag = OrderTagEnum.asValue(s.getOrderTag());
            t.setOrderTag(Objects.nonNull(orderTag) ? orderTag.name : "-");
            t.setRoutingType(RoutingTypeEnum.asValue(s.getRoutingType()).name);
            String matchRouting = OrderPushFlagEnum.asValue(s.getLv1MasterType()).name;
            if(StringUtils.isNotBlank(s.getLv2MasterType())){
                matchRouting = matchRouting + "," + OrderPushFlagEnum.asValue(s.getLv2MasterType()).name;
            }
            if(StringUtils.isNotBlank(s.getLv3MasterType())){
                matchRouting = matchRouting + "," + OrderPushFlagEnum.asValue(s.getLv3MasterType()).name;
            }
            t.setMatchRouting(matchRouting);

            OrderFromEnum orderFromEnum = OrderFromEnum.asValue(s.getOrderFrom());
            t.setOrderFrom(orderFromEnum != null ? orderFromEnum.name : "-");

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }


}
