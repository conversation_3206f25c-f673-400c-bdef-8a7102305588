package com.wanshifu.master.order.push.api.base;


import com.wanshifu.master.notice.domains.request.sms.SmsNoTmplRqtBean;
import com.wanshifu.master.notice.domains.request.sms.SmsTmplRqtBean;
import com.wanshifu.master.order.push.domain.request.common.SmsTemplateRqt;
import com.wanshifu.master.order.push.domain.request.common.SmsTmplListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "sms-service",
        url = "${sms-service.url}",
        path = "/smsTmpl",
        contextId = "smsTmplApi",
        configuration = {DefaultEncoder.class, DefaultDecoder.class, DefaultErrorDecode.class})
public interface SmsTmplApi {

    /**
     *发送短信（有模版）
     * @param tmplNameEn 信息
     * @return
     */
    @PostMapping("list")
    Long list(String tmplNameEn);


    /**
     * 发送短信（无模版）
     * @param tmplId 发送信息
     * @return
     */
    @PostMapping("queryById")
    String queryById(Long tmplId);



    /**
     * 发送短信（无模版）
     * @param smsTypeId 发送信息
     * @return
     */
    @PostMapping("selectPassTmpl")
    List<SmsTmplListResp> selectPassTmpl(SmsTemplateRqt tqt);


    /**
     * 发送短信（无模版）
     * @param smsTypeId 发送信息
     * @return
     */
    @PostMapping("selectPassVoiceTmpl")
    List<SmsTmplListResp> selectPassVoiceTmpl(SmsTemplateRqt tqt);




}

