package com.wanshifu.master.order.push.domain.response.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-23 17:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServeListResp {
    private Long serveId;
    private String serveName;
    private List<ServeListResp> serveList;
}