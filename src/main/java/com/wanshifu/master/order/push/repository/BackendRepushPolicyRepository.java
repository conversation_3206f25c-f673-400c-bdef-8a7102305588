//package com.wanshifu.master.order.push.repository;
//
//import cn.hutool.core.lang.Assert;
//import cn.hutool.core.util.StrUtil;
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.constant.CommonConstant;
//import com.wanshifu.master.order.push.domain.po.RepushPolicy;
//import com.wanshifu.master.order.push.domain.po.SortingStrategy;
//import com.wanshifu.master.order.push.mapper.BackendRepushPolicyMapper;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
//@Repository
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class BackendRepushPolicyRepository extends BaseRepository<RepushPolicy> {
//
//    private final BackendRepushPolicyMapper backendRepushPolicyMapper;
//
//    public RepushPolicy selectByPolicyId(Long policyId) {
//        RepushPolicy repushPolicy = this.selectByPrimaryKey(policyId);
//        Assert.isTrue(repushPolicy != null && Objects.equals(repushPolicy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
//        return repushPolicy;
//    }
//
//    public Long insert(Integer businessLineId, Long snapshotId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
//        RepushPolicy repushPolicy = new RepushPolicy();
//        repushPolicy.setSnapshotId(snapshotId);
//        repushPolicy.setPolicyDesc(policyDesc);
//        repushPolicy.setPolicyName(policyName);
//        repushPolicy.setBusinessLineId(businessLineId);
//        repushPolicy.setCityIds(cityIds);
//        repushPolicy.setCategoryIds(categoryIds);
//        repushPolicy.setPolicyStatus(CommonConstant.STRATEGY_STATUS_0);
//        repushPolicy.setStrategyCombination(strategyCombinationJson);
//        repushPolicy.setCreateAccountId(loginUserId);
//        repushPolicy.setUpdateAccountId(loginUserId);
//        this.insertSelective(repushPolicy);
//        return repushPolicy.getPolicyId();
//    }
//
//    public int update(Long policyId, Long snapshotId, Integer businessLineId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
//        RepushPolicy repushPolicy = new RepushPolicy();
//        repushPolicy.setPolicyId(policyId);
//        repushPolicy.setSnapshotId(snapshotId);
//        repushPolicy.setPolicyDesc(policyDesc);
//        repushPolicy.setPolicyName(policyName);
//        repushPolicy.setBusinessLineId(businessLineId);
//        repushPolicy.setCityIds(cityIds);
//        repushPolicy.setCategoryIds(categoryIds);
//        repushPolicy.setStrategyCombination(strategyCombinationJson);
//        repushPolicy.setUpdateAccountId(loginUserId);
//        return this.updateByPrimaryKeySelective(repushPolicy);
//    }
//
//    public int updateStatus(Long policyId, Integer policyStatus) {
//        RepushPolicy repushPolicy = new RepushPolicy();
//        repushPolicy.setPolicyId(policyId);
//        repushPolicy.setPolicyStatus(policyStatus);
//        return this.updateByPrimaryKeySelective(repushPolicy);
//    }
//
//    public List<RepushPolicy> selectList(Long businessLineId, Long cityId, String policyName, Date createStartTime, Date createEndTime) {
//        Example example = new Example(SortingStrategy.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (businessLineId != null) {
//            criteria.andEqualTo("businessLineId", businessLineId);
//        }
//        if (cityId != null) {
//            criteria.andCondition(StrUtil.format("(city_ids = 'all' or FIND_IN_SET({},city_ids))", cityId));
//        }
//        if (StringUtils.isNotBlank(policyName)) {
//            criteria.andCondition(StrUtil.format("policy_name like '%{}%'", policyName));
//        }
//        if (createStartTime != null) {
//            criteria.andGreaterThanOrEqualTo("createTime", createStartTime);
//        }
//        if (createEndTime != null) {
//            criteria.andLessThanOrEqualTo("createTime", createEndTime);
//        }
//        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
//        example.orderBy("updateTime").desc();
//        return this.selectByExample(example);
//    }
//
//    public int softDeleteByStrategyId(Long policyId) {
//        RepushPolicy repushPolicy = new RepushPolicy();
//        repushPolicy.setPolicyId(policyId);
//        repushPolicy.setIsDelete(CommonConstant.DELETE_STATUS_1);
//        return this.updateByPrimaryKeySelective(repushPolicy);
//    }
//
//    public RepushPolicy selectByCityAndCategory(List<String> cityIdList, List<String> categoryIdList, Long policyId, Integer businessLineId) {
//        return backendRepushPolicyMapper.selectByCityAndCategory(CollectionUtils.isEmpty(cityIdList) ? "" : String.join(",", cityIdList), categoryIdList, policyId,businessLineId);
//    }
//}