package com.wanshifu.master.order.push.config;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/17 16:01
 */
public enum DataSourceKey {

    /**
     * 默认数据源order_push_service
     */
    DEFAULT_PUSH_DATASOURCE("default_push_dataSource");

    public final String value;


    DataSourceKey(String value) {
        this.value = value;
    }


    private static final Map<String, DataSourceKey> valueMapping = new HashMap<>((int) (DataSourceKey.values().length / 0.75));

    static {
        for (DataSourceKey instance : DataSourceKey.values()) {
            valueMapping.put(instance.value, instance);
        }
    }

    public static DataSourceKey asValue(String value) {
        return valueMapping.get(value);
    }
}
