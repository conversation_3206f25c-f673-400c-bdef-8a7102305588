package com.wanshifu.master.order.push.domain.response.permission;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GetPermissionSetDetailResp {

    private Integer permissionSetId;

    private String permissionSetName;

    private String permissionSetDesc;

    private List<RoleInfo> roleList;

    private List<PermissionMenu> menuList;


    @Data
    public static final class RoleInfo{

        private Integer roleId;

        private String roleName;

        private Date operateTime;

        private String operateAccountName;
    }


    @Data
    public static final class PermissionMenu{

        private Integer menuId;

        private List<Integer> buttonList;

        private List<Integer> tabList;


    }
    


}
