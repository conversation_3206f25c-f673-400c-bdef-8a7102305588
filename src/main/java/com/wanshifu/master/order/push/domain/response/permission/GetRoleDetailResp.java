package com.wanshifu.master.order.push.domain.response.permission;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GetRoleDetailResp {

    private Integer roleId;

    private String roleName;

    private String roleDesc;

    private List<AccountInfo> accountList;

    @Data
    public static final class AccountInfo{

        private Long accountId;

        private String accountName;

        private Date operateTime;

        private String operateAccountName;
    }
}
