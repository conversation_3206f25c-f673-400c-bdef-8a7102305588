package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 描述 :  状态初筛.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 16:01
 */
@Data
public class StatusSelectVo {

    /**
     * 或且关系, and: 且，or: 或
     */
    @NotEmpty
    @ValueIn("and,or")
    private String condition;

    /**
     * 初筛条件
     */
    @NotEmpty
    @Valid
    private List<StatusSelectItem> itemList;

    @Data
    private static class StatusSelectItem {
        /**
         * 条件名称, master_status: 师傅状态
         */
        @NotEmpty
        @ValueIn("master_status")
        private String itemName;

        /**
         * 符号 in:包含
         */
        @ValueIn("in")
        @NotEmpty
        private String term;

        /**
         * 条件值
         * black_list: 黑名单
         * account_status_abnormal: 账号状态异常
         * account_freeze:账号冻结
         * not_settled:未完成入住
         * exam_not_pass:考试未通过
         * not_active_gt_30:超过30天未活跃
         * not_working:开工状态为休息
         */
        @NotEmpty
        private String itemValue;
    }
}