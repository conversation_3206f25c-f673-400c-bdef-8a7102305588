package com.wanshifu.master.order.push.controller.filterStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.filterStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;
import com.wanshifu.master.order.push.service.filterStrategy.impl.FilterStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 描述 :  召回策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/filterStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FilterStrategyController {

    private final FilterStrategyService filterStrategyService;

    /**
     * 创建召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return filterStrategyService.create(rqt);
    }


    /**
     * 修改召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return filterStrategyService.update(rqt);
    }

    /**
     * 召回策略详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return filterStrategyService.detail(rqt);
    }

    /**
     * 召回策略列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return filterStrategyService.list(rqt);
    }


    /**
     * 启用/禁用召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return filterStrategyService.enable(rqt);
    }

    /**
     * 删除召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return filterStrategyService.delete(rqt);
    }
}