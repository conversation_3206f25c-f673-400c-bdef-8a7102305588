package com.wanshifu.master.order.push.api.address;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/19 9:51
 */
public interface CommonAddressService {

    /**
     * 根据订单区域id或师傅区域id获取省下级地址id
     *
     * @param divisionId   订单区域id或师傅区域id
     * @param sourceMethod 上游方法，用于后续排查哪个接口未传参或传参有�?
     * @param params 上游方法参数
     * @return
     */
    List<Long> getProvinceNextIdV2(Long divisionId, String sourceMethod, String params);
}
