package com.wanshifu.master.order.push.config;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Strings;
import com.wanshifu.master.order.push.domain.po.OrderPushShardingConfig;
import com.wanshifu.master.order.push.repository.OrderPushShardingConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 16:22
 */
@Service
@Slf4j
public class OrderPushShardingConfigService {

    /**
     * 分表配置
     * key:城市divisionId value:分表表名后缀
     */
    private static Map<String, String> shardingConfigMap = new HashMap<>();

    @Resource
    private OrderPushShardingConfigRepository orderPushShardingConfigRepository;

    private void loadShardingConfigFromDatabase() {
        if (orderPushShardingConfigRepository == null) {
            throw new IllegalStateException("orderPushShardingConfigRepository has not been initialized!");
        }

        List<OrderPushShardingConfig> orderPushShardingConfigs = orderPushShardingConfigRepository.selectAll();
        if (CollectionUtil.isEmpty(orderPushShardingConfigs)) {
            throw new IllegalStateException("orderPushShardingConfigs is empty,initialized failed!");
        }

        shardingConfigMap = orderPushShardingConfigs.stream()
                .collect(Collectors.toMap(
                        OrderPushShardingConfig::getCityDivisionId,
                        OrderPushShardingConfig::getSuffixTableName
                ));
    }

    @PostConstruct
    public void init() {
        log.info("order push sharding config init start");
        loadShardingConfigFromDatabase();
    }

    /**
     * 获取分表后缀
     * @param cityDivisionId 城市divisionId
     * @return 分表后缀
     */
    public static String getShardingSuffixTableName(String cityDivisionId) {
        if (Strings.isNullOrEmpty(cityDivisionId) || "0".equals(cityDivisionId)) {
            log.error(String.format("order push sharding orderDivisionId error! cityDivisionId:%s", cityDivisionId));
            return null;
        }
        return shardingConfigMap.get(cityDivisionId);
    }
}
