package com.wanshifu.master.order.push.service.repushPolicy.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.*;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.repushPolicy.DetailResp;
import com.wanshifu.master.order.push.domain.response.repushPolicy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.repushPolicy.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.repushPolicy.RepushPolicyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 16:48
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RepushPolicyServiceImpl implements RepushPolicyService {

    private final AddressCommonService addressCommon;
    // private final AuthHandler authHandler;
    private final IopAccountApi iopAccountApi;
    private final ServeCommonService serveCommonService;
    private final RepushPolicyApi repushPolicyApi;
    private final BaseSelectStrategyApi baseSelectStrategyApi;
    private final FilterStrategyApi filterStrategyApi;
    private final SortingStrategyApi sortingStrategyApi;
    private final PushRuleApi pushRuleApi;


    private final  String VERSION = "v2.0.0";
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return repushPolicyApi.create(rqt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        log.info("RepushPolicyServiceImpl updateRqt:" + JSON.toJSONString(rqt));
        return repushPolicyApi.update(rqt);
    }


    private void checkCityNumber(String cityIds) {
        if (!StringUtils.equals(cityIds, "all")) {
            Assert.isTrue(cityIds.split(",").length <= 100, "所选城市不得超过100个");
        }
    }




    @Override
    public DetailResp detail(DetailRqt rqt) {
        RepushPolicy repushPolicy = repushPolicyApi.detail(rqt);

        DetailResp detailResp = new DetailResp();
        BeanCopyUtil.copyProperties(repushPolicy, detailResp);
        List<DetailResp.PushStrategyVo> pushStrategyVoList= Lists.newArrayList();
        if (VERSION.equals(repushPolicy.getVersion())) {
            pushStrategyVoList = JSONObject.parseArray(repushPolicy.getStrategyCombination(), DetailResp.PushStrategyVo.class);
        } else {
            pushStrategyVoList.add(JSONObject.parseObject(repushPolicy.getStrategyCombination(), DetailResp.PushStrategyVo.class));
        }
        pushStrategyVoList.forEach(pushStrategyVo->{
            pushStrategyVo.getStrategyCombination().setBaseSelectStrategyName(Optional.ofNullable(baseSelectStrategyApi.detail(new com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.DetailRqt(pushStrategyVo.getStrategyCombination().getBaseSelectStrategyId()))).map(BaseSelectStrategy::getStrategyName).orElse(""));
            pushStrategyVo.getStrategyCombination().setFilterStrategyName(Optional.ofNullable(filterStrategyApi.detail(new com.wanshifu.master.order.push.domain.rqt.filterStrategy.DetailRqt(pushStrategyVo.getStrategyCombination().getFilterStrategyId()))).map(FilterStrategy::getStrategyName).orElse(""));
            pushStrategyVo.getStrategyCombination().setSortingStrategyName(Optional.ofNullable(sortingStrategyApi.detailV2(new com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt(pushStrategyVo.getStrategyCombination().getSortingStrategyId()))).map(SortingStrategy::getStrategyName).orElse(""));

            if(StringUtils.isBlank(pushStrategyVo.getPushRuleType())){
                pushStrategyVo.setPushRuleType("old");
            }

            Integer pushRuleId = pushStrategyVo.getPushRuleId();
            if(pushRuleId != null && pushRuleId > 0){
                com.wanshifu.master.order.push.domain.rqt.pushRule.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.pushRule.DetailRqt();
                detailRqt.setRuleId(pushRuleId);
                PushRule pushRule = pushRuleApi.detail(detailRqt);
                pushStrategyVo.setPushRuleName(pushRule.getRuleName());
            }
        });

        String cityIds = repushPolicy.getCityIds();
        if (StringUtils.equals(cityIds, "all")) {
            detailResp.setProvinceIds("all");
        } else {
            //城市地址
            List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIds);
            Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

            List<Address> addresses = Arrays.stream(cityIds.split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
            detailResp.setProvinceIds(addresses.stream().map(it -> it.getLv2DivisionId().toString()).distinct().collect(Collectors.joining(",")));
        }

        Set<Long> serveIds =   pushStrategyVoList.stream().flatMap(it->it.getOpenCondition().getItemList().stream())
                .filter(it->StringUtils.equals(it.getItemName(),"serve"))
                .map(DetailResp.PushStrategyVo.OpenConditionItem::getServeIdList)
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        //回显服务名称
        Map<Long, String> finalServeInfoMap = serveInfoMap;

        pushStrategyVoList.forEach(pushStrategyVo-> pushStrategyVo.getOpenCondition().getItemList().forEach(it -> {
            if (StringUtils.equals(it.getItemName(), "serve")) {
                List<List<Long>> serveIdListList = it.getServeIdList();
                if (CollectionUtils.isNotEmpty(serveIdListList)) {
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    it.setServeInfoList(serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
            }
        }));

        detailResp.setPushStrategy(pushStrategyVoList);
        return detailResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return repushPolicyApi.enable(rqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        SimplePageInfo<RepushPolicy> repushPolicySimplePageInfo = repushPolicyApi.list(rqt);
        List<RepushPolicy> repushPolicies = repushPolicySimplePageInfo.getList();

        String cityIdsStr = repushPolicies.stream().map(RepushPolicy::getCityIds).flatMap(it -> Arrays.stream(it.split(","))).distinct().filter(it -> !StringUtils.equals("all", it)).collect(Collectors.joining(","));
        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        List<Long> updateAccountIds = repushPolicies.stream().map(RepushPolicy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }

        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(repushPolicies, ListResp.class, (s, t) -> {
            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setProvince("全国");
                t.setCity("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCity(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
                t.setProvince(addresses.stream().map(Address::getLv2DivisionName).distinct().collect(Collectors.joining(",")));
            }
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });
        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(repushPolicySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(repushPolicySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(repushPolicySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(repushPolicySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(DeleteRqt rqt) {
        return repushPolicyApi.delete(rqt);
    }
}