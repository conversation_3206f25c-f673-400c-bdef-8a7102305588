//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.po.SimulatePush;
//import org.springframework.stereotype.Repository;
//
//@Repository
//public class BackendSimulatePushRepository extends BaseRepository<SimulatePush> {
//
//    public SimulatePush selectBySimulateIdAndOrderId(Long simulateId,Long orderId){
//        SimulatePush simulatePush = new SimulatePush();
//        simulatePush.setSimulateId(simulateId);
//        simulatePush.setOrderId(orderId);
//        return CollectionUtils.getFirstSafety(select(simulatePush));
//    }
//
//}
