package com.wanshifu.master.order.push.service.pushPortRule.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.PushPortRuleApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.dto.PortPushRuleDTO;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.pushPortRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushPortRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.pushPortRule.PushPortRuleService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 推单端口规则
 */
@Service
public class PushPortRuleServiceImpl implements PushPortRuleService {


    @Resource
    private PushPortRuleApi pushPortRuleApi;

    @Resource
    private IopAccountApi iopAccountApi;


    @Resource
    private AddressCommonService addressCommonService;


    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private ServeCommonService serveCommonService;

//    @Resource
//    private AuthHandler authHandler;


    /**
     * 创建端口规则
     * @param rqt
     * @return
     */
    @Override
    public Integer create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushPortRuleApi.create(rqt);
    }

    /**
     * 更新端口规则
     * @param rqt
     * @return
     */
    @Override
    public Integer update(UpdateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushPortRuleApi.update(rqt);
    }


    @Override
    public DetailResp detail(DetailRqt rqt){
        PushPortRule pushPortRule = pushPortRuleApi.detail(rqt);
        if(Objects.isNull(pushPortRule)){
            return null;
        }

        DetailResp resp = new DetailResp();
        BeanUtil.copyProperties(pushPortRule,resp);
        if(StringUtils.isNotBlank(pushPortRule.getPushRule())){
            resp.setRuleList(JSON.parseArray(pushPortRule.getPushRule(), PortPushRuleDTO.class));
        }
        return resp;

    }


    /**
     * 端口规则详情
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){
        SimplePageInfo<PushPortRule> simplePageInfo = pushPortRuleApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PushPortRule> pushPortRuleList = simplePageInfo.getList();


        List<Long> updateAccountIds = pushPortRuleList.stream().map(PushPortRule::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        String cityIdsStr = pushPortRuleList.stream().map(PushPortRule::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));

        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));



        //类目名称
        Set<Long> serveIds = pushPortRuleList.stream().map(PushPortRule::getLv1ServeIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getName()));



        List<ListResp> listResps = BeanCopyUtil.copyListProperties(pushPortRuleList, ListResp.class, (s, t) -> {



            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }
            if (StringUtils.equals(s.getLv1ServeIds(), "all")) {
                t.setLv1ServeNames("全部");
            } else {
                t.setLv1ServeNames(Arrays.stream(s.getLv1ServeIds().split(",")).map(it -> serveInfoMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }


            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }


    /**
     * 启用/禁用端口规则
     * @param rqt
     * @return
     */
    @Override
    public Integer enable(EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushPortRuleApi.enable(rqt);
    }


}
