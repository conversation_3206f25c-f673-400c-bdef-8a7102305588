package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum DistributeType {

    AUTO_RECEIVE("auto_receive", "自动接单"),

    AGREEMENT("new_master_support", "新师傅扶持"),

    NEW_MODEL("new_model", "样板城市"),

    COOPERATION_BUSINESS_MASTER("cooperation_business_master", "合作经营师傅"),

    ENTERPRISE_APPOINT("enterprise_appoint","总包直接指派"),

    NEW_MASTER("new_master", "新师傅"),

    AFTER_VERIFY_NEW_MASTER("after_verify_new_master","新师傅验证后派单"),

    ENTERPRISE_APPOINT_NEW_MASTER("enterprise_appoint_new_master", "总包直接指派新师傅"),


    FULL_TIME_MASTER("full_time_master", "全时师傅派单"),



    ENTERPRISE_APPOINT_FULL_TIME_MASTER("enterprise_appoint_full_time_master", "总包全时派单");





    private final String code;

    private final String desc;


    private static final Map<String, DistributeType> valueMapping = new HashMap<>((int) (DistributeType.values().length / 0.75));

    static {
        for (DistributeType instance : DistributeType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static DistributeType asValue(String code) {
        return valueMapping.get(code);
    }

}
