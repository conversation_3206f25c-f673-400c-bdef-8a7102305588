//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
//import com.wanshifu.master.order.push.mapper.BackendStrategyCombinationSimulateMapper;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.Date;
//import java.util.List;
//
//@Repository
//public class BackendStrategyCombinationSimulateRepository extends BaseRepository<StrategyCombinationSimulate> {
//
//    @Resource
//    private BackendStrategyCombinationSimulateMapper backendStrategyCombinationSimulateMapper;
//
//
//    public List<StrategyCombinationSimulate> selectByStrategyCombinationIds(List<Long> combinationIds) {
//        if (CollectionUtils.isEmpty(combinationIds)) {
//            return Collections.emptyList();
//        }
//        Example example = new Example(StrategyCombinationSimulate.class);
//        example.createCriteria().andIn("strategyCombinationId",combinationIds);
//        return this.selectByExample(example);
//    }
//
//    public StrategyCombinationSimulate selectLatestSimulate(Long strategyCombinationId){
//        return backendStrategyCombinationSimulateMapper.selectLatestSimulate(strategyCombinationId);
//    }
//
//
//    public Long insertSimulate(Long strategyCombinationId, Date simulateTime){
//        StrategyCombinationSimulate simulate = new StrategyCombinationSimulate();
//        simulate.setStrategyCombinationId(strategyCombinationId);
//        simulate.setOrderNum(0);
//        simulate.setSimulatedOrderNum(0);
//        simulate.setSimulateStatus(1);
//        simulate.setSimulateTime(simulateTime);
//        this.insertSelective(simulate);
//        return simulate.getSimulateId();
//    }
//
//
//    public int addSimulatedOrderNum(Long simulateId){
//        return backendStrategyCombinationSimulateMapper.addSimulatedOrderNum(simulateId);
//    }
//
//
//    public int updateOrderNum(Long simulateId,Integer orderNum){
//        StrategyCombinationSimulate simulate = new StrategyCombinationSimulate();
//        simulate.setSimulateId(simulateId);
//        simulate.setOrderNum(orderNum);
//        return this.updateByPrimaryKeySelective(simulate);
//
//    }
//
//    public int finishSimulateStatus(Long simulateId) {
//        StrategyCombinationSimulate simulate = new StrategyCombinationSimulate();
//        simulate.setSimulateId(simulateId);
//        simulate.setSimulateStatus(2);
//        return this.updateByPrimaryKeySelective(simulate);
//    }
//}