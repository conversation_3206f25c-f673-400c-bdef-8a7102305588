package com.wanshifu.master.order.push.service.agentDistributeStrategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.AgentDistributeStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.AgentDistributeRule;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.request.common.GetServeByServeIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GetSubListByDivisionIdReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.resp.agent.TobGroupAgentInfoResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.AgentDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetTobGroupAgentListResp;
import com.wanshifu.master.order.push.domain.response.common.GetServeByServeIdsResp;
import com.wanshifu.master.order.push.domain.response.common.GetSubListByDivisionIdResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import com.wanshifu.master.order.push.service.agentDistributeStrategy.AgentDistributeStrategyService;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.BackendCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AgentDistributeStrategyServiceImpl implements AgentDistributeStrategyService {

    @Resource
    private AgentDistributeStrategyApi agentDistributeStrategyApi;

    @Resource
    private BackendCommonService backendCommonService;

    @Resource
    private ServeCommonService serveCommonService;

    @Resource
    private AddressCommonService addressCommonService;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private IopAccountApi iopAccountApi;


    @Override
    public Integer add(AddAgentDistributeStrategyRqt rqt){
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());


        String serveIdsTemp = rqt.getServeIds();
        if ("tobGroup".equals(rqt.getMasterSourceType())) {
            List<List<Long>> serveIds = JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {
            });
            serveIdsTemp = serveCommonService.getServeIds(serveIds);
            rqt.setServeIdArray(rqt.getServeIds());
        }
        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(serveIdsTemp)
                        .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);

        List<String> serveNameList = respList.stream().map(GetServeByServeIdsResp::getServeName).collect(Collectors.toList());

        rqt.setServeNames(StringUtils.join(serveNameList,","));
        rqt.setServeIds(serveIdsTemp);
        return agentDistributeStrategyApi.add(rqt);
    }

    @Override
    public Integer update(UpdateAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());

        String serveIdsTemp = rqt.getServeIds();
        if ("tobGroup".equals(rqt.getMasterSourceType())) {
            List<List<Long>> serveIds = JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {
            });
            serveIdsTemp = serveCommonService.getServeIds(serveIds);
            rqt.setServeIdArray(rqt.getServeIds());
        }
        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(serveIdsTemp)
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);

        List<String> serveNameList = respList.stream().map(GetServeByServeIdsResp::getServeName).collect(Collectors.toList());

        rqt.setServeNames(StringUtils.join(serveNameList,","));
        rqt.setServeIds(serveIdsTemp);
        return agentDistributeStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agentDistributeStrategyApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agentDistributeStrategyApi.delete(rqt);
    }

    @Override
    public AgentDistributeStrategyDetailResp detail(AgentDistributeStrategyDetailRqt rqt){
        AgentDistributeDetailResp agentDistributeDetailResp = agentDistributeStrategyApi.detail(rqt);
        if(agentDistributeDetailResp == null){
            return null;
        }

        AgentDistributeStrategyDetailResp resp = new AgentDistributeStrategyDetailResp();
        BeanUtils.copyProperties(agentDistributeDetailResp,resp);

        resp.setAgentName(agentDistributeDetailResp.getAgentName());
        if ("tobGroup".equals(agentDistributeDetailResp.getMasterSourceType())) {
            String serveIdArray = agentDistributeDetailResp.getServeIdArray();
            if (Strings.isNullOrEmpty(serveIdArray)) {
                resp.setServeIds("");
                resp.setServiceInfoList(Lists.newArrayList());
            } else {
                resp.setServeIds(serveIdArray);
                Set<Long> serveIdSet = JSON.parseObject(serveIdArray, new TypeReference<List<List<Long>>>(){})
                        .stream().flatMap(List::stream).collect(Collectors.toSet());
                List<ServeBaseInfoResp> serveList = serveCommonService.getServeBaseInfoByServeIdSet(serveIdSet);
                if (CollectionUtils.isNotEmpty(serveList)) {
                    List<String> serviceInfoList = new ArrayList<>();
                    for (ServeBaseInfoResp serveResp : serveList) {
                        String serveIdAndName = serveResp.getServeId() + ":" + serveResp.getName();
                        serviceInfoList.add(serveIdAndName);
                    }
                    resp.setServiceInfoList(serviceInfoList);
                }
            }

        } else {
            Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(agentDistributeDetailResp.getServeIds())
                            .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet());

            GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
            getServeByServeIdsReq.setServeIds(serveIdSet);
            List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);
            List<AgentDistributeStrategyDetailResp.AgentServe> agentServeList = new ArrayList<>();
            respList.forEach(getServeByServeIdsResp -> {
                AgentDistributeStrategyDetailResp.AgentServe agentServe = new AgentDistributeStrategyDetailResp.AgentServe();
                BeanUtils.copyProperties(getServeByServeIdsResp,agentServe);
                agentServeList.add(agentServe);
            });
            resp.setServeList(agentServeList);
        }



        List<Address> addressList = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(agentDistributeDetailResp.getFourthDivisionIds())) {
            List<String> divisionIdList = Arrays.asList(agentDistributeDetailResp.getFourthDivisionIds().split(","));
            List<List<String>> partition = Lists.partition(divisionIdList, 50);
            for (List<String> ids : partition) {
                addressList.addAll(addressCommonService.getDivisionInfoListByDivisionIds(String.join(",", ids)));
            }
        }


        if (CollectionUtil.isNotEmpty(addressList)) {
            List<AgentDistributeStrategyDetailResp.AgentDivision> agentDivisionList = new ArrayList<>();
            addressList.forEach(address -> {
                AgentDistributeStrategyDetailResp.AgentDivision agentDivision = new AgentDistributeStrategyDetailResp.AgentDivision();
                agentDivision.setFourthDivisionId(address.getDivisionId());
                agentDivision.setFourthDivisionName(address.getDivisionName());
                agentDivisionList.add(agentDivision);
            });


            Address address = addressList.get(0);
            resp.setProvinceDivisionId(address.getLv2DivisionId());
            resp.setProvince(address.getLv2DivisionName());

            if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                resp.setCityDivisionId(address.getLv3DivisionId());
                resp.setCity(address.getLv3DivisionName());
            }else{
                resp.setCityDivisionId(address.getLv4DivisionId());
                resp.setCity(address.getLv4DivisionName());
            }

            resp.setDivisionList(agentDivisionList);
        }

        resp.setPushStrategy(agentDistributeDetailResp.getPushStrategy());

        return resp;
    }

    @Override
    public SimplePageInfo<GetAgentListResp> getAgentList(GetAgentListRqt rqt){
        SimplePageInfo<AgentInfo> agentInfoSimplePage = agentDistributeStrategyApi.getAgentList(rqt);
        SimplePageInfo<GetAgentListResp> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPageNum(agentInfoSimplePage.getPageNum());
        simplePageInfo.setPageSize(agentInfoSimplePage.getPageSize());
        simplePageInfo.setPages(agentInfoSimplePage.getPages());
        simplePageInfo.setTotal(agentInfoSimplePage.getTotal());
        List<AgentInfo> agentInfoList = agentInfoSimplePage.getList();
        List<GetAgentListResp> getAgentListRespList = new ArrayList<>();
        agentInfoList.forEach(agentInfo -> {
            GetAgentListResp getAgentListResp = new GetAgentListResp();
            BeanUtils.copyProperties(agentInfo, getAgentListResp);
            getAgentListResp.setAgentStatus(agentInfo.getUseStatus());
            Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(agentInfo.getServeIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet());

            GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
            getServeByServeIdsReq.setServeIds(serveIdSet);
            List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);
            List<GetAgentListResp.AgentServe> agentServeList = new ArrayList<>();
            respList.forEach(getServeByServeIdsResp -> {
                GetAgentListResp.AgentServe agentServe = new GetAgentListResp.AgentServe();
                BeanUtils.copyProperties(getServeByServeIdsResp,agentServe);
                agentServeList.add(agentServe);
            });


            List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(agentInfo.getDivisionId());

            List<GetAgentListResp.AgentDivision> agentDivisionList = new ArrayList<>();

            addressList.forEach(address -> {
                GetAgentListResp.AgentDivision agentDivision = new GetAgentListResp.AgentDivision();
                agentDivision.setThirdDivisionId(address.getDivisionId());
                agentDivision.setThirdDivisionName(address.getDivisionName());

                if (address.getDivisionLevel() == 5) {
                    //东莞这种城市
                    List<GetAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                    GetAgentListResp.AgentFourthDivision agentFourthDivision = new GetAgentListResp.AgentFourthDivision();
                    agentFourthDivision.setFourthDivisionId(address.getDivisionId());
                    agentFourthDivision.setFourthDivisionName(address.getDivisionName());
                    agentFourthDivisionList.add(agentFourthDivision);
                    agentDivision.setFourthDivisionList(agentFourthDivisionList);

                } else if (address.getDivisionLevel() == 4) {
                    //正常的区县这一级地址
                    GetSubListByDivisionIdReq req = new GetSubListByDivisionIdReq();
                    req.setDivisionId(address.getDivisionId());
                    List<GetSubListByDivisionIdResp> subListByDivisionIdRespList = addressCommonService.getSubListByDivisionId(req);
                    if (CollectionUtil.isNotEmpty(subListByDivisionIdRespList)) {
                        List<GetAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                        subListByDivisionIdRespList.forEach(subListByDivisionIdResp -> {
                            GetAgentListResp.AgentFourthDivision agentFourthDivision = new GetAgentListResp.AgentFourthDivision();
                            agentFourthDivision.setFourthDivisionId(subListByDivisionIdResp.getDivisionId());
                            agentFourthDivision.setFourthDivisionName(subListByDivisionIdResp.getDivisionName());
                            agentFourthDivisionList.add(agentFourthDivision);
                        });
                        agentDivision.setFourthDivisionList(agentFourthDivisionList);
                    }

                }
                agentDivisionList.add(agentDivision);

            });


            Address address = addressList.get(0);
            getAgentListResp.setProvinceDivisionId(address.getLv2DivisionId());
            getAgentListResp.setProvince(address.getLv2DivisionName());

            if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                getAgentListResp.setCityDivisionId(address.getLv3DivisionId());
                getAgentListResp.setCity(address.getLv3DivisionName());
            }else{
                getAgentListResp.setCityDivisionId(address.getLv4DivisionId());
                getAgentListResp.setCity(address.getLv4DivisionName());
            }

            List<String> divisionNameList = addressList.stream().map(Address::getDivisionName).collect(Collectors.toList());

            getAgentListResp.setDivisionNames(StringUtils.join(divisionNameList,","));


            getAgentListResp.setServeList(agentServeList);
            getAgentListResp.setDivisionList(agentDivisionList);

            getAgentListRespList.add(getAgentListResp);
        });

        simplePageInfo.setList(getAgentListRespList);

        return simplePageInfo;

    }

    @Override
    public SimplePageInfo<GetTobGroupAgentListResp> tobGroupAgentList(TobGroupAgentInfoRqt rqt) {
        SimplePageInfo<TobGroupAgentInfoResp> tobGroupAgentInfoSimplePage = agentDistributeStrategyApi.getTobGroupAgentList(rqt);
        SimplePageInfo<GetTobGroupAgentListResp> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPageNum(tobGroupAgentInfoSimplePage.getPageNum());
        simplePageInfo.setPageSize(tobGroupAgentInfoSimplePage.getPageSize());
        simplePageInfo.setPages(tobGroupAgentInfoSimplePage.getPages());
        simplePageInfo.setTotal(tobGroupAgentInfoSimplePage.getTotal());


        List<TobGroupAgentInfoResp> tobGroupAgentInfoList = tobGroupAgentInfoSimplePage.getList();
        if (CollectionUtil.isEmpty(tobGroupAgentInfoList)) {
            simplePageInfo.setList(new ArrayList<>());
            return simplePageInfo;
        }
        List<GetTobGroupAgentListResp> getAgentListRespList = new ArrayList<>();
        tobGroupAgentInfoList.forEach(tobGroupAgentInfo -> {
            GetTobGroupAgentListResp getTobGroupAgentListResp = new GetTobGroupAgentListResp();
            getTobGroupAgentListResp.setAgentId(tobGroupAgentInfo.getMasterId());
            getTobGroupAgentListResp.setAgentName(tobGroupAgentInfo.getMasterName().concat("（B端团队师傅）"));
            getTobGroupAgentListResp.setGroupMasterId(tobGroupAgentInfo.getMasterId());
            getTobGroupAgentListResp.setGroupMasterName(tobGroupAgentInfo.getMasterName());
            getTobGroupAgentListResp.setGroupMasterId(tobGroupAgentInfo.getMasterId());
            getTobGroupAgentListResp.setPhone(tobGroupAgentInfo.getPhone());
            getTobGroupAgentListResp.setAgentStatus(tobGroupAgentInfo.getUseStatus());

            List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(tobGroupAgentInfo.getServiceFourthDivisionIds());

            if (CollectionUtil.isNotEmpty(addressList)) {

                Set<Long> thirdDivisionIdSet = new HashSet<>();
                Set<String> thirdDivisionNameSet = new HashSet<>();
                addressList.forEach(address -> {

                    if (address.getLv4DivisionId() == 0) {
                        thirdDivisionIdSet.add(address.getLv5DivisionId());
                        thirdDivisionNameSet.add(address.getLv5DivisionName());
                    }else {
                        thirdDivisionIdSet.add(address.getLv4DivisionId());
                        thirdDivisionNameSet.add(address.getLv4DivisionName());
                    }

                });
                getTobGroupAgentListResp.setDivisionNames(StringUtils.join(thirdDivisionNameSet, ","));

                List<Address> thirdAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(thirdDivisionIdSet, ","));

                Map<Long, Address> thirdAddressMap = thirdAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

                List<GetTobGroupAgentListResp.AgentDivision> divisionList = Lists.newArrayList();

                for (Long thirdDivisionId : thirdDivisionIdSet) {
                    Address thirdAddress = thirdAddressMap.get(thirdDivisionId);
                    if (Objects.isNull(thirdAddress)) {
                        continue;
                    }

                    GetTobGroupAgentListResp.AgentDivision agentDivision = new GetTobGroupAgentListResp.AgentDivision();
                    agentDivision.setThirdDivisionId(thirdDivisionId);
                    agentDivision.setThirdDivisionName(thirdAddress.getDivisionName());

                    List<GetTobGroupAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                    if (thirdAddress.getLv4DivisionId() == 0) {
                        //东莞这种地址
                        GetTobGroupAgentListResp.AgentFourthDivision agentFourthDivision = new GetTobGroupAgentListResp.AgentFourthDivision();
                        agentFourthDivision.setFourthDivisionId(thirdAddress.getLv5DivisionId());
                        agentFourthDivision.setFourthDivisionName(thirdAddress.getLv5DivisionName());
                        agentFourthDivision.setSelected(true);
                        agentFourthDivisionList.add(agentFourthDivision);

                    } else {
                        //正常有区县这一级的地址
                        List<Long> selectFourthDivisionIdList = addressList.stream().map(Address::getDivisionId).collect(Collectors.toList());

                        GetSubListByDivisionIdReq getSubListByDivisionIdReq = new GetSubListByDivisionIdReq();
                        getSubListByDivisionIdReq.setDivisionId(thirdDivisionId);

                        List<GetSubListByDivisionIdResp> subListByDivisionIdRespList = addressCommonService.getSubListByDivisionId(getSubListByDivisionIdReq);
                        if (CollectionUtil.isNotEmpty(subListByDivisionIdRespList)) {
                            subListByDivisionIdRespList.forEach(subListByDivisionIdResp -> {
                                GetTobGroupAgentListResp.AgentFourthDivision agentFourthDivision = new GetTobGroupAgentListResp.AgentFourthDivision();
                                agentFourthDivision.setFourthDivisionId(subListByDivisionIdResp.getDivisionId());
                                agentFourthDivision.setFourthDivisionName(subListByDivisionIdResp.getDivisionName());
                                if (selectFourthDivisionIdList.contains(subListByDivisionIdResp.getDivisionId())) {
                                    agentFourthDivision.setSelected(true);
                                } else {
                                    agentFourthDivision.setSelected(false);
                                }
                                agentFourthDivisionList.add(agentFourthDivision);
                            });
                        }
                    }

                    agentDivision.setFourthDivisionList(agentFourthDivisionList);
                    divisionList.add(agentDivision);
                }

                getTobGroupAgentListResp.setDivisionList(divisionList);

                Address address = addressList.get(0);
                getTobGroupAgentListResp.setProvinceDivisionId(address.getLv2DivisionId());
                getTobGroupAgentListResp.setProvince(address.getLv2DivisionName());

                if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                    getTobGroupAgentListResp.setCityDivisionId(address.getLv3DivisionId());
                    getTobGroupAgentListResp.setCity(address.getLv3DivisionName());
                }else{
                    getTobGroupAgentListResp.setCityDivisionId(address.getLv4DivisionId());
                    getTobGroupAgentListResp.setCity(address.getLv4DivisionName());
                }

            }

            getAgentListRespList.add(getTobGroupAgentListResp);
            });


        simplePageInfo.setList(getAgentListRespList);

        return simplePageInfo;
    }

    @Override
    public SimplePageInfo<GetAgentDistributeStrategyListResp> list(GetAgentDistributeStrategyListRqt rqt){
        SimplePageInfo<AgentDistributeStrategy> agentDistributeSimplePageInfo = agentDistributeStrategyApi.list(rqt);

        SimplePageInfo<GetAgentDistributeStrategyListResp> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPageNum(agentDistributeSimplePageInfo.getPageNum());
        simplePageInfo.setPageSize(agentDistributeSimplePageInfo.getPageSize());
        simplePageInfo.setPages(agentDistributeSimplePageInfo.getPages());
        simplePageInfo.setTotal(agentDistributeSimplePageInfo.getTotal());

        List<GetAgentDistributeStrategyListResp> getAgentDistributeStrategyListRespList = new ArrayList<>();
        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeSimplePageInfo.getList();

        agentDistributeStrategyList.forEach(agentDistributeStrategy -> {
            GetAgentDistributeStrategyListResp getAgentDistributeStrategyListResp = new GetAgentDistributeStrategyListResp();
            getAgentDistributeStrategyListResp.setStrategyId(agentDistributeStrategy.getStrategyId());
            getAgentDistributeStrategyListResp.setMasterSourceType(agentDistributeStrategy.getMasterSourceType());
            getAgentDistributeStrategyListResp.setStrategyName(agentDistributeStrategy.getStrategyName());

            getAgentDistributeStrategyListResp.setDistributeRule(AgentDistributeRule.DIRECT_APPOINT.code.equals(agentDistributeStrategy.getDistributeRule()) ? "直接指派" : "定向推送");
            getAgentDistributeStrategyListResp.setAgentName(agentDistributeStrategy.getAgentName());
            getAgentDistributeStrategyListResp.setStrategyStatus(agentDistributeStrategy.getStrategyStatus());

            getAgentDistributeStrategyListResp.setServe(agentDistributeStrategy.getServeNames());

            List<Long> updateAccountIds = agentDistributeStrategyList.stream().map(AgentDistributeStrategy::getUpdateAccountId)
                    .distinct().filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(updateAccountIds)) {
                iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                        .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                        .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
            }
            Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

            getAgentDistributeStrategyListResp.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(agentDistributeStrategy.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            getAgentDistributeStrategyListResp.setCreateTime(agentDistributeStrategy.getCreateTime());
            getAgentDistributeStrategyListResp.setUpdateTime(agentDistributeStrategy.getUpdateTime());


            List<Address> addressList = Lists.newArrayList();
            if (Strings.isNullOrEmpty(agentDistributeStrategy.getFourthDivisionIds())) {
                return;
            }
            List<String> divisionIdList = Arrays.asList(agentDistributeStrategy.getFourthDivisionIds().split(","));
            List<List<String>> partition = Lists.partition(divisionIdList, 50);
            for (List<String> ids : partition) {
                addressList.addAll(addressCommonService.getDivisionInfoListByDivisionIds(String.join(",", ids)));
            }

            if (CollectionUtil.isEmpty(addressList)) {
                return;
            }
            Address address = addressList.get(0);
            getAgentDistributeStrategyListResp.setProvince(address.getLv2DivisionName());

            if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                getAgentDistributeStrategyListResp.setCity(address.getLv3DivisionName());
            }else{
                getAgentDistributeStrategyListResp.setCity(address.getLv4DivisionName());
            }


            Set<String> divisionNameList = Sets.newHashSet();
            for (Address fourthAddress : addressList) {
                if (Strings.isNullOrEmpty(fourthAddress.getLv4DivisionName())) {
                    divisionNameList.add(fourthAddress.getLv5DivisionName());
                } else {
                    divisionNameList.add(fourthAddress.getLv4DivisionName());
                }
            }

            getAgentDistributeStrategyListResp.setDistrict(StringUtils.join(divisionNameList,","));

            getAgentDistributeStrategyListRespList.add(getAgentDistributeStrategyListResp);
        });


        simplePageInfo.setList(getAgentDistributeStrategyListRespList);
        return simplePageInfo;
    }

    @Override
    public GetAgentListResp getAgentById(GetAgentByIdRqt rqt) {
        AgentInfo agentInfo = agentDistributeStrategyApi.getAgentById(rqt.getAgentId());
        if (Objects.isNull(agentInfo)) {
            return null;
        }
        GetAgentListResp getAgentListResp = new GetAgentListResp();
        BeanUtils.copyProperties(agentInfo, getAgentListResp);
        getAgentListResp.setAgentStatus(agentInfo.getUseStatus());
        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(agentInfo.getServeIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);
        List<GetAgentListResp.AgentServe> agentServeList = new ArrayList<>();
        respList.forEach(getServeByServeIdsResp -> {
            GetAgentListResp.AgentServe agentServe = new GetAgentListResp.AgentServe();
            BeanUtils.copyProperties(getServeByServeIdsResp, agentServe);
            agentServeList.add(agentServe);
        });


        List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(agentInfo.getDivisionId());

        List<GetAgentListResp.AgentDivision> agentDivisionList = new ArrayList<>();

        addressList.forEach(address -> {
            GetAgentListResp.AgentDivision agentDivision = new GetAgentListResp.AgentDivision();
            agentDivision.setThirdDivisionId(address.getDivisionId());
            agentDivision.setThirdDivisionName(address.getDivisionName());

            if (address.getDivisionLevel() == 5) {
                //东莞这种城市
                List<GetAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                GetAgentListResp.AgentFourthDivision agentFourthDivision = new GetAgentListResp.AgentFourthDivision();
                agentFourthDivision.setFourthDivisionId(address.getDivisionId());
                agentFourthDivision.setFourthDivisionName(address.getDivisionName());
                agentFourthDivisionList.add(agentFourthDivision);
                agentDivision.setFourthDivisionList(agentFourthDivisionList);

            } else if (address.getDivisionLevel() == 4) {
                //正常的区县这一级地址
                GetSubListByDivisionIdReq req = new GetSubListByDivisionIdReq();
                req.setDivisionId(address.getDivisionId());
                List<GetSubListByDivisionIdResp> subListByDivisionIdRespList = addressCommonService.getSubListByDivisionId(req);
                if (CollectionUtil.isNotEmpty(subListByDivisionIdRespList)) {
                    List<GetAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                    subListByDivisionIdRespList.forEach(subListByDivisionIdResp -> {
                        GetAgentListResp.AgentFourthDivision agentFourthDivision = new GetAgentListResp.AgentFourthDivision();
                        agentFourthDivision.setFourthDivisionId(subListByDivisionIdResp.getDivisionId());
                        agentFourthDivision.setFourthDivisionName(subListByDivisionIdResp.getDivisionName());
                        agentFourthDivisionList.add(agentFourthDivision);
                    });
                    agentDivision.setFourthDivisionList(agentFourthDivisionList);
                }

            }
            agentDivisionList.add(agentDivision);

        });


        Address address = addressList.get(0);
        getAgentListResp.setProvinceDivisionId(address.getLv2DivisionId());
        getAgentListResp.setProvince(address.getLv2DivisionName());

        if (address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0) {
            getAgentListResp.setCityDivisionId(address.getLv3DivisionId());
            getAgentListResp.setCity(address.getLv3DivisionName());
        } else {
            getAgentListResp.setCityDivisionId(address.getLv4DivisionId());
            getAgentListResp.setCity(address.getLv4DivisionName());
        }

        List<String> divisionNameList = addressList.stream().map(Address::getDivisionName).collect(Collectors.toList());

        getAgentListResp.setDivisionNames(StringUtils.join(divisionNameList, ","));


        getAgentListResp.setServeList(agentServeList);
        getAgentListResp.setDivisionList(agentDivisionList);

        return getAgentListResp;
    }

    @Override
    public GetTobGroupAgentListResp getTobGroupAgentById(GetTobGroupAgentByIdRqt rqt) {
        TobGroupAgentInfoResp tobGroupAgentInfo = agentDistributeStrategyApi.getTobGroupAgentById(rqt.getAgentId());
        if (Objects.isNull(tobGroupAgentInfo)) {
            return null;
        }
        GetTobGroupAgentListResp getTobGroupAgentListResp = new GetTobGroupAgentListResp();
        getTobGroupAgentListResp.setAgentId(tobGroupAgentInfo.getMasterId());
        getTobGroupAgentListResp.setAgentName(tobGroupAgentInfo.getMasterName().concat("（B端团队师傅）"));
        getTobGroupAgentListResp.setGroupMasterId(tobGroupAgentInfo.getMasterId());
        getTobGroupAgentListResp.setGroupMasterName(tobGroupAgentInfo.getMasterName());
        getTobGroupAgentListResp.setGroupMasterId(tobGroupAgentInfo.getMasterId());
        getTobGroupAgentListResp.setPhone(tobGroupAgentInfo.getPhone());
        getTobGroupAgentListResp.setAgentStatus(tobGroupAgentInfo.getUseStatus());

        List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(tobGroupAgentInfo.getServiceFourthDivisionIds());

        if (CollectionUtil.isNotEmpty(addressList)) {

            Set<Long> thirdDivisionIdSet = new HashSet<>();
            Set<String> thirdDivisionNameSet = new HashSet<>();
            addressList.forEach(address -> {

                if (address.getLv4DivisionId() == 0) {
                    thirdDivisionIdSet.add(address.getLv5DivisionId());
                    thirdDivisionNameSet.add(address.getLv5DivisionName());
                } else {
                    thirdDivisionIdSet.add(address.getLv4DivisionId());
                    thirdDivisionNameSet.add(address.getLv4DivisionName());
                }

            });
            getTobGroupAgentListResp.setDivisionNames(StringUtils.join(thirdDivisionNameSet, ","));

            List<Address> thirdAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(thirdDivisionIdSet, ","));

            Map<Long, Address> thirdAddressMap = thirdAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

            List<GetTobGroupAgentListResp.AgentDivision> divisionList = Lists.newArrayList();

            for (Long thirdDivisionId : thirdDivisionIdSet) {
                Address thirdAddress = thirdAddressMap.get(thirdDivisionId);
                if (Objects.isNull(thirdAddress)) {
                    continue;
                }

                GetTobGroupAgentListResp.AgentDivision agentDivision = new GetTobGroupAgentListResp.AgentDivision();
                agentDivision.setThirdDivisionId(thirdDivisionId);
                agentDivision.setThirdDivisionName(thirdAddress.getDivisionName());

                List<GetTobGroupAgentListResp.AgentFourthDivision> agentFourthDivisionList = new ArrayList<>();
                if (thirdAddress.getLv4DivisionId() == 0) {
                    //东莞这种地址
                    GetTobGroupAgentListResp.AgentFourthDivision agentFourthDivision = new GetTobGroupAgentListResp.AgentFourthDivision();
                    agentFourthDivision.setFourthDivisionId(thirdAddress.getLv5DivisionId());
                    agentFourthDivision.setFourthDivisionName(thirdAddress.getLv5DivisionName());
                    agentFourthDivision.setSelected(true);
                    agentFourthDivisionList.add(agentFourthDivision);

                } else {
                    //正常有区县这一级的地址

                    GetSubListByDivisionIdReq getSubListByDivisionIdReq = new GetSubListByDivisionIdReq();
                    getSubListByDivisionIdReq.setDivisionId(thirdDivisionId);

                    List<GetSubListByDivisionIdResp> subListByDivisionIdRespList = addressCommonService.getSubListByDivisionId(getSubListByDivisionIdReq);
                    if (CollectionUtil.isNotEmpty(subListByDivisionIdRespList)) {
                        subListByDivisionIdRespList.forEach(subListByDivisionIdResp -> {
                            GetTobGroupAgentListResp.AgentFourthDivision agentFourthDivision = new GetTobGroupAgentListResp.AgentFourthDivision();
                            agentFourthDivision.setFourthDivisionId(subListByDivisionIdResp.getDivisionId());
                            agentFourthDivision.setFourthDivisionName(subListByDivisionIdResp.getDivisionName());
                            agentFourthDivisionList.add(agentFourthDivision);
                        });
                    }
                }

                agentDivision.setFourthDivisionList(agentFourthDivisionList);
                divisionList.add(agentDivision);
            }

            getTobGroupAgentListResp.setDivisionList(divisionList);

            Address address = addressList.get(0);
            getTobGroupAgentListResp.setProvinceDivisionId(address.getLv2DivisionId());
            getTobGroupAgentListResp.setProvince(address.getLv2DivisionName());

            if (address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0) {
                getTobGroupAgentListResp.setCityDivisionId(address.getLv3DivisionId());
                getTobGroupAgentListResp.setCity(address.getLv3DivisionName());
            } else {
                getTobGroupAgentListResp.setCityDivisionId(address.getLv4DivisionId());
                getTobGroupAgentListResp.setCity(address.getLv4DivisionName());
            }

        }
        return getTobGroupAgentListResp;
    }

}
