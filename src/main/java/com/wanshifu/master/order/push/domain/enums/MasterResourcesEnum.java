package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum MasterResourcesEnum {

    AUTO_RECEIVE("auto_receive", "自动接单师傅（普通师傅）"),

    AGREEMENT("agreement", "协议师傅"),

    AGENT("agent", "合作商师�?),

    NEW_MASTER("new_master", "验证前新师傅"),

    COOPERATION_BUSINESS_MASTER("cooperation_business_master", "合作经营师傅"),

    NEW_MODEL("new_model", "样板城市师傅"),

    FULL_TIME_MASTER("full_time_master", "全时师傅"),

    AFTER_VERIFY_NEW_MASTER("after_verify_new_master", "验证后新师傅");







    private final String code;

    private final String desc;


    private static final Map<String, MasterResourcesEnum> valueMapping = new HashMap<>((int) (MasterResourcesEnum.values().length / 0.75));

    static {
        for (MasterResourcesEnum instance : MasterResourcesEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static MasterResourcesEnum asValue(String code) {
        return valueMapping.get(code);
    }

}
