package com.wanshifu.master.order.push.service.normalOrderDistributeStrategy;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface NormalOrderDistributeStrategyService {


    int create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);


}
