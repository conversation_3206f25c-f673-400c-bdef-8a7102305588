package com.wanshifu.master.order.push.domain.response.common;

import com.wanshifu.master.order.push.domain.response.address.AddressListResp;
import lombok.Data;

import java.util.List;

@Data
public class GetSubListByDivisionIdResp {

    private Long divisionId;
    private String divisionName;
    private Integer divisionLevel;

    public GetSubListByDivisionIdResp(Long divisionId, String divisionName,Integer divisionLevel) {
        this.divisionId = divisionId;
        this.divisionName = divisionName;
        this.divisionLevel = divisionLevel;
    }
}
