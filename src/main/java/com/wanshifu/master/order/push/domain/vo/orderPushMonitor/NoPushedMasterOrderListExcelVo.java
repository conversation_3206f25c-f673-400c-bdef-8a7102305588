package com.wanshifu.master.order.push.domain.vo.orderPushMonitor;


import lombok.Data;

import java.util.Date;


@Data
public class NoPushedMasterOrderListExcelVo {

    /**
     * 推单时间
     */
    private Date pushTime;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String street;

//    /**
//     * 服务类目
//     */
//    @Excel(name = "服务类目", width = 15,orderNum = "6")
//    private String serveCategory;

    /**
     * 一级服务类目
     */
    private String level1NameServeCategory;

    /**
     * 二级服务类目
     */
    private String level2NameServeCategory;

    /**
     * 三级服务类目
     */
    private String level3NameServeCategory;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 下单模式
     */
    private String appointType;

    /**
     * 推送路由
     */
    private String pushStrategy;

    /**
     * 初筛师傅数
     */
    private Integer baseSelectMasterNum;

    /**
     * 召回后师傅数
     */
    private Integer masterNumAfterFilter;


}
