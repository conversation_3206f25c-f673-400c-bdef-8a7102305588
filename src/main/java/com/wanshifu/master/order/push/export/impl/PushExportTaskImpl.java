package com.wanshifu.master.order.push.export.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushExportTaskApi;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.domain.request.export.PushExportTaskListRqt;
import com.wanshifu.master.order.push.domain.response.export.PushExportTaskResp;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.ListPushExportTaskRqt;
import com.wanshifu.master.order.push.export.PushExportTaskService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 15:30
 */
@Service
public class PushExportTaskImpl implements PushExportTaskService {

    @Resource
    private PushExportTaskApi pushExportTaskApi;

    @Override
    public SimplePageInfo<PushExportTaskResp> list(PushExportTaskListRqt rqt) {
        SimplePageInfo<PushExportTaskResp> pushExportTaskRespSimplePageInfo = new SimplePageInfo<>();

        ListPushExportTaskRqt listPushExportTaskRqt = new ListPushExportTaskRqt();
        listPushExportTaskRqt.setPageNum(rqt.getPageNum());
        listPushExportTaskRqt.setPageSize(rqt.getPageSize());
        listPushExportTaskRqt.setCreateTimeStart(rqt.getCreateTimeStart());
        listPushExportTaskRqt.setCreateTimeEnd(rqt.getCreateTimeEnd());

        SimplePageInfo<PushExportTask> list = pushExportTaskApi.listPushExportTask(listPushExportTaskRqt);
        if (Objects.isNull(list)) {
            return pushExportTaskRespSimplePageInfo;
        }
        pushExportTaskRespSimplePageInfo.setPageNum(list.getPageNum());
        pushExportTaskRespSimplePageInfo.setPageSize(list.getPageSize());
        pushExportTaskRespSimplePageInfo.setTotal(list.getTotal());
        pushExportTaskRespSimplePageInfo.setPages(list.getPages());
        if (CollectionUtil.isEmpty(list.getList())) {
            pushExportTaskRespSimplePageInfo.setList(new ArrayList<>());

        } else {
            pushExportTaskRespSimplePageInfo.setList(list.getList().stream().map(pushExportTask -> BeanUtil.toBean(pushExportTask, PushExportTaskResp.class)).collect(Collectors.toList()));
        }

        return pushExportTaskRespSimplePageInfo;
    }
}
