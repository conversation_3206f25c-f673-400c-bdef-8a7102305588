package com.wanshifu.master.order.push.domain.vo.orderpush;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18 11:13
 */
@Data
public class OrderPushRecordDetailVo {


    /**
     * 订单ID
     */
    @JSONField(name = "order_id")
    private Long orderId;

    /**
     * 师傅ID
     */
    @JSONField(name = "master_id")
    private Long masterId;


    /**
     * 推送时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss",name = "push_time")
    private Date pushTime;


    /**
     * 师傅第一次查看待报价订单详情时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss",name = "first_view_time")
    private Date firstViewTime;

    /**
     * 报价时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss",name = "offer_time")
    private Date offerTime;

    /**
     * 推单距离(当时推单师傅和客户得距离),单位米
     */
    @JSONField(name = "push_distance")
    private Long pushDistance;

    /**
     * 是否按照师傅技能推送 0:否,1:是
     */
    @JSONField(name = "according_technology_push_flag")
    private Integer accordingTechnologyPushFlag;


}
