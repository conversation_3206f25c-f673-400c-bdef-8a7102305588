package com.wanshifu.master.order.push.domain.po;

import javax.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 
 */
@Data
@ToString
@Table(name = "score_item")
public class ScoreItem {

    /**
     * 注解id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "item_id")
    private Long itemId;

    /**
     * 匹配项编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 匹配项名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 匹配项描述
     */
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 赋值类型
     */
    @Column(name = "value_type")
    private String valueType;

    /**
     * 匹配项条件表达式
     */
    @Column(name = "feature_expression")
    private String featureExpression;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}