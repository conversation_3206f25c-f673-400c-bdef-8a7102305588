package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 指派类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum RoutingTypeEnum {

    /**
     * 直接雇佣
     */
    COMMON_ROUTING("common_routing","常用路由"),

    STANDBY_ROUTING("standby_routing","备用路由");

    public final String code;

    public final String name;

    RoutingTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, RoutingTypeEnum> valueMapping = new HashMap<>((int) (RoutingTypeEnum.values().length / 0.75));

    static {
        for (RoutingTypeEnum instance : RoutingTypeEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static RoutingTypeEnum asValue(String value) {
        return valueMapping.get(value);
    }

    public static Boolean isInclude(Integer value) {
        if(Objects.isNull(value)) {
            return false;
        }
        for (RoutingTypeEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

}
