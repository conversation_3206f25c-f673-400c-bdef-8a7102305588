package com.wanshifu.master.order.push.service.orderRoutingStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.notice.domains.enums.PushNoticeChannelType;
import com.wanshifu.master.notice.domains.enums.PushNoticeGroupType;
import com.wanshifu.master.notice.domains.enums.PushNoticeTarget;
import com.wanshifu.master.notice.domains.po.PushNoticeStrategyCombination;
import com.wanshifu.master.order.push.api.OrderMatchRoutingApi;
import com.wanshifu.master.order.push.api.OrderRoutingStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.OrderFromEnum;
import com.wanshifu.master.order.push.domain.enums.OrderTagEnum;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.orderRoutingStrategy.OrderRoutingStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderRoutingStrategyServiceImpl implements OrderRoutingStrategyService {

    @Resource
    private OrderRoutingStrategyApi orderRoutingStrategyApi;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private OrderMatchRoutingApi orderMatchRoutingApi;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private AddressCommonService addressCommonService;

//    @Resource
//    private AuthHandler authHandler;


    @Override
    public int create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        orderRoutingStrategyApi.create(rqt);
        return 1;
    }

    @Override
    public int update(UpdateRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderRoutingStrategyApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt){
        OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyApi.detail(rqt);
        if(orderRoutingStrategy == null){
            return null;
        }
        DetailResp resp = new DetailResp();
        BeanUtils.copyProperties(orderRoutingStrategy,resp);
        com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt detailRqt = new com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt();
        detailRqt.setRoutingId(orderRoutingStrategy.getMatchRoutingId());
        OrderMatchRouting orderMatchRouting = orderMatchRoutingApi.detail(detailRqt);
        resp.setMatchRoutingName(orderMatchRouting.getRoutingName());
        return resp;
    }


    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){

        SimplePageInfo<OrderRoutingStrategy> simplePageInfo = orderRoutingStrategyApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderRoutingStrategy> orderRoutingStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = orderRoutingStrategyList.stream().map(OrderRoutingStrategy::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(",")))
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        //城市地址
        String cityIdsStr = orderRoutingStrategyList.stream().map(OrderRoutingStrategy::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        List<Address> divisionInfoListByDivisionIds = addressCommonService.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));


        List<Long> updateAccountIds = orderRoutingStrategyList.stream().map(OrderRoutingStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(orderRoutingStrategyList, ListResp.class, (s, t) -> {

            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(","))
                    .map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it))
                    .collect(Collectors.joining(",")));

            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }
            t.setStrategyStatus(s.getStrategyStatus());
            t.setStrategyStatusStr(s.getStrategyStatus() == 1 ? "启用" : "禁用");
            t.setOrderTag(OrderTagEnum.asValue(s.getOrderTag()).name);
            OrderFromEnum orderFromEnum = OrderFromEnum.asValue(s.getOrderFrom());
            t.setOrderFrom(orderFromEnum != null ? orderFromEnum.name : "-");

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

    @Override
    public Integer delete(DeleteRqt rqt){
        return orderRoutingStrategyApi.delete(rqt);
    }


    @Override
    public Integer enable(EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderRoutingStrategyApi.enable(rqt);
    }
}
