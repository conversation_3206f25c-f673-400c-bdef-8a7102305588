package com.wanshifu.master.order.push.domain.response.orderSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;

@Data
public class OrderSelectStrategyDetailResp {

    /**
     * 策略id
     */
    private Integer strategyId;

    /**
     * 业务线id
     */
    private Long businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;


    private String orderFrom;


    private String masterResources;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 筛选策略
     */
    private SelectStrategyVo selectStrategy;


    @Data
    public static class SelectStrategyVo{

        @NotNull
        private AppointGroupVo appointGroup;


//        @NotNull
//        private SelectRuleVo selectRule;

        private List<RuleItem> ruleList;

    }

    @Data
    public static class AppointGroupVo{

        @NotNull
        private Integer isAppointGroup;


        @Valid
        private List<GroupItemVo> itemList;

    }




//    @Data
//    public static class SelectRuleVo{
//
//        /**
//         * 或且关系
//         */
//        @NotEmpty
//        @ValueIn("and,or")
//        private String condition;
//
//        @NotEmpty
//        private List<RuleItemVo> itemList;
//
//    }


//    @Data
//    public static class RuleItemVo{
//
//        /**
//         * master_group: 师傅人群，master_quota: 师傅指标
//         */
//        @NotEmpty
//        private String itemType;
//
//        private String itemTitle;
//
//        /**
//         * 规则项名称
//         */
//        @NotEmpty
//        private String itemName;
//
//        /**
//         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
//         */
//        @NotEmpty
//        @ValueIn("in,not_in,>,<,=,<=,>=")
//        private String term;
//
//        /**
//         * 规则项值
//         */
//        @NotEmpty
//        private String itemValue;
//
//        private List<TermItem> termList;
//
//        private List<ValueItem> valueList;
//    }


    @Data
    public static class GroupItemVo{

        /**
         * master_group: 师傅人群，master_quota: 师傅指标
         */
        @NotEmpty
        private String itemType;


        private String itemTitle;



        /**
         * 规则项名称
         */
        @NotEmpty
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
         */
        @NotEmpty
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        /**
         * 规则项值
         */
        @NotEmpty
        private String itemValue;

        private List<TermItem> termList;

        private List<ValueItem> valueList;

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }


    @Data
    public static class RuleItem {
        private String ruleName;
        private OpenCondition openCondition;
        private FilterRule filterRule;
    }

    @Data
    public static class FilterRule {
        private String condition;
        private List<FilterRuleItem> itemList;
    }
    @Data
    public static class OpenCondition {
        private String condition;
        private List<OpenConditionItem> itemList;
    }

    @Data
    public static class FilterRuleItem {
        private String itemType;
        private String itemName;
        private String term;
        private String itemValue;

        private String itemTitle;
        private List<TermItem> termList;
        private List<ValueItem> valueList;

    }

    @Data
    public static class OpenConditionItem {
        private String itemName;
        private String term;
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;
    }


}
