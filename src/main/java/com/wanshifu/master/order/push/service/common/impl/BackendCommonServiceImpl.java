package com.wanshifu.master.order.push.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.BigdataGroupListReq;
import com.wanshifu.master.order.push.domain.request.common.GetGroupByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GetServeByServeIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GroupListReq;
import com.wanshifu.master.order.push.domain.request.common.MasterQuotaReq;
import com.wanshifu.master.order.push.domain.request.common.ServeListReq;
import com.wanshifu.master.order.push.domain.request.common.ServeReq;
import com.wanshifu.master.order.push.domain.response.common.*;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaValueRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetScoreItemValueRqt;
import com.wanshifu.master.order.push.domain.rqt.common.MasterItemRqt;
import com.wanshifu.master.order.push.domain.rqt.common.MasterQuotaRqt;
import com.wanshifu.master.order.push.service.common.BackendCommonService;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.sort.domains.api.response.common.OrderSortItemListResp;
import com.wanshifu.master.order.sort.service.api.CommonServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.TreeServe;
import com.wanshifu.order.config.domains.po.Serve;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:45
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BackendCommonServiceImpl implements BackendCommonService {
    private final MasterBigDataOpenApi masterBigDataOpenApi;
    private final GoodsCommonService goodsCommonService;
    private final ServeCommonService serveCommonService;
    private final CommonServiceApi commonServiceApi;
    private final CommonApi commonApi;

    @Override
    public SimplePageInfo<UserGroupListResp> userGroupList(GroupListReq rqt) {
        return this.getGroupList(rqt, 1);
    }

    @Override
    public SimplePageInfo<UserGroupListResp> masterGroupList(GroupListReq rqt) {
        Integer businessLineId = rqt.getBusinessLineId();
        return this.getGroupList(rqt, Objects.nonNull(businessLineId) && businessLineId == 999 ? 4: 2);
    }

    /**
     * @param rqt
     * @param personaId 1:用户人群 2:师傅人群
     * @return
     */
    private SimplePageInfo<UserGroupListResp> getGroupList(GroupListReq rqt, Integer personaId) {

        String groupName = rqt.getGroupName();
        Integer pageSize = rqt.getPageSize();
        Integer pageNum = rqt.getPageNum();
        BigdataGroupListReq bigdataGroupListReq = new BigdataGroupListReq();
        bigdataGroupListReq.setGroupName(groupName);
        bigdataGroupListReq.setPersonaId(personaId);
        bigdataGroupListReq.setPageNum(pageNum);
        bigdataGroupListReq.setPageSize(pageSize);
        BigdataGetAllGroupListForPageResp<BigdataGetAllGroupListForPageResp.GroupInfo> allGroupListForPage = masterBigDataOpenApi.getAllGroupListForPage(bigdataGroupListReq);
        List<UserGroupListResp> userGroupListResps = BeanCopyUtil.copyListProperties(Optional.ofNullable(allGroupListForPage)
                        .map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()), UserGroupListResp.class,
                (s, t) -> t.setSystemName(s.getAppIdName()));
        int total = Optional.ofNullable(allGroupListForPage).map(BigdataGetAllGroupListForPageResp::getTotal).orElse(0);

        SimplePageInfo<UserGroupListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        if (total == 0) {
            return new SimplePageInfo<>();
        }

        if(CollectionUtils.isNotEmpty(userGroupListResps) && personaId != 1){
            String groupType = personaId == 2 ? "tob" : "toc";
            userGroupListResps.forEach(resp -> resp.setGroupType(groupType));
        }
        listRespSimplePageInfo.setPages((int) Math.ceil(total * 1.0 / pageSize));
        listRespSimplePageInfo.setPageNum(pageNum);
        listRespSimplePageInfo.setTotal(total);
        listRespSimplePageInfo.setPageSize(pageSize);
        listRespSimplePageInfo.setList(userGroupListResps);
        return listRespSimplePageInfo;
    }

    @Override
    public SimplePageInfo<MasterQuotaResp> masterQuota(MasterQuotaReq rqt) {
        String quotaName = rqt.getQuotaName();
        Page<?> startPage = PageHelper.startPage(rqt.pageNum, rqt.pageSize);
        MasterQuotaRqt masterQuotaRqt = new MasterQuotaRqt();
        masterQuotaRqt.setQuotaName(quotaName);
        masterQuotaRqt.setPageSize(rqt.getPageSize());
        masterQuotaRqt.setPageNum(rqt.getPageNum());
        masterQuotaRqt.setBusinessLineId(rqt.getBusinessLineId());
        SimplePageInfo<MasterQuota> simplePageInfo = commonApi.masterQuota(masterQuotaRqt);
        List<MasterQuota> masterQuotas = simplePageInfo.getList();
        // valueType值类型：range_value: 度量，enum_value: 枚举
        List<Long> enumMasterQuotaIds = masterQuotas.stream().filter(it -> StringUtils.equals("enum_value", it.getValueType())).
                map(MasterQuota::getQuotaId).collect(Collectors.toList());

        List<MasterQuotaValue> masterQuotaValues = Collections.emptyList();
        if(CollectionUtils.isNotEmpty(enumMasterQuotaIds)){
            GetMasterQuotaValueRqt getMasterQuotaValueRqt = new GetMasterQuotaValueRqt();
            getMasterQuotaValueRqt.setMasterQuotaIdList(enumMasterQuotaIds);
            masterQuotaValues = commonApi.getMasterQuotaValue(getMasterQuotaValueRqt);
        }

        Map<Long, List<MasterQuotaValue>> masterQuotaValuesMap = masterQuotaValues.stream().collect(Collectors.groupingBy(MasterQuotaValue::getMasterQuotaId));

        List<MasterQuotaResp> respList = masterQuotas.stream().map(it -> {
            MasterQuotaResp masterQuotaResp = new MasterQuotaResp(it.getQuotaCode(), it.getQuotaName(), it.getQuotaDesc(), it.getValueType());
            if (StringUtils.equals(it.getValueType(), "enum_value")) {
                List<MasterQuotaValue> quotaValueList = masterQuotaValuesMap.getOrDefault(it.getQuotaId(), Collections.emptyList());
                List<MasterQuotaResp.EnumValue> enumValues = BeanCopyUtil.copyListProperties(quotaValueList, MasterQuotaResp.EnumValue.class, null);
                masterQuotaResp.setEnumValueList(enumValues);
            }
            return masterQuotaResp;
        }).collect(Collectors.toList());
        SimplePageInfo<MasterQuotaResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(respList);
        listRespSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        listRespSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        listRespSimplePageInfo.setTotal(simplePageInfo.getTotal());
        listRespSimplePageInfo.setPages(simplePageInfo.getPages());
        return listRespSimplePageInfo;
    }

    @Override
    public List<MasterItemListResp> masterItemList(String itemName) {
        MasterItemRqt masterItemRqt = new MasterItemRqt();
        masterItemRqt.setItemName(itemName);
        List<ScoreItem> scoreItems = commonApi.masterItemList(masterItemRqt);
        List<Long> scoreItemIds = scoreItems.stream().filter(it -> StringUtils.equals("enum_value", it.getValueType()))
                .map(ScoreItem::getItemId).collect(Collectors.toList());

        List<ScoreItemValue> scoreItemValues = Collections.emptyList();
        if(CollectionUtils.isNotEmpty(scoreItemIds)){
            GetScoreItemValueRqt getScoreItemValueRqt =  new GetScoreItemValueRqt();
            getScoreItemValueRqt.setScoreItemIdList(scoreItemIds);
            scoreItemValues = commonApi.getScoreItemValue(getScoreItemValueRqt);
        }


        Map<Long, List<ScoreItemValue>> scoreItemValuesMap = scoreItemValues.stream().collect(Collectors.groupingBy(ScoreItemValue::getScoreItemId));
        return scoreItems.stream().map(it -> {
            MasterItemListResp masterItemListResp = new MasterItemListResp(it.getItemCode(), it.getItemName(), it.getItemDesc(), it.getValueType());
            if (StringUtils.equals(it.getValueType(), "enum_value")) {
                List<ScoreItemValue> values = scoreItemValuesMap.getOrDefault(it.getItemId(), Collections.emptyList());
                List<MasterItemListResp.EnumValue> enumValues = BeanCopyUtil.copyListProperties(values, MasterItemListResp.EnumValue.class, (s, t) -> {
                    t.setName(s.getName());
                    t.setValue(s.getCode());
                });
                masterItemListResp.setValueList(enumValues);
            }
            return masterItemListResp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OrderSortItemListResp> orderSortItemList(String itemName) {
        return commonServiceApi.orderSortItemList(itemName);
    }

    @Override
    public List<CategoryListResp> categoryList(Integer businessLineId) {
        List<Integer> businessLineIds = Lists.newArrayList();
        if (businessLineId == null) {
            businessLineIds = Lists.newArrayList(1, 2, 3);
        } else if (businessLineId == 1) {
            businessLineIds = Lists.newArrayList(1, 3);
        } else if (businessLineId == 2 || businessLineId == 999) {
            businessLineIds = Lists.newArrayList(2);
        }
        List<CategoryListResp> category = goodsCommonService.getCategoryByBusinessLineIds(businessLineIds);
        Collections.sort(category, Comparator.comparing(CategoryListResp::getGoodsId));
        return category;
    }

    @Override
    public List<ServeListResp> serveList(ServeListReq rqt) {
        List<Long> categoryIds = rqt.getCategoryIds();
        Long businessLineId = rqt.getBusinessLineId();

        List<TreeServe> treeServe = serveCommonService.getTreeServe(businessLineId);
        if (businessLineId == 1) {
            //需要查询成品和创新业务线的服务
            treeServe.addAll(serveCommonService.getTreeServe(3L));
        }
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            treeServe = treeServe.stream().filter(it -> categoryIds.contains(it.getGoodsId())).collect(Collectors.toList());
        }
        return this.getRegionEntity(treeServe);
    }

    private List<ServeListResp> getRegionEntity(List<TreeServe> serveList) {
        if (CollectionUtils.isEmpty(serveList)) {
            return Collections.emptyList();
        }
        return serveList.stream().map(it -> new ServeListResp(it.getServeId(), it.getName(),
                this.getRegionEntity(it.getChildren()))).collect(Collectors.toList());
    }

    @Override
    public List<ServeResp> serve(ServeReq rqt) {
        List<Long> categoryIds = rqt.getCategoryIds();
        Long businessLineId = rqt.getBusinessLineId();
        Long parentId = rqt.getParentId();
        businessLineId = businessLineId == 999 ? 2 : businessLineId;
        List<ServeBaseInfoResp> serveBaseInfoResps = serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(Sets.newHashSet(parentId), businessLineId);
        if (businessLineId == 1) {
            serveBaseInfoResps.addAll(serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(Sets.newHashSet(parentId), 3L));
        }
        if (parentId == 0 && CollectionUtils.isNotEmpty(categoryIds)) {
            serveBaseInfoResps = serveBaseInfoResps.stream().filter(it -> categoryIds.contains(it.getGoodsId())).collect(Collectors.toList());
        }
        Map<Long, List<ServeBaseInfoResp>> serveListMap = serveBaseInfoResps.stream().collect(Collectors.groupingBy(ServeBaseInfoResp::getBusinessLineId));
        List<ServeBaseInfoResp> businessLine1Serve = serveListMap.get(1L);
        List<ServeBaseInfoResp> subServe = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(businessLine1Serve)) {
            subServe.addAll(serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(businessLine1Serve.stream().map(ServeBaseInfoResp::getServeId).collect(Collectors.toSet()), 1L));
        }
        List<ServeBaseInfoResp> businessLine2Serve = serveListMap.get(2L);
        if (CollectionUtils.isNotEmpty(businessLine2Serve)) {
            subServe.addAll(serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(businessLine2Serve.stream().map(ServeBaseInfoResp::getServeId).collect(Collectors.toSet()), 2L));
        }
        List<ServeBaseInfoResp> businessLine3Serve = serveListMap.get(3L);
        if (CollectionUtils.isNotEmpty(businessLine3Serve)) {
            subServe.addAll(serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(businessLine3Serve.stream().map(ServeBaseInfoResp::getServeId).collect(Collectors.toSet()), 3L));
        }
        Map<Long, Long> subServeMap = subServe.stream().collect(Collectors.groupingBy(ServeBaseInfoResp::getParentId, Collectors.counting()));

        return serveBaseInfoResps.stream().map(it -> {
            ServeResp serveResp = new ServeResp();
            serveResp.setServeId(it.getServeId());
            serveResp.setServeName(it.getName());
            serveResp.setLastServe(subServeMap.getOrDefault(it.getServeId(), 0L) > 0 ? 0 : 1);
            return serveResp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GetGroupByGroupIdsResp> getGroupByGroupIds(GetGroupByGroupIdsReq rqt) {
        return Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(rqt.getGroupIds(), rqt.getPersonaId())))
                .map(BigdataGetAllGroupListForPageResp::getData)
                .orElse(Collections.emptyList()).stream().
                map(it -> new GetGroupByGroupIdsResp(it.getGroupId(), it.getGroupName())).collect(Collectors.toList());
    }

    @Override
    public List<GetServeByServeIdsResp> getServeByServeIds(GetServeByServeIdsReq rqt) {
        return serveCommonService.getServeBaseInfoByServeIdSet(rqt.getServeIds()).stream()
                .map(it -> new GetServeByServeIdsResp(it.getServeId(), it.getName()))
                .collect(Collectors.toList());
    }


    @Override
    public List<GetGoodsListResp> getGoodsList(Integer businessLineId){

        List<Serve> lv1ServeList = serveCommonService.getLevel1ServeByBusinessLineId(businessLineId);

        lv1ServeList = lv1ServeList.stream().
                collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Serve::getGoodsId))), ArrayList::new));

        Set<Long> lv1SeveIdSet = lv1ServeList.stream().map(Serve::getServeId).collect(Collectors.toSet());

        List<ServeBaseInfoResp> lv2ServeList = serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(lv1SeveIdSet,Long.valueOf(businessLineId));

        Set<Long> lv2SeveIdSet = lv2ServeList.stream().map(ServeBaseInfoResp::getServeId).collect(Collectors.toSet());


        Map<Long,List<ServeBaseInfoResp>> lv2ServeMap = lv2ServeList.stream().collect(Collectors.groupingBy(lv2Serve -> lv2Serve.getLevel1Id()));


        List<ServeBaseInfoResp> lv3ServeList = serveCommonService.getServeBaseInfoByParentIdsAndBusinessLineId(lv2SeveIdSet,Long.valueOf(businessLineId));
//
//        lv3ServeList = lv3ServeList.stream().
//                collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ServeBaseInfoResp::getGoodsId))), ArrayList::new));

        Map<Long,List<ServeBaseInfoResp>> lv3ServeMap = lv3ServeList.stream().collect(Collectors.groupingBy(lv3Serve -> lv3Serve.getLevel2Id()));


        Set<Long> goodsIdSet = new HashSet<>();
        List<GetGoodsListResp> respList = new ArrayList<>();

        lv1ServeList.forEach(lv1Serve -> {
            GetGoodsListResp lv1Resp = new GetGoodsListResp();
            lv1Resp.setGoodsId(lv1Serve.getGoodsId());
            lv1Resp.setGoodsName(lv1Serve.getGoodsName());
            List<ServeBaseInfoResp> lv2ServeBaseInfoRespList = lv2ServeMap.get(lv1Serve.getServeId());
            if(CollectionUtils.isNotEmpty(lv2ServeBaseInfoRespList)){
                List<GetGoodsListResp> lv2RespList = new ArrayList<>();
                lv2ServeBaseInfoRespList.forEach(lv2Serve -> {
                    GetGoodsListResp lv2Resp = new GetGoodsListResp();
                    lv2Resp.setGoodsId(lv2Serve.getGoodsId());
                    lv2Resp.setGoodsName(lv2Serve.getGoodsName());

                    List<ServeBaseInfoResp> lv3ServeBaseInfoRespList = lv3ServeMap.get(lv2Serve.getServeId());
                    if(CollectionUtils.isNotEmpty(lv3ServeBaseInfoRespList)){
                        List<GetGoodsListResp> lv3RespList = new ArrayList<>();
                        lv3ServeBaseInfoRespList.forEach(lv3Serve -> {

                            GetGoodsListResp lv3Resp = new GetGoodsListResp();
                            lv3Resp.setGoodsId(lv3Serve.getGoodsId());
                            lv3Resp.setGoodsName(lv3Serve.getGoodsName());

                            lv3RespList.add(lv3Resp);


                        });

                        lv2Resp.setChildGoodsList(lv3RespList);
                    }

                    lv2RespList.add(lv2Resp);

                });
                lv1Resp.setChildGoodsList(lv2RespList);
            }

            respList.add(lv1Resp);

        });

        return respList;
    }


    @Override
    public List<GetServeTypeListResp> getServeTypeList(Long businessLineId,Long categoryId){
        List<GetServeTypeListResp> serveTypeListRespList = new ArrayList<>();
        List<ServeBaseInfoResp> respList = serveCommonService.getServeBaseInfoByGoodsId(categoryId);
        respList = respList.stream().filter(it -> businessLineId.equals(it.getBusinessLineId())).collect(Collectors.toList());
        respList.forEach(resp -> serveTypeListRespList.add(new GetServeTypeListResp(resp.getServeTypeId(),resp.getServeTypeName())));
        return serveTypeListRespList;
    }

    @Override
    public List<GetServeTypeListResp> batchGetServeTypeList(Long businessLineId, String categoryIds) {
        List<GetServeTypeListResp> serveTypeListRespList = new ArrayList<>();
        final HashSet<Long> categoryIdsSet = new HashSet<>();
        if (StringUtils.isNotEmpty(categoryIds)) {
            final Set<Long> collect = Arrays.asList(categoryIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
            categoryIdsSet.addAll(collect);
        }else {
            categoryIdsSet.addAll(new HashSet<>());
        }
        List<ServeBaseInfoResp> respList = serveCommonService.getServeBaseInfoByGoodsId(businessLineId,categoryIdsSet);
        respList = respList.stream().filter(it -> businessLineId.equals(it.getBusinessLineId())).collect(Collectors.toList());
        respList.forEach(resp -> serveTypeListRespList.add(new GetServeTypeListResp(resp.getServeTypeId(),resp.getServeTypeName())));
        return serveTypeListRespList.stream().distinct().collect(Collectors.toList());
    }

}
