package com.wanshifu.master.order.push.service.pushmatchlog;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.request.pushmatchlog.PushMatchLogExportRqt;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;

import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/4/25 20:26
 */
public interface PushMatchLogService {

    SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    Integer exportPushMatchLogList(PushMatchLogExportRqt rqt);

    SimplePageInfo<OrderFullTimeMasterMatchLogDto> fullTimeMatchLogList(PushMatchLogRqt pushMatchLogRqt);

}
