package com.wanshifu.master.order.push.controller.orderRoutingStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;
import com.wanshifu.master.order.push.service.orderRoutingStrategy.OrderRoutingStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * Title�?
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderRoutingStrategy")
public class OrderRoutingStrategyController {

    @Resource
    private OrderRoutingStrategyService orderRoutingStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return orderRoutingStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return orderRoutingStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return orderRoutingStrategyService.detail(rqt);
    }


    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt){
        return orderRoutingStrategyService.list(rqt);
    }



    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return orderRoutingStrategyService.delete(rqt);
    }


    /**
     *
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt) {
        return orderRoutingStrategyService.enable(rqt);
    }

}
