package com.wanshifu.master.order.push.service.newMasterOrderDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.newMasterOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.*;

public interface NewMasterOrderDistributeStrategyService {

    Integer create(CreateRqt rqt);


    Integer update(UpdateRqt rqt);

    Integer enable(EnableRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);


    Integer delete(DeleteRqt rqt);


}
