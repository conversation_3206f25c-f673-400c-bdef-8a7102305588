//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Condition;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.List;
//
//@Repository
//public class FilterStrategySnapshotRepository extends BaseRepository<FilterStrategySnapshot> {
//
//    public Long insert(String strategyName, String strategyDesc, String filterStrategyJson, String ruleExpression, Integer businessLineId, String categoryIds, Long accountId) {
//        FilterStrategySnapshot filterStrategySnapshot = new FilterStrategySnapshot();
//        filterStrategySnapshot.setStrategyName(strategyName);
//        filterStrategySnapshot.setStrategyDesc(strategyDesc);
//        filterStrategySnapshot.setCategoryIds(categoryIds);
//        filterStrategySnapshot.setFilterRule(filterStrategyJson);
//        filterStrategySnapshot.setRuleExpression(ruleExpression);
//        filterStrategySnapshot.setBusinessLineId(businessLineId);
//        filterStrategySnapshot.setCreateAccountId(accountId);
//        filterStrategySnapshot.setUpdateAccountId(accountId);
//        this.insertSelective(filterStrategySnapshot);
//        return filterStrategySnapshot.getSnapshotId();
//    }
//
//
//    public List<FilterStrategySnapshot> selectBySnapshotIdList(List<Long> snapshotIdList){
//        Condition condition = new Condition(FilterStrategySnapshot.class);
//        Example.Criteria criteria = condition.createCriteria();
//        criteria.andIn("snapshotId", snapshotIdList);
//        return this.selectByCondition(condition);
//    }
//}
