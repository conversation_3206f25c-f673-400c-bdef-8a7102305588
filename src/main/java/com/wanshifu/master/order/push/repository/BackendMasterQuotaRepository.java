//package com.wanshifu.master.order.push.repository;
//
//import cn.hutool.core.util.StrUtil;
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.po.MasterQuota;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Collections;
//import java.util.List;
//
//@Repository
//public class BackendMasterQuotaRepository extends BaseRepository<MasterQuota> {
//
//
//    public List<MasterQuota> selectQuotaList(String quotaName) {
//        Example example = new Example(MasterQuota.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (StringUtils.isNotBlank(quotaName)) {
//            criteria.andLike("quotaName", StrUtil.format("%{}%",quotaName));
//        }
//        criteria.andEqualTo("isDelete", 0);
//        return this.selectByExample(example);
//    }
//
//    public List<MasterQuota> selectByCodes(List<String> quotaCodes) {
//        if (CollectionUtils.isEmpty(quotaCodes)) {
//            return Collections.emptyList();
//        }
//        Example example = new Example(MasterQuota.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("quotaCode", quotaCodes)
//                .andEqualTo("isDelete", 0);
//        return this.selectByExample(example);
//    }
//}
