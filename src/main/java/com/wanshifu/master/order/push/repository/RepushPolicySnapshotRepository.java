//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.master.order.push.domain.po.RepushPolicySnapshot;
//import org.springframework.stereotype.Repository;
//
//@Repository
//public class RepushPolicySnapshotRepository extends BaseRepository<RepushPolicySnapshot> {
//
//    public Long insert(Integer businessLineId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
//        RepushPolicySnapshot repushPolicySnapshot = new RepushPolicySnapshot();
//        repushPolicySnapshot.setPolicyDesc(policyDesc);
//        repushPolicySnapshot.setPolicyName(policyName);
//        repushPolicySnapshot.setBusinessLineId(businessLineId);
//        repushPolicySnapshot.setCityIds(cityIds);
//        repushPolicySnapshot.setCategoryIds(categoryIds);
//        repushPolicySnapshot.setStrategyCombination(strategyCombinationJson);
//        repushPolicySnapshot.setCreateAccountId(loginUserId);
//        repushPolicySnapshot.setUpdateAccountId(loginUserId);
//        this.insertSelective(repushPolicySnapshot);
//        return repushPolicySnapshot.getSnapshotId();
//    }
//}