package com.wanshifu.master.order.push.config;

import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 16:05
 */
@Slf4j
public class ClearDataSourceKeyInterceptor implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        //先清除DataSourceKey防止串号
        DynamicDataSource.clearDataSourceKey();
        return invocation.proceed();
    }
}
