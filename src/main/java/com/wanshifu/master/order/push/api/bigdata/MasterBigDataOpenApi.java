package com.wanshifu.master.order.push.api.bigdata;

import com.wanshifu.master.order.push.decoder.MasterBigDataOpenApiDecoder;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.BigdataGroupListReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "bigdata-persona-service-api",
        url = "${wanshifu.bigdata-persona-service.url}",
        contextId = "masterBigDataOpenApi",
        configuration = {MasterBigDataOpenApiDecoder.class, MasterBigDataOpenApiDecoder.ApiErrorDecoder.class})
public interface MasterBigDataOpenApi {

    /***
     * 分页获取人群信息
     * @param bigdataUserGroupListReq
     * @return
     */
    @PostMapping("/openApi/getAllGroupListForPage")
    BigdataGetAllGroupListForPageResp<BigdataGetAllGroupListForPageResp.GroupInfo> getAllGroupListForPage(@RequestBody BigdataGroupListReq bigdataUserGroupListReq);

    /***
     * 根据人群id列表查询人群信息
     * @param bigdataUserGroupListReq
     * @return
     */
    @PostMapping("/openApi/getAllGroupListByGroupIds")
    BigdataGetAllGroupListForPageResp<BigdataGetAllGroupListByGroupIdsResp> getAllGroupListByGroupIds(@RequestBody BigdataGetAllGroupListByGroupIdsReq bigdataUserGroupListReq);
}
