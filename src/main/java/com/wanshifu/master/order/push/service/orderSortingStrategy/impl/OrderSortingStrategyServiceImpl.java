package com.wanshifu.master.order.push.service.orderSortingStrategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.dto.OrderSortStrategyExposureRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.OrderSortStrategyRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.ScoreRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.SortRuleExpressionDto;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.request.orderSortingStrategy.*;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.feature.SpecialCategoryResp;
import com.wanshifu.master.order.push.domain.vo.common.ExposureRuleItem;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.domain.vo.orderSortingStrategy.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.common.StrategyRuleExpressionService;
import com.wanshifu.master.order.push.service.orderSortingStrategy.OrderSortingStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.master.order.sort.domain.po.ComplexFeature;
import com.wanshifu.master.order.sort.domain.po.OrderSortStrategy;
import com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.ListRqt;
import com.wanshifu.master.order.sort.service.api.MasterOrderSortServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-15 10:14
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderSortingStrategyServiceImpl implements OrderSortingStrategyService {
    private final StrategyRuleExpressionService strategyRuleExpressionService;
    private final AddressCommonService addressCommon;
    private final IopAccountApi iopAccountApi;
    private final GoodsCommonService goodsCommon;
    private final MasterOrderSortServiceApi masterOrderSortServiceApi;
    private final ServeCommonService serveCommonService;
    // private final AuthHandler authHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(CreateRqt rqt) {

        com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.CreateRqt createRqt = new com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.CreateRqt();
        BeanUtils.copyProperties(rqt, createRqt);
        createRqt.setOperateTime(new Date());
        createRqt.setOperateAccountId(UserInfoUtils.getCurrentLoginAccountId());

        //自然排序
        SortRule sortRule = rqt.getSortRule();
        checkSortRuleParams(sortRule);
        createRqt.setSortRule(JSONObject.toJSONString(sortRule));
        //构建表达式
        OrderSortStrategyRuleExpressionDto orderSortStrategyRuleExpressionDto = new OrderSortStrategyRuleExpressionDto();
        this.buildSortRuleExpression(orderSortStrategyRuleExpressionDto, sortRule);
        createRqt.setRuleExpression(JSON.toJSONString(orderSortStrategyRuleExpressionDto));

        //干预排序
        ExposureSortRule exposureSortRule = rqt.getExposureSortRule();
        if (Objects.nonNull(exposureSortRule)) {
            checkExposureSortRuleParams(exposureSortRule);
            createRqt.setExposureSortRule(JSONObject.toJSONString(exposureSortRule));
            //构建表达式
            OrderSortStrategyExposureRuleExpressionDto orderSortStrategyExposureRuleExpressionDto = new OrderSortStrategyExposureRuleExpressionDto();
            this.buildExposureSortRuleExpression(orderSortStrategyExposureRuleExpressionDto, exposureSortRule);
            createRqt.setExposureRuleExpression(JSON.toJSONString(orderSortStrategyExposureRuleExpressionDto));

        } else {
            createRqt.setExposureSortRule("");
            createRqt.setExposureRuleExpression("");
        }

        return masterOrderSortServiceApi.create(createRqt);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(UpdateRqt rqt) {
        com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.ModifyRqt modifyRqt = new com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.ModifyRqt();
        BeanUtils.copyProperties(rqt, modifyRqt);
        modifyRqt.setOperateTime(new Date());
        modifyRqt.setOperateAccountId(UserInfoUtils.getCurrentLoginAccountId());

        //自然排序
        SortRule sortRule = rqt.getSortRule();
        checkSortRuleParams(sortRule);
        modifyRqt.setSortRule(JSONObject.toJSONString(sortRule));
        //构建表达式
        OrderSortStrategyRuleExpressionDto orderSortStrategyRuleExpressionDto = new OrderSortStrategyRuleExpressionDto();
        this.buildSortRuleExpression(orderSortStrategyRuleExpressionDto, sortRule);
        modifyRqt.setRuleExpression(JSON.toJSONString(orderSortStrategyRuleExpressionDto));

        //干预排序
        ExposureSortRule exposureSortRule = rqt.getExposureSortRule();
        if (Objects.nonNull(exposureSortRule)) {
            checkExposureSortRuleParams(exposureSortRule);
            modifyRqt.setExposureSortRule(JSONObject.toJSONString(exposureSortRule));
            //构建表达式
            OrderSortStrategyExposureRuleExpressionDto orderSortStrategyExposureRuleExpressionDto = new OrderSortStrategyExposureRuleExpressionDto();
            this.buildExposureSortRuleExpression(orderSortStrategyExposureRuleExpressionDto, exposureSortRule);
            modifyRqt.setExposureRuleExpression(JSON.toJSONString(orderSortStrategyExposureRuleExpressionDto));

        } else {
            modifyRqt.setExposureSortRule("");
            modifyRqt.setExposureRuleExpression("");
        }

        return masterOrderSortServiceApi.modify(modifyRqt);
    }

    private void checkSortRuleParams(SortRule sortRule) {
        SortRuleItemVo generalRule = sortRule.getGeneralRule();
        DifferenceRuleVo differenceRule = sortRule.getDifferenceRule();
        SortRuleItemVo specialRule = sortRule.getSpecialRule();
        BigDecimal totalWeight = generalRule.getWeight().add(differenceRule.getWeight());
        Assert.isTrue(totalWeight.compareTo(BigDecimal.ONE) == 0, "通用项和差异项整体权重和必须为1!");

        BigDecimal generalRuleWeight = generalRule.getItemList().stream().map(RuleItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(generalRuleWeight.compareTo(BigDecimal.ONE) == 0, "通用项各个匹配项权重和必须为1!");

        List<Long> generalOneCategoryIds = new ArrayList<>();
        List<Long> generalTwoCategoryIds = new ArrayList<>();
        for (RuleItem ruleItem : generalRule.getItemList()) {
            if ("is_special_category".equals(ruleItem.getItemName())) {
                if (ruleItem.getOneCategoryIds().isEmpty()
                        && ruleItem.getTwoCategoryIds().isEmpty()) {
                    throw new IllegalArgumentException("订单通用项是否特殊类目需至少配置一个类目！");
                }
                generalOneCategoryIds.addAll(ruleItem.getOneCategoryIds());
                generalTwoCategoryIds.addAll(ruleItem.getTwoCategoryIds());
            }
        }
        if (CollectionUtil.isNotEmpty(generalOneCategoryIds)) {
            if (new HashSet<>(generalOneCategoryIds).size() != generalOneCategoryIds.size()) {
                throw new IllegalArgumentException("订单通用项存在同样的类目配置！");
            }
        }
        if (CollectionUtil.isNotEmpty(generalTwoCategoryIds)) {
            if (new HashSet<>(generalTwoCategoryIds).size() != generalTwoCategoryIds.size()) {
                throw new IllegalArgumentException("订单通用项存在同样的类目配置！");
            }
        }

        for (com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule rule : differenceRule.getDifferenceRuleList()) {
            List<Long> differenceOneCategoryIds = new ArrayList<>();
            List<Long> differenceTwoCategoryIds = new ArrayList<>();
            for (RuleItem ruleItem : rule.getItemList()) {
                if ("is_special_category".equals(ruleItem.getItemName())) {
                    if (ruleItem.getOneCategoryIds().isEmpty()
                            && ruleItem.getTwoCategoryIds().isEmpty()) {
                        throw new IllegalArgumentException("订单差异项是否特殊类目需至少配置一个类目！");
                    }
                    differenceOneCategoryIds.addAll(ruleItem.getOneCategoryIds());
                    differenceTwoCategoryIds.addAll(ruleItem.getTwoCategoryIds());
                }
            }
            if (CollectionUtil.isNotEmpty(differenceOneCategoryIds)) {
                if (new HashSet<>(differenceOneCategoryIds).size() != differenceOneCategoryIds.size()) {
                    throw new IllegalArgumentException("订单差异项存在同样的类目配置！");
                }
            }
            if (CollectionUtil.isNotEmpty(differenceTwoCategoryIds)) {
                if (new HashSet<>(differenceTwoCategoryIds).size() != differenceTwoCategoryIds.size()) {
                    throw new IllegalArgumentException("订单差异项存在同样的类目配置！");
                }
            }
        }

        if (Objects.nonNull(specialRule)) {
            //特殊规则项下架兼容
            List<Long> specialOneCategoryIds = new ArrayList<>();
            List<Long> specialTwoCategoryIds = new ArrayList<>();
            for (RuleItem ruleItem : specialRule.getItemList()) {
                Assert.isTrue(ruleItem.getWeight().compareTo(BigDecimal.ONE) == 0, "特殊项各个匹配项权重必须为1!");
                if ("is_special_category".equals(ruleItem.getItemName())) {
                    if (ruleItem.getOneCategoryIds().isEmpty()
                            && ruleItem.getTwoCategoryIds().isEmpty()) {
                        throw new IllegalArgumentException("特殊规则项是否特殊类目需至少配置一个类目！");
                    }
                    specialOneCategoryIds.addAll(ruleItem.getOneCategoryIds());
                    specialTwoCategoryIds.addAll(ruleItem.getTwoCategoryIds());
                }
            }
            if (CollectionUtil.isNotEmpty(specialOneCategoryIds)) {
                if (new HashSet<>(specialOneCategoryIds).size() != specialOneCategoryIds.size()) {
                    throw new IllegalArgumentException("特殊规则项存在同样的类目配置！");
                }
            }
            if (CollectionUtil.isNotEmpty(specialTwoCategoryIds)) {
                if (new HashSet<>(specialTwoCategoryIds).size() != specialTwoCategoryIds.size()) {
                    throw new IllegalArgumentException("特殊规则项存在同样的类目配置！");
                }
            }
        }


        boolean allMatch = differenceRule.getDifferenceRuleList().stream().allMatch(it -> {
            BigDecimal reduce = it.getItemList().stream().map(RuleItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            return reduce.compareTo(BigDecimal.ONE) == 0;
        });
        Assert.isTrue(allMatch, "差异项各个匹配项权重之和必须为1!");
    }

    private void checkExposureSortRuleParams(ExposureSortRule sortRule) {
        ExposurePositionConfig positionConfig = sortRule.getExposurePositionConfig();
        if (Objects.isNull(positionConfig)) {
            throw new IllegalArgumentException("干预规则位置配置参数为空！");
        }
        if (positionConfig.getPositionType() == 1) {
            throw new IllegalArgumentException("干预规则暂不支持固定位置参数配置！");
        }
        if (positionConfig.getPositionType() == 2) {
            if (Objects.isNull(positionConfig.getDynamicEveryFewSize())
                    || Objects.isNull(positionConfig.getDynamicPositionIndex())) {
                throw new IllegalArgumentException("干预规则动态位置配置未传具体位置参数！");
            }
            if (positionConfig.getDynamicPositionIndex() > positionConfig.getDynamicEveryFewSize()) {
                throw new IllegalArgumentException("干预规则动态位置配置位置索引不能大于每页总数！");
            }
        }

        if (CollectionUtil.isEmpty(sortRule.getItemList())) {
            throw new IllegalArgumentException("干预规则配置参数为空！");
        }

        List<Long> generalOneCategoryIds = new ArrayList<>();
        List<Long> generalTwoCategoryIds = new ArrayList<>();
        for (ExposureRuleItem exposureRuleItem : sortRule.getItemList()) {
            if ("is_special_category".equals(exposureRuleItem.getItemName())) {
                if (exposureRuleItem.getOneCategoryIds().isEmpty()
                        && exposureRuleItem.getTwoCategoryIds().isEmpty()) {
                    throw new IllegalArgumentException("干预规则是否特殊类目需至少配置一个类目！");
                }
                generalOneCategoryIds.addAll(exposureRuleItem.getOneCategoryIds());
                generalTwoCategoryIds.addAll(exposureRuleItem.getTwoCategoryIds());
            }
        }
        if (CollectionUtil.isNotEmpty(generalOneCategoryIds)) {
            if (new HashSet<>(generalOneCategoryIds).size() != generalOneCategoryIds.size()) {
                throw new IllegalArgumentException("干预规则是否特殊类目存在同样的类目配置！");
            }
        }
        if (CollectionUtil.isNotEmpty(generalTwoCategoryIds)) {
            if (new HashSet<>(generalTwoCategoryIds).size() != generalTwoCategoryIds.size()) {
                throw new IllegalArgumentException("干预规则是否特殊类目存在同样的类目配置！");
            }
        }
    }

    private void buildSortRuleExpression(OrderSortStrategyRuleExpressionDto expressionDto, SortRule sortRule) {
        //通用项
        SortRuleItemVo generalRule = sortRule.getGeneralRule();
        OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpressionVo scoreRuleItemExpressionVo = new OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpressionVo();
        scoreRuleItemExpressionVo.setWeight(generalRule.getWeight());

        List<ScoreRuleExpressionDto> generalRuleExpressionDtos = strategyRuleExpressionService.getRuleExpressions(generalRule.getItemList(), "orderSort");
        List<OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpression> scoreRuleItemExpressions =
                BeanCopyUtil.copyListProperties(generalRuleExpressionDtos, OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpression.class, null);
        scoreRuleItemExpressionVo.setScoreRuleItemExpressionList(scoreRuleItemExpressions);

        expressionDto.setGeneralRuleExpression(scoreRuleItemExpressionVo);
        //特殊项
        if (Objects.nonNull(sortRule.getSpecialRule())) {
            //特殊项下架兼容
            SortRuleItemVo specialRule = sortRule.getSpecialRule();
            OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpressionVo specialScoreRuleItemExpressionVo = new OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpressionVo();
            //特殊项去除权重配置，默认1
            specialScoreRuleItemExpressionVo.setWeight(BigDecimal.ONE);
            List<ScoreRuleExpressionDto> specialRuleExpressionDtos = strategyRuleExpressionService.getRuleExpressions(specialRule.getItemList(), "orderSort");
            List<OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpression> specialScoreRuleItemExpressions =
                    BeanCopyUtil.copyListProperties(specialRuleExpressionDtos, OrderSortStrategyRuleExpressionDto.ScoreRuleItemExpression.class, null);
            specialScoreRuleItemExpressionVo.setScoreRuleItemExpressionList(specialScoreRuleItemExpressions);

            expressionDto.setSpecialRuleRuleExpression(specialScoreRuleItemExpressionVo);
        }
        //差异项
        DifferenceRuleVo differenceRule = sortRule.getDifferenceRule();
        List<com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule> differenceRuleList = differenceRule.getDifferenceRuleList();
        List<SortRuleExpressionDto> differenceSortRuleExpressionDtos = strategyRuleExpressionService.buildSortRuleExpressions(differenceRuleList, "orderSort");

        OrderSortStrategyRuleExpressionDto.DifferenceRuleRuleExpressionVo differenceRuleRuleExpressionVo = new OrderSortStrategyRuleExpressionDto.DifferenceRuleRuleExpressionVo();
        differenceRuleRuleExpressionVo.setWeight(differenceRule.getWeight());
        List<OrderSortStrategyRuleExpressionDto.DifferenceRuleRuleExpressionItem> differenceRuleRuleExpressionItems = BeanCopyUtil.copyListProperties(differenceSortRuleExpressionDtos, OrderSortStrategyRuleExpressionDto.DifferenceRuleRuleExpressionItem.class, null);
        differenceRuleRuleExpressionVo.setDifferenceRuleExpressionList(differenceRuleRuleExpressionItems);
        expressionDto.setDifferenceRuleExpression(differenceRuleRuleExpressionVo);
    }

    private void buildExposureSortRuleExpression(OrderSortStrategyExposureRuleExpressionDto expressionDto, ExposureSortRule sortRule) {
        List<ScoreRuleExpressionDto> exposureRuleExpressionDtos = strategyRuleExpressionService.getExposureRuleExpressions(sortRule.getItemList());
        List<OrderSortStrategyExposureRuleExpressionDto.ScoreRuleItemExpression> exposureRuleItemExpressions =
                BeanCopyUtil.copyListProperties(exposureRuleExpressionDtos, OrderSortStrategyExposureRuleExpressionDto.ScoreRuleItemExpression.class, null);
        expressionDto.setExposureRuleItemExpressionList(exposureRuleItemExpressions);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {

        OrderSortStrategy detail = masterOrderSortServiceApi.detail(new com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.DetailRqt(rqt.getStrategyId()));

        DetailResp detailResp = new DetailResp();
        BeanCopyUtil.copyProperties(detail, detailResp);
        detailResp.setTestFlag(detail.getTestFlag());
        detailResp.setTestType(detail.getTestType());
        if (!Strings.isNullOrEmpty(detail.getTestGroupName())) {
            detailResp.setTestGroupNameList(StrUtil.split(detail.getTestGroupName(), ','));
        }

        String exposureSortRule = detail.getExposureSortRule();
        if (Strings.isNullOrEmpty(exposureSortRule)) {
            detailResp.setExposureSortRule(null);
        } else {
            detailResp.setExposureSortRule(JSON.parseObject(exposureSortRule, ExposureSortRule.class));
        }

        String sortingRule = detail.getSortRule();
        DetailResp.SortRule sortRule = JSON.parseObject(sortingRule, DetailResp.SortRule.class);

        Set<Long> serveIds = sortRule.getDifferenceRule().getDifferenceRuleList().stream()
                .flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it -> CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serveIds)) {
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;

        sortRule.getDifferenceRule().getDifferenceRuleList().forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {
            List<List<Long>> serveIdListList = item.getServeIdList();
            if (CollectionUtils.isNotEmpty(serveIdListList)) {
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList(serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));
        detailResp.setSortRule(sortRule);
        return detailResp;
    }

    @Override
    public Integer enable(EnableRqt rqt) {
        com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.EnableRqt enableRqt = new com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.EnableRqt(rqt.getStrategyId(), rqt.getStrategyStatus(), UserInfoUtils.getCurrentLoginAccountId(), new Date());
        enableRqt.setOperateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return masterOrderSortServiceApi.enable(enableRqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        SimplePageInfo<com.wanshifu.master.order.sort.domains.api.response.orderSortStrategy.ListResp> list = masterOrderSortServiceApi.list(rqt);

        String cityIdsStr = list.getList().stream().map(com.wanshifu.master.order.sort.domains.api.response.orderSortStrategy.ListResp::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));
        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));


        List<Long> updateAccountIds = list.getList().stream().map(com.wanshifu.master.order.sort.domains.api.response.orderSortStrategy.ListResp::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }

        //类目名称
        List<Long> goodsIds = list.getList().stream().map(com.wanshifu.master.order.sort.domains.api.response.orderSortStrategy.ListResp::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));
        goodsNameMap.put(0L,"全部(不限类目)");

        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(list.getList(), ListResp.class, (s, t) -> {
            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCity("全国");
            }else {
                List<Address> addresses = Arrays.stream(cityIds.split(","))
                        .map(it -> addressMap.get(Long.parseLong(it)))
                        .filter(Objects::nonNull).collect(Collectors.toList());
                t.setCity(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }
            InterprectChineseUtil.reflexEnum(t);
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            t.setTestFlag(s.getTestFlag());
            t.setTestType(s.getTestType());
            t.setTestGroupNameList(s.getTestGroupNameList());
        });
        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(list.getPages());
        listRespSimplePageInfo.setPageNum(list.getPageNum());
        listRespSimplePageInfo.setTotal(list.getTotal());
        listRespSimplePageInfo.setPageSize(list.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    public Integer delete(DeleteRqt rqt) {
        com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.DeleteRqt deleteRqt = new com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.DeleteRqt(rqt.getStrategyId(), UserInfoUtils.getCurrentLoginAccountId(), new Date());
        return masterOrderSortServiceApi.delete(deleteRqt);
    }

    @Override
    public SpecialCategoryResp specialCategoryConfigQuery() {
        final SpecialCategoryResp specialCategoryResp = new SpecialCategoryResp();
        ComplexFeature complexFeature=masterOrderSortServiceApi.specialCategoryConfigQuery();
        specialCategoryResp.setFeatureId(complexFeature.getFeatureId());
        final String[] parseSpecialCategory = parseSpecialCategory(complexFeature.getCalculateExpression());
        specialCategoryResp.setBusinessLineOneSpecialCategoryIds(parseSpecialCategory[0]);
        specialCategoryResp.setBusinessLineTwoSpecialCategoryIds(parseSpecialCategory[1]);
        translateSpecialCategory(specialCategoryResp,parseSpecialCategory);
        return specialCategoryResp;
    }


    private void translateSpecialCategory(SpecialCategoryResp specialCategoryResp,String[] parseSpecialCategory){
        //翻译成中文
        List<Long> goodsIds = new ArrayList<>();
        List<Long> businessLineOneSpecialCategoryIds=new ArrayList<>();
        List<Long> businessLineTwoSpecialCategoryIds=new ArrayList<>();
        if (StringUtils.isNotEmpty(parseSpecialCategory[0])) {
            businessLineOneSpecialCategoryIds=
                    Arrays.stream(parseSpecialCategory[0].split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(parseSpecialCategory[1])) {
            businessLineTwoSpecialCategoryIds=
                    Arrays.stream(parseSpecialCategory[1].split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        goodsIds.addAll(businessLineOneSpecialCategoryIds);
        goodsIds.addAll(businessLineTwoSpecialCategoryIds);
        goodsIds=goodsIds.stream().distinct().collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));
        final JSONArray businessLineOneSpecialCategoryArray = new JSONArray();
        businessLineOneSpecialCategoryIds.forEach(
                categoryId->{
                    final JSONObject row = new JSONObject();
                    row.put("categoryId",categoryId);
                    row.put("categoryName",goodsNameMap.get(categoryId));
                    businessLineOneSpecialCategoryArray.add(row);
                }
        );
        specialCategoryResp.setBusinessLineOneSpecialCategory(businessLineOneSpecialCategoryArray);

        final JSONArray businessLineTwoSpecialCategoryArray = new JSONArray();
        businessLineTwoSpecialCategoryIds.forEach(
                categoryId->{
                    final JSONObject row = new JSONObject();
                    row.put("categoryId",categoryId);
                    row.put("categoryName",goodsNameMap.get(categoryId));
                    businessLineTwoSpecialCategoryArray.add(row);
                }
        );
        specialCategoryResp.setBusinessLineTwoSpecialCategory(businessLineTwoSpecialCategoryArray);

    }


    private String[] parseSpecialCategory(String exp) {
        final String[] result = new String[2];
        final String businessOne = exp.substring("((business_line_id == 1 and category_id in [".length(), exp.indexOf("]) or ("));
        String twoStart=") or (business_line_id == 2 and category_id in [";
        final String businessTwo = exp.substring(exp.indexOf(twoStart)+twoStart.length(),exp.indexOf("])) ? 1 : 0"));
        result[0]=businessOne;
        result[1]=businessTwo;
        return result;
    }

    @Override
    public Integer specialCategoryConfigUpdate(UpdateSpecialCategoryRqt updateSpecialCategoryRqt) {
        final Long featureId = updateSpecialCategoryRqt.getFeatureId();
        String part1="((business_line_id == 1 and category_id in [";
        final String businessLineTwoSpecialCategoryIds = updateSpecialCategoryRqt.getBusinessLineTwoSpecialCategoryIds();
        String part2="]) or (business_line_id == 2 and category_id in [";
        final String businessLineOneSpecialCategoryIds = updateSpecialCategoryRqt.getBusinessLineOneSpecialCategoryIds();
        String part3="])) ? 1 : 0";

        final StringBuilder sb = new StringBuilder();
        sb.append(part1).append(businessLineOneSpecialCategoryIds).append(part2).append(businessLineTwoSpecialCategoryIds).append(part3);
        String expression=sb.toString();


        final com.wanshifu.master.order.sort.domains.api.request.feature.UpdateSpecialCategoryRqt
                updateSpecialCategory = new com.wanshifu.master.order.sort.domains.api.request.feature.UpdateSpecialCategoryRqt();
        updateSpecialCategory.setFeatureId(updateSpecialCategoryRqt.getFeatureId());
        updateSpecialCategory.setCalculateExpression(expression);
        return masterOrderSortServiceApi.specialCategoryConfigUpdate(
                updateSpecialCategory
        );
    }
}