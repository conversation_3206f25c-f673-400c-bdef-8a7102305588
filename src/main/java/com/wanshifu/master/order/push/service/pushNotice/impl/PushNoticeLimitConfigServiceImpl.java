package com.wanshifu.master.order.push.service.pushNotice.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.wanshifu.master.notice.domains.po.PushNoticeLimitConfig;
import com.wanshifu.master.notice.domains.request.pushNotice.PushNoticeLimitConfigUpdateRqt;
import com.wanshifu.master.notice.service.api.PushNoticeLimitConfigApi;
import com.wanshifu.master.order.push.domain.request.pushnotice.NoticeLimitConfigUpdateRqt;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeLimitConfigService;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/21 18:07
 */
@Service
public class PushNoticeLimitConfigServiceImpl implements PushNoticeLimitConfigService {

    @Resource
    private PushNoticeLimitConfigApi pushNoticeLimitConfigApi;

//    @Resource
//    private AuthHandler authHandler;

    @Override
    public List<PushNoticeLimitConfig> listByBusinessLineId(Integer businessLineId) {
        UserInfoUtils.getCurrentLoginAccountId();
        return pushNoticeLimitConfigApi.listByBusinessLineId(businessLineId);
    }

    @Override
    public void updateByList(List<NoticeLimitConfigUpdateRqt> configList) {
        if (CollectionUtil.isEmpty(configList)) {
            return;
        }
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        List<PushNoticeLimitConfigUpdateRqt> rqtList = Lists.newArrayList();
        configList.forEach(item -> {
            PushNoticeLimitConfigUpdateRqt rqt = BeanUtil.toBean(item, PushNoticeLimitConfigUpdateRqt.class);
            rqt.setUpdateAccountId(loginUserId.intValue());
            rqtList.add(rqt);
        });
        pushNoticeLimitConfigApi.updateByList(rqtList);
    }
}
