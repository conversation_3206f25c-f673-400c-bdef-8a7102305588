//package com.wanshifu.master.order.push.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.authentication.AuthenticationManager;
//import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-17 14:23
// */
//@EnableWebSecurity
//@Configuration
////@Order(1)
//@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
//public class SecurityConfig extends WebSecurityConfigurerAdapter {
//
//    @Bean
//    @Override
//    public AuthenticationManager authenticationManager() throws Exception {
//        return super.authenticationManager();
//    }
//
//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http.httpBasic().disable();
//        http.requestMatchers()
//                .antMatchers("/login", "/oauth/authorize")
//                .and()
//                .authorizeRequests()
//                .antMatchers("/orderPush/listOrderPushRecordDetail"
//                        , "/orderPush/listInfoOrderPushRecordDetail"
//                        , "/orderPush/getOrderPushPushNumber").permitAll()
//                .anyRequest().authenticated()
//                .and().csrf().disable()
//                .formLogin().loginPage("/login").permitAll();
//    }
//}