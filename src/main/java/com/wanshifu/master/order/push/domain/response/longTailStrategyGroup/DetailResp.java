package com.wanshifu.master.order.push.domain.response.longTailStrategyGroup;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  长尾策略详情Resp.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class DetailResp {

    @NotEmpty
    private String longTailStrategyGroupName;
    @NotEmpty
    private String longTailStrategyGroupDesc;
    @NotNull
    private String categoryIds;
    @NotNull
    private String openCityMode;
    @NotNull
    private String cityIds;

    @NotNull
    private List<LongTailGroupRuleResp> longTailGroupRuleList;
    @NotNull
    private Integer businessLineId;
    private Long createAccountId;

    private String pushType;


    @Data
    public static class LongTailGroupRuleResp{
        /**
         * 开启条件
         */
        private OpenCondition openCondition;


        /**
         * 长尾策略id
         */
        private Long longTailStrategyId;


        /**
         * 长尾单策略名称
         */
        private String longTailStrategyName;

        /**
         * 开启条件item
         */
        @Data
        public static class OpenCondition {
            /**
             * 或且关系
             */
            private String condition;

            /**
             *开启条件规则项
             */
            private List<OpenConditionItem> itemList;


            /**
             * 开启条件item
             */
            @Data
            public static class OpenConditionItem{

                /**
                 *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源,offer_num:接单人数
                 */
                private String itemName;

                /**
                 * 符号 in:包含  not_in:不包含
                 */
                private String term;

                /**
                 * 规则项值
                 */
                private String itemValue;

                /**
                 * [1],[1,2],[1,2,3] 数组长度表示 服务级别
                 */
                private List<List<Long>> serveIdList;



                /**
                 * ["1:家具安装",“2:家具送货到楼下”]
                 */
                private List<String> serveInfoList;


            }

        }


    }



}