package com.wanshifu.master.order.push.controller.pushShuntRule;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.pushShuntRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushShuntRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushShuntRule.*;
import com.wanshifu.master.order.push.service.pushShuntRule.PushShuntRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/pushShuntRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushShuntRuleController {

    @Resource
    private PushShuntRuleService pushShuntRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return pushShuntRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return pushShuntRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return pushShuntRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return pushShuntRuleService.list(rqt);
    }


        /**
         * 删除补偿调度策略
         * @param rqt
         * @return
         */
        @PostMapping("/enable")
        public Integer enable(@RequestBody @Valid EnableRqt rqt) {
            return pushShuntRuleService.enable(rqt);
        }



    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return pushShuntRuleService.delete(rqt);
    }




}
