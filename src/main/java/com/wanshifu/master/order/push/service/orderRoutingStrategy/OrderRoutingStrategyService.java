package com.wanshifu.master.order.push.service.orderRoutingStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderRoutingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;

public interface OrderRoutingStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);


    }
