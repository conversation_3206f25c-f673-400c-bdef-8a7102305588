package com.wanshifu.master.order.push.export.convert;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.dto.UserAgreementPushMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.export.exceldto.UserAgreementPushMatchLogExcelDto;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 17:00
 */
public class UserAgreementLogExportConvert implements PushMatchLogExportConvert<UserAgreementPushMatchLogDto, UserAgreementPushMatchLogExcelDto> {

    @Override
    public UserAgreementPushMatchLogExcelDto convert(UserAgreementPushMatchLogDto source) {
        UserAgreementPushMatchLogExcelDto target = new UserAgreementPushMatchLogExcelDto();

        target.setMatchType(PushLogMatchType.USER_AGREEMENT.getDesc());
        target.setOrderCityName(source.getOrderCityName());
        target.setOrderNo(source.getOrderNo());
        target.setUserId(Objects.isNull(source.getUserId()) ? "" : source.getUserId().toString());
        target.setOrderCreateTime(Objects.isNull(source.getOrderCreateTime()) ? "-" : DateUtils.formatDateTime(source.getOrderCreateTime()));
        target.setOrderSource(source.getOrderSource());
        target.setServeTypeName(source.getServeTypeName());
        target.setMasterId(source.getMasterId().toString());
        target.setMasterName(source.getMasterName());
        target.setRecruitId(String.valueOf(source.getRecruitId()));

        if (Objects.isNull(source.getIsMatchSuccess())) {
            target.setIsMatchSuccess("-");
        } else {

            if (source.getIsMatchSuccess() == 1) {
                target.setIsMatchSuccess("是");
            } else {
                target.setIsMatchSuccess("否");
            }
        }
        target.setMatchFailReason(Strings.isNullOrEmpty(source.getMatchFailReason()) ? "" : source.getMatchFailReason());

        if (Objects.isNull(source.getIsCalculatePriceSuccess())) {
            target.setIsCalculatePriceSuccess("否");
        } else {
            if (source.getIsCalculatePriceSuccess() == 1) {
                target.setIsCalculatePriceSuccess("是");
            } else {
                target.setIsCalculatePriceSuccess("否");
            }
        }

        target.setCalculatePriceFailReason(Strings.isNullOrEmpty(source.getCalculatePriceFailReason()) ? "" : source.getCalculatePriceFailReason());

        if (Objects.isNull(source.getIsFilter())) {
            target.setIsFilter("否");
        } else {
            if (source.getIsFilter() == 1) {
                target.setIsFilter("是");
            } else {
                target.setIsFilter("否");
            }
        }

        target.setFilterReason(Strings.isNullOrEmpty(source.getFilterReason()) ? "" : source.getFilterReason());

        if (Objects.isNull(source.getIsDistribute())) {
            target.setIsDistribute("否");

        } else {
            if (source.getIsDistribute() == 1) {

                target.setIsDistribute("是");
            } else {
                target.setIsDistribute("否");
            }
        }

        target.setDistributeRule(Strings.isNullOrEmpty(source.getDistributeRule()) ? "-" : source.getDistributeRule());

        if (Objects.isNull(source.getIsAutoGrabSuccess())) {
            target.setIsAutoGrabSuccess("否");

        } else {
            if (source.getIsAutoGrabSuccess() == 1) {
                target.setIsAutoGrabSuccess("否");
            } else {
                target.setIsAutoGrabSuccess("否");
            }
        }

        target.setAutoGrabFailReason(Strings.isNullOrEmpty(source.getAutoGrabFailReason()) ? "" : source.getAutoGrabFailReason());

        target.setOrderVersion(source.getOrderVersion());

        target.setCreateTime(Objects.isNull(source.getCreateTime()) ? "-" : DateUtils.formatDateTime(source.getCreateTime()));

        return target;
    }
}
