package com.wanshifu.master.order.push.controller.orderPushMonitor;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.NoPushedMasterOrderListResp;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.OrderDynamicRoundsPushListResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;
import com.wanshifu.master.order.push.service.orderPushMonitor.OrderDynamicRoundsPushService;
import com.wanshifu.master.order.push.service.orderPushMonitor.OrderPushMonitorService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述 : 推单监控
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-07-11 15:31
 */
@RestController
@RequestMapping("/orderPushMonitor")
@Validated
public class OrderPushMonitorController {

    @Resource
    private OrderPushMonitorService orderPushMonitorService;

    @Resource
    private OrderDynamicRoundsPushService dynamicRoundsPushCountService;


    /**
     * 未推单明细列表
     * @param rqt
     * @return
     */
    @PostMapping("/noPushedOrderList")
    public SimplePageInfo<NoPushedMasterOrderListResp> noPushedMasterOrderList(@RequestBody @Valid NoPushedMasterOrderListRqt rqt) {
        return orderPushMonitorService.noPushedMasterOrderList(rqt);
    }



    /**
     *
     * @param rqt
     * @return
     */
    @GetMapping("/exportNoPushedOrderList")
    public Integer exportNoPushMasterOrderList(@Valid NoPushedMasterOrderListRqt rqt, HttpServletResponse httpServletResponse) {
        return orderPushMonitorService.exportNoPushMasterOrderList(rqt,httpServletResponse);
    }


    @PostMapping("/filterData")
    public List<GetFilterDataResp> filterData(@Valid @RequestBody GetFilterDataRqt rqt) {
        return orderPushMonitorService.filterData(rqt);
    }


    @PostMapping(value = "masterLackList")
    public SimplePageInfo<OrderDynamicRoundsPushListResp> masterLackList(@Valid @RequestBody ListRqt rqt){
        return dynamicRoundsPushCountService.list(rqt);
    }

    /**
     *
     * @param rqt
     * @return
     */
    @GetMapping("/exportMasterLackList")
    public Integer exportMasterLackList(@Valid ListRqt rqt, HttpServletResponse httpServletResponse) {
        return dynamicRoundsPushCountService.exportMasterLackList(rqt,httpServletResponse);
    }
}