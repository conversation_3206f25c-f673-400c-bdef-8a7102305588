package com.wanshifu.master.order.push.domain.response.agentDistributeStrategy;

import com.wanshifu.master.order.push.domain.rqt.agent.AddAgentDistributeStrategyRqt;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.List;

@Data
public class AgentDistributeStrategyDetailResp {

    private Integer strategyId;

    private Long agentId;

    private String agentName;

    private String masterSourceType;

    private String strategyName;

    private String strategyDesc;

    private Long cityDivisionId;

    private String serveIds;

    @ApiModelProperty(value = "服务集合")
    private List<String> serviceInfoList;

    private String thirdDivisionIds;

    private String distributeRule;

    private String distributePriority;

    private Long scoringStrategyId;
    private String scoringStrategyName;

    private Integer strategyStatus;

    private List<AgentServe> serveList;

    private String province;

    private String city;

    private Long provinceDivisionId;


    private List<AgentDivision> divisionList;

    private List<AddAgentDistributeStrategyRqt.NonEffectiveTime> nonEffectiveTimeList;

    private AddAgentDistributeStrategyRqt.PushStrategy pushStrategy;

    @Data
    public static class AgentServe{

        private Long serveId;

        private String serveName;

    }


    @Data
    public static class AgentDivision{

        private Long fourthDivisionId;

        private String fourthDivisionName;

    }

}
