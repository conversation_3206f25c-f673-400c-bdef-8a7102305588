//package com.wanshifu.master.order.push.config;
//
//import com.google.common.base.Strings;
//import com.wanshifu.framework.redis.autoconfigure.properties.RedisProperties;
//import com.wanshifu.master.order.push.service.common.impl.UserDetailsServiceImpl;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
//import org.springframework.security.authentication.AuthenticationManager;
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
//import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
//import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
//import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
//import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
//import redis.clients.jedis.JedisPoolConfig;
//
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-17 14:28
// */
//@Configuration
//@EnableAuthorizationServer
//public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
//
//    @Resource
//    AuthenticationManager authenticationManager;
//
//    @Resource
//    UserDetailsServiceImpl myUserDetailsService;
//
//    @Resource
//    DataSource dataSource;
//
//    @Value("${spring.oauth.client_id:push_backend}")
//    private String clientId;
//
//    @Value("${spring.oauth.client_secret:123456}")
//    private String clientSecret;
//
//    @Value("${spring.oauth.accessTokenValiditySeconds:3600}")
//    private Integer accessTokenValiditySeconds;
//
//    @Resource
//    private JedisConnectionFactory jedisConnectionFactory;
//
//    @Resource
//    private TokenStore tokenStore;
//
//    @Bean
//    PasswordEncoder passwordEncoder() {
//        return new BCryptPasswordEncoder();
//    }
//
//    @Bean
//    TokenStore tokenStore() {
//        RedisTokenStore redisTokenStore = new RedisTokenStore(jedisConnectionFactory);
//        redisTokenStore.setPrefix("master-order-push-backend-service:auth-token:");
//        return redisTokenStore;
//    }
//
//    @Bean
//    public JedisConnectionFactory createJedisConnectionFactory(RedisProperties properties) {
//
//        JedisPoolConfig config = new JedisPoolConfig();
//        config.setMaxIdle(properties.getRedisPoolMaxIdle());
//        config.setMaxWaitMillis(properties.getRedisPoolMaxWait());
//        config.setTestOnBorrow(properties.isRedisPoolTestOnBorrow());
//        config.setTestOnReturn(properties.isRedisPoolTestOnReturn());
//        config.setMaxTotal(properties.getRedisPoolMaxActive());
//        JedisConnectionFactory factory = new JedisConnectionFactory(config);
//
//        factory.setHostName(properties.getRedisHost());
//        factory.setPort(properties.getRedisPort());
//        factory.setDatabase(properties.getDatabase());
//
//        if (!Strings.isNullOrEmpty(properties.getRedisPassword())) {
//            factory.setPassword(properties.getRedisPassword());
//        }
//        return factory;
//    }
//
//    @Override
//    public void configure(ClientDetailsServiceConfigurer clientDetailsServiceConfigurer) throws Exception {
//        // 数据库存储配置信息+密码模式
//        serviceConfig(0, clientDetailsServiceConfigurer, dataSource, "password");
//    }
//
//    /**
//     * 服务配置，如授权方式，token过期时间等.
//     *
//     * @param flag                           内存和数据库标识，0：内存；1：数据库
//     * @param clientDetailsServiceConfigurer 配置器
//     * @param dataSource                     数据源
//     * @param grantType                      授权类型，password：密码模式；authorization_code：授权码模式
//     * @throws Exception 异常
//     */
//    private void serviceConfig(int flag, ClientDetailsServiceConfigurer clientDetailsServiceConfigurer, DataSource dataSource, String grantType) throws Exception {
//        if (flag == 1) {
//            clientDetailsServiceConfigurer.jdbc(dataSource);
//        } else {
//            clientDetailsServiceConfigurer
//                    .inMemory()
//                    .withClient(clientId)
//                    .secret(clientSecret)
//                    .authorities("ROLE_CLIENT")
//                    .authorizedGrantTypes("password", "refresh_token")
//                    .accessTokenValiditySeconds(accessTokenValiditySeconds)
//                    .redirectUris("https://www.wanshifu.com")
//                    .resourceIds("rid")
//                    .scopes("all")
//                    .authorizedGrantTypes("password");
//        }
//    }
//
//    @Override
//    public void configure(AuthorizationServerEndpointsConfigurer endpointsConfig) {
//        // 默认情况下，授权码存储在内存中：InMemoryAuthorizationCodeServices，
//        // 所以，不用配置
//        endpointsConfig.tokenStore(tokenStore)
//                .authenticationManager(authenticationManager)
//                .userDetailsService(myUserDetailsService);
//        // exceptionTranslator(extendOAuth2ResponseExceptionTranslator);
//    }
//
//    @Override
//    public void configure(AuthorizationServerSecurityConfigurer serverSecurityConfig) throws Exception {
//        serverSecurityConfig.allowFormAuthenticationForClients();
//        //在BasicAuthenticationFilter之前去加入一个过滤器
////        serverSecurityConfig.addTokenEndpointAuthenticationFilter(new IntegrationAuthenticationFilter());
//    }
//}