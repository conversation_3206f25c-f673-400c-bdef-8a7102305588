package com.wanshifu.master.order.push.domain.response.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 描述 :  查询师傅指标Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
@NoArgsConstructor
public class MasterQuotaResp {
    private String quotaCode;
    private String quotaName;
    private String quotaDesc;
    private String valueType;

    public List<EnumValue> enumValueList;

    @Data
    public static class EnumValue {
        private String code;
        private String name;
    }

    public MasterQuotaResp(String quotaCode, String quotaName, String quotaDesc, String valueType) {
        this.quotaCode = quotaCode;
        this.quotaName = quotaName;
        this.quotaDesc = quotaDesc;
        this.valueType = valueType;
    }
}