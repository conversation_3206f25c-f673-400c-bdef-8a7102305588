package com.wanshifu.master.order.push.domain.response.specialGroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

@ApiModel("推送策略详�?)
@Data
public class DetailResp {

    @ApiModelProperty("策略id")
    private Long strategyId;

    @ApiModelProperty(value = "业务线id")
    private Integer businessLineId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略描述")
    protected String strategyDesc;

    @ApiModelProperty(value = "区域级别,如city,country")
    protected String regionLevel;

    @ApiModelProperty(value = "城市id集合，多个用逗号分隔")
    private String cityIds;

    @ApiModelProperty(value = "服务id集合")
    private String serveIds;

    @ApiModelProperty(value = "服务集合")
    private List<String> serviceInfoList;

    @ApiModelProperty(value = "下单模式�?:报价招标�?:一口价�?:预付�?)
    protected String serveModels;

    @ApiModelProperty(value = "调度人群，多个用逗号分隔")
    private String pushGroups;

    @ApiModelProperty(value = "过滤人群")
    private String filterGroups;

    @ApiModelProperty(value = "延迟推送下一优先级的时间（分钟）")
    protected Integer delayMinutes;


}
