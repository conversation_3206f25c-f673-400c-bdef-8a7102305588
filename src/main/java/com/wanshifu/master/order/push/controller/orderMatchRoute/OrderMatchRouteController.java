package com.wanshifu.master.order.push.controller.orderMatchRoute;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.service.orderMatchRoute.OrderMatchRouteService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRoute")
public class OrderMatchRouteController {

    @Resource
    private OrderMatchRouteService orderMatchRouteService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderMatchRouteRqt rqt) {
        return orderMatchRouteService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderMatchRouteRqt rqt) {
        return orderMatchRouteService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public OrderMatchRouteDetailResp detail(@Valid @RequestBody OrderMatchRouteDetailRqt rqt) {
        return orderMatchRouteService.detail(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<OrderMatchRouteListResp> list(@Valid @RequestBody OrderMatchRouteListRqt rqt){
        return orderMatchRouteService.list(rqt);
    }


}
