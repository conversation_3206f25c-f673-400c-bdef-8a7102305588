package com.wanshifu.master.order.push.domain.request.common;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述 :  查询用户/师傅人群数据.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BigdataGroupListReq extends GroupListReq {

    /**
     * APPID取值
     * 1   智能运营
     * 2   师傅APP
     * 3   风控系统
     * 4   用户端
     * 5   OCS
     * 6   推单系统
     */
    private Integer appId = 6;

    /**
     * 启用状态 1、停用 2、启动 默认2-启用
     */
    private Integer enableStatus = 2;

    /**
     * 用户和师傅区分标志 1-用户 2-师傅
     */
    private Integer personaId;

}