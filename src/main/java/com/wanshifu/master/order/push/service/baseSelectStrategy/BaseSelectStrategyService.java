package com.wanshifu.master.order.push.service.baseSelectStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface BaseSelectStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    int enable(EnableRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    int delete(DeleteRqt rqt);
}