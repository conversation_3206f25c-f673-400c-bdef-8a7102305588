//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Collection;
//import java.util.Collections;
//import java.util.List;
//
//@Repository
//public class BackendMasterQuotaValueRepository extends BaseRepository<MasterQuotaValue> {
//
//
//    public List<MasterQuotaValue> selectByMasterQuotaIds(List<Long> enumMasterQuotaIds) {
//        if (CollectionUtils.isEmpty(enumMasterQuotaIds)) {
//            return Collections.emptyList();
//        }
//        Example example = new Example(MasterQuotaValue.class);
//        example.createCriteria().andIn("masterQuotaId", enumMasterQuotaIds);
//        return this.selectByExample(example);
//    }
//}