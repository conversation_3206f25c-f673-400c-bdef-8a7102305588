package com.wanshifu.master.order.push.service.permission;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetMenuListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetListResp;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;

import java.util.List;

public interface PermissionSetService {

    int add(AddPermissionSetRqt rqt);


    int update(UpdatePermissionSetRqt rqt);

    SimplePageInfo<GetPermissionSetListResp> list(GetPermissionSetListRqt rqt);


    List<GetMenuListResp> menuList(GetMenuListRqt rqt);


    GetPermissionSetDetailResp detail(GetPermissionSetDetailRqt rqt);


    List<GetPermissionListResp> permissionList();

    Integer delete(DeletePermissionSetRqt rqt);



}
