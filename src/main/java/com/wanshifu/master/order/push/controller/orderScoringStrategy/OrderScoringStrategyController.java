package com.wanshifu.master.order.push.controller.orderScoringStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.GetOrderScoringStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.OrderScoringStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;
import com.wanshifu.master.order.push.service.orderScoringStrategy.OrderScoringStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderScoringStrategy")
public class OrderScoringStrategyController {

    @Resource
    private OrderScoringStrategyService orderScoringStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public OrderScoringStrategyDetailResp detail(@Valid @RequestBody OrderScoringStrategyDetailRqt rqt) {
        return orderScoringStrategyService.detail(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<GetOrderScoringStrategyListResp> list(@Valid @RequestBody GetOrderScoringStrategyListRqt rqt){
        return orderScoringStrategyService.list(rqt);
    }


    /**
     * 查询匹配项
     *
     * @return
     */
    @GetMapping("/scoringItemList")
    public List<OrderScoringItemListResp> scoringItemList(@RequestParam(value = "itemName", required = false) String itemName) {
        return orderScoringStrategyService.scoringItemList(itemName);
    }


}
