package com.wanshifu.master.order.push.domain.request.common;

import lombok.Data;

import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-25 14:06
 */
@Data
public class IopGetInfoListByAccountIdsReq {

    public IopGetInfoListByAccountIdsReq(List<Long> accountIds) {
        this.accountIds = accountIds;
    }

    //旧Id和新accountId可以混合放进去
    private List<Long> accountIds;
    /**
     * 1-crm市场管理后台, 2-ocs管理后台, 3-智能运营平台，4-客服系统
     */
    private Integer productType = 2;
}