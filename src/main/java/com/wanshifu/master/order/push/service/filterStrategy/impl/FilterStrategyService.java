package com.wanshifu.master.order.push.service.filterStrategy.impl;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.filterStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface FilterStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    int enable(EnableRqt rqt);

    int delete(DeleteRqt rqt);
}