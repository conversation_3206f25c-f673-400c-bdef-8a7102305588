//package com.wanshifu.master.order.push.service.common;
//
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.dto.UserDetail;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpHeaders;
//import org.springframework.security.oauth2.common.OAuth2AccessToken;
//import org.springframework.security.oauth2.provider.OAuth2Authentication;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//import org.springframework.stereotype.Component;
//
//import jakarta.servlet.http.HttpServletRequest;
//import java.util.Optional;
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-25 18:02
// */
//@Slf4j
//@Component
//public class AuthHandler {
//
//    @Autowired
//    private HttpServletRequest request;
//    @Autowired
//    private TokenStore tokenStore;
//
//    public Long getLoginUserId() {
//        return Optional.ofNullable(this.getUserDetail()).map(UserDetail::getUserId).orElse(null);
//    }
//
//    public String getLoginUserName() {
//        return Optional.ofNullable(this.getUserDetail()).map(UserDetail::getUsername).orElse(null);
//    }
//
//    private UserDetail getUserDetail() {
//        String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
//        if (StringUtils.isBlank(authHeader)) {
//            return null;
//        }
//        try {
//            String tokenValue = authHeader.replace(OAuth2AccessToken.BEARER_TYPE, "").trim();
//            OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(tokenValue);
//            return (UserDetail) oAuth2Authentication.getUserAuthentication().getPrincipal();
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("解析登录用户失败!");
//        }
//        return null;
//    }
//}