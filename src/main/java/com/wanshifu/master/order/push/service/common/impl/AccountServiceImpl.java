package com.wanshifu.master.order.push.service.common.impl;

import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.request.common.GetAccountListRqt;
import com.wanshifu.master.order.push.domain.request.common.GetAllAccountReq;
import com.wanshifu.master.order.push.domain.response.common.AccountResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.service.common.AccountService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AccountServiceImpl implements AccountService{

    @Resource
    private IopAccountApi iopAccountApi;

    @Override
    public List<AccountResp> list(GetAccountListRqt rqt){
        IopAccountResp<List<IopAccountResp.AccountInfo>> accountInfoIopAccountResp = iopAccountApi.getAllAccount(new GetAllAccountReq(2));
        List<IopAccountResp.AccountInfo> accountInfoList = accountInfoIopAccountResp.getRetData();
        if(StringUtils.isNotBlank(rqt.getAccountName())){
            accountInfoList = accountInfoList.stream().filter(accountInfo -> rqt.getAccountName().equals(accountInfo.getUsername())).collect(Collectors.toList());
        }
        List<AccountResp> listResps = BeanCopyUtil.copyListProperties(accountInfoList, AccountResp.class, (s, t) -> {
            t.setAccountId(s.getAccountId());
            t.setAccountName(s.getUsername());
            InterprectChineseUtil.reflexEnum(t);
        });
        return listResps;
    }
}
