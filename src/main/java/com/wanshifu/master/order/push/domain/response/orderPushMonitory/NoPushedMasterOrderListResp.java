package com.wanshifu.master.order.push.domain.response.orderPushMonitory;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-07-11 17:00:01
 */
@Data
public class NoPushedMasterOrderListResp {

    private Long pushId;

    /**
     * 推单时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 业务�?
     */
    private String businessLine;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String street;

    /**
     * 服务类目
     */
    private String serveCategory;

    private String Level1NameServeCategory;
    private String Level2NameServeCategory;
    private String Level3NameServeCategory;


    /**
     * 服务类型
     */
    private String serveType;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 下单模式
     */
    private String appointType;

    /**
     * 推送路�?
     */
    private String pushStrategy;

    /**
     * 初筛师傅�?
     */
    private Integer baseSelectMasterNum;

    /**
     * 召回后师傅数
     */
    private Integer masterNumAfterFilter;

}
