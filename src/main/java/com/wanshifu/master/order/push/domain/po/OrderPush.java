package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 10:32
 */
@Data
@Table(name = "ltb_order_push")
public class OrderPush {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "push_id")
    private Long pushId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 师傅ID
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 指派类型(2:发布任务,3:直接雇佣,4:一口价)
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 来源客户端(site:网站,backend:后台,weixin:微信,ikea:宜家)
     */
    @Column(name = "order_from")
    private String orderFrom;

    /**
     * 下单人账号类型
     */
    @Column(name = "account_type")
    private String accountType;
    /**
     * 下单账号id（）
     */
    private Long fromAccount;

    /**
     * 订单标签(空字符串:默认,rigorous_selection:严选)
     */
    @Column(name = "order_label")
    private String orderLabel;

    /**
     * 报价数量
     */
    @Deprecated
    @Column(name = "offer_number")
    private Integer offerNumber;

    /**
     * 备注
     */
    private String note;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 报价时间
     */
    @Column(name = "offer_time")
    private Date offerTime;

    /**
     * 雇佣时间
     */
    @Column(name = "hire_time")
    private Date hireTime;

    /**
     * 报价限制(1:可以报价,2:不可报价)
     */
    @Column(name = "limit_offer")
    private Integer limitOffer;


    /**
     * 推送的地址级别(3:三级地址推单,4:四级地址推单)
     */
    @Column(name = "push_division_level")
    private Integer pushDivisionLevel;


    /**
     * 删除状态(1:删除0:未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 师傅第一次拉取待报价订单时间
     */
    @Column(name = "first_pull_time")
    private Date firstPullTime;

    /**
     * 师傅第一次查看待报价订单详情时间
     */
    @Column(name = "first_view_time")
    private Date firstViewTime;

    /**
     * 截止报价时间
     */
    @Column(name = "stop_offer_time")
    private Date stopOfferTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 到货状态(1:已到货,2:未到货),默认0
     */
    @Column(name = "is_arrived")
    private Integer isArrived;

    /**
     * 是否是意向单(1:是,0:不是),默认0
     */
    @Column(name = "is_intention")
    private Integer isIntention;

    /**
     * 订单区域(三级)
     */
    @Column(name = "order_division_id")
    private Long orderDivisionId;

    /**
     * 师傅纬度
     */
    @Column(name = "master_latitude")
    private BigDecimal masterLatitude;

    /**
     * 师傅经度
     */
    @Column(name = "master_longitude")
    private BigDecimal masterLongitude;

    /**
     * 推单距离(当时推单师傅和客户得距离),单位米
     */
    @Column(name = "push_distance")
    private Long pushDistance;

    /**
     * 推单距离类型 1：导航距离(默认) 2:直线距离
     */
    @Column(name = "push_distance_type")
    private Integer pushDistanceType;

    /**
     * 推送来源(1:智能推单,2:ocs后台)
     */
    @Column(name = "push_from")
    private Integer pushFrom;

    /**
     * 专属订单标记（1专属订单，2转换后的普通订单，0普通订单，默认0）
     */
    @Column(name = "exclusive_flag")
    private Integer exclusiveFlag;

    /**
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    @Column(name = "technique_type_ids")
    private String techniqueTypeIds;

    /**
     * 订单类目id
     */
    @Column(name = "category_id")
    private Integer categoryId;

    /**
     * 推单标识 0：正常推单 1：附近推单 4: 附近红包单
     */
    @Column(name = "push_flag")
    private Integer pushFlag;

    /**
     * 菜单类别 0：默认 1：指派专区, 2:悬赏分区
     */
    @Column(name = "menu_category")
    private Integer menuCategory;

    /**
     * 加急单标识 0、普通订单 1、加急单 2、加急单转成普通订单
     */
    @Column(name = "emergency_order_flag")
    private Integer emergencyOrderFlag;

    /**
     * 合作商标识 0、非合作商 1、家庭合作商
     */
    @Column(name = "agent_order_flag")
    private Integer agentOrderFlag;

    /**
     * 是否拉取订单距离,1:是,0:否
     *
     * 该字段用于旧的顺路单逻辑(用于判断是否需要重新计算订单跟师傅的距离)，顺路单已完成ES的改造，该字段废弃
     */
    @Column(name = "is_pull_order_distance")
    private Integer isPullOrderDistance;

    /**
     * 排序评分分值
     */
    @Column(name = "score")
    private BigDecimal score;

    /**
     * 干预排序评分
     */
    @Column(name = "exposure_score")
    private BigDecimal exposureScore;

    /**
     * 是否按照师傅和订单距离推送 1:是,0:否
     */
    @Column(name = "according_distance_push_flag")
    private Integer accordingDistancePushFlag;


    /**
     * 是否按照师傅技能推送 0:否,1:是
     */
    @Column(name = "according_technology_push_flag")
    private Integer accordingTechnologyPushFlag;

    /**
     * 竞争少标识(0:否;1:是)
     */
    @Column(name = "less_contend_flag")
    private Integer lessContendFlag;

    /**
     * 推单数据省下级地址id
     */
    @Column(name = "province_next_id")
    private Long provinceNextId;

    /**
     * 家庭样板城市订单标识，0：非样板城市订单，1：样板城市订单，2.样板城市订单转推普通师傅，3:样板城市订单主力师傅直接指派，默认0
     */
    @Column(name = "tmpl_city_flag")
    private Integer tmplCityFlag;

    /**
     * 家庭样板城市订单首页弹窗读取时间
     */
    @Column(name = "tmpl_city_tip_time")
    private Date tmplCityTipTime;


    /**
     * 必接订单标记
     */
    @Column(name = "must_order_flag")
    private Integer mustOrderFlag;


    /**
     * 是否查看，1：已查看，0：未查看
     */
    @Column(name = "is_pull_view")
    private Integer isPullView;
}
