package com.wanshifu.master.order.push.domain.response.orderMatchRoute;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class OrderMatchRouteDetailResp {

    private Integer routeId;

    private Integer businessLineId;

    private String routeName;

    private String routeDesc;

    private String orderPushFlag;

    private String orderPriorityMatchRule;

    private Long createAccountId;

    /**
     * 订单备用路由规则
     */
    @NotNull
    @Valid
    private List<CreateOrderMatchRouteRqt.MatchRule> orderStandbyMatchRule;



}
