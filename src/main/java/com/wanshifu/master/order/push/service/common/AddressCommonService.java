package com.wanshifu.master.order.push.service.common;

import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.base.address.domain.resp.RegionEntityResp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.request.common.GetSubListByDivisionIdReq;
import com.wanshifu.master.order.push.domain.response.address.AddressListResp;
import com.wanshifu.master.order.push.domain.response.common.GetSubListByDivisionIdResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述 :  地址公共服务.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-09 10:29
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddressCommonService {

    private final AddressApi addressApi;


    /**
     * 根据divisionIds批量获取地区信息
     *
     * @param divisionIds divisionIds
     * @return list
     */
    public List<Address> getDivisionInfoListByDivisionIds(String divisionIds) {
        if (StringUtils.isBlank(divisionIds)) {
            return Collections.emptyList();
        }
        try {
            return addressApi.getDivisionInfoListByDivisionIds(divisionIds);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoListByDivisionIds=====divisionIds={},e={}", divisionIds, e);
        }
        return Collections.emptyList();
    }

    public List<Address> getDivisionInfoListByDivisionIdsMatch(List<String> divisionList) {
        if (CollectionUtils.isEmpty(divisionList)) {
            return Collections.emptyList();
        }
        final ArrayList<Address> result = new ArrayList<>();
        final int total = divisionList.size();
        int size=400;
        try {
            for (int index = 0; index < total; index=index+size) {
                int end = index + size;
                if (end>total) {
                    end=total;
                }
                final List<String> sub = divisionList.subList(index, end);
                result.addAll(addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(sub,",")));
            }
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoListByDivisionIds=====divisionIds={},e={}", divisionList, e);
        }
        return result;
    }

    /**
     * 获取全国的省市区
     *
     * @param provinceId
     * @return 全国的省市区
     */
    public List<AddressListResp> addressList(Long cityId) {
        Long provinceIdTemp = null;
        if(cityId != null){
            Address address = addressApi.getParentDivisionInfoByDivisionId(cityId);
            if(address != null){
                provinceIdTemp = address.getDivisionId();
            }
        }
        RegionEntityResp provinceCityRegion = addressApi.getProvinceCityRegion();
        if (provinceCityRegion == null || CollectionUtils.isEmpty(provinceCityRegion.getChildren())) {
            return Collections.emptyList();
        }
        Long provinceId = provinceIdTemp;
        List<AddressListResp> regionEntity = this.getRegionEntity(provinceCityRegion.getChildren());
        if (provinceId != null && provinceId > 0) {
            return regionEntity.stream().filter(it -> Objects.equals(it.getDivisionId(), provinceId)).collect(Collectors.toList());
        }
        return regionEntity;
    }


    public List<AddressListResp> getCityList(Long cityId) {
        if(cityId == null){
            List<AddressListResp> addressListRespList = new ArrayList<>();
            RegionEntityResp provinceCityRegion = addressApi.getProvinceCityRegion();
            provinceCityRegion.getChildren().forEach(regionEntityResp -> {
                regionEntityResp.getChildren().forEach(resp -> {
                    addressListRespList.add(new AddressListResp(resp.getId(),resp.getName()));
                });
            });

            return addressListRespList;

        }
        Address address = addressApi.getDivisionInfoByDivisionId(cityId);
        if(address == null){
            return null;
        }

        List<Address> addressList = addressApi.getSubListByDivisionId(address.getParentId());

        if(CollectionUtils.isEmpty(addressList)){
            return null;
        }

        return addressList.stream().map(it -> new AddressListResp(it.getDivisionId(), it.getDivisionName())).collect(Collectors.toList());

    }

    private List<AddressListResp> getRegionEntity(List<RegionEntityResp> regionEntityResps) {
        if (CollectionUtils.isEmpty(regionEntityResps)) {
            return Collections.emptyList();
        }
        return regionEntityResps.stream().map(it -> new AddressListResp(it.getId(), it.getName(),
                this.getRegionEntity(it.getChildren()))).collect(Collectors.toList());
    }


    public List<GetSubListByDivisionIdResp> getSubListByDivisionId(GetSubListByDivisionIdReq rqt) {
        List<Address> addressList = addressApi.getSubListByDivisionId(rqt.getDivisionId());
        if (CollectionUtils.isEmpty(addressList)) {
            return Collections.emptyList();
        }
        return addressList.stream().map(it -> new GetSubListByDivisionIdResp(it.getDivisionId(), it.getDivisionName(),it.getDivisionLevel())).collect(Collectors.toList());    }






    }