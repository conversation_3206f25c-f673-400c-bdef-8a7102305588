package com.wanshifu.master.order.push.service.longTailStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.api.LongTailStrategyApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaByCodesRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaValueRqt;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailRule;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.longTailStrategy.LongTailStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyServiceImpl implements LongTailStrategyService {

    private final LongTailStrategyApi longTailStrategyApi;

    private final IopAccountApi iopAccountApi;

    // private final AuthHandler authHandler;

    private final MasterBigDataOpenApi masterBigDataOpenApi;

    private final CommonApi commonApi;

    private final ServeCommonService serveCommonService;

    /**
     * 策略管理-获取列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        final SimplePageInfo<LongTailStrategy> strategySimplePageInfo = longTailStrategyApi.list(rqt);
        final List<LongTailStrategy> strategySimplePageInfoList = strategySimplePageInfo.getList();
        List<Long> updateAccountIds = strategySimplePageInfoList.stream()
                .map(LongTailStrategy::getUpdateAccountId).distinct()
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(
                strategySimplePageInfoList, ListResp.class, (s, t) -> {
                    t.setLastUpdateAccountName(
                            Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                            .map(IopAccountResp.IopAccount::getChineseName).orElse(""));
                    if ("nearby_more".equals(s.getPushType())) {
                        t.setPushType("更多订单");
                    } else if ("bonus_order".equals(s.getPushType())) {
                        t.setPushType("红包订单");
                    } else if ("out_district".equals(s.getPushType())) {
                        t.setPushType("区县外订�?);
                    } else {
                        t.setPushType("");
                    }

                    InterprectChineseUtil.reflexEnum(t);
                });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(strategySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(strategySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(strategySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(strategySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    /**
     * 策略管理-创建长尾单策�?
     *
     * @param createRqt
     * @return
     */
    @Override
    public int create(CreateRqt createRqt) {
        createRqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyApi.create(createRqt);
    }

    /**
     * 策略管理-修改长尾单策�?
     *
     * @param updateRqt
     * @return
     */
    @Override
    public int update(UpdateRqt updateRqt) {
        updateRqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyApi.update(updateRqt);
    }

    /**
     * 策略管理-更新策略状态（启用/禁用�?
     *
     * @param enableRqt
     * @return
     */
    @Override
    public int updateStatus(EnableRqt enableRqt) {
        enableRqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return longTailStrategyApi.updateStatus(enableRqt);
    }

    /**
     * 策略管理-删除长尾单策�?
     *
     * @param deleteRqt
     * @return
     */
    @Override
    public int delete(DeleteRqt deleteRqt) {
        return longTailStrategyApi.delete(deleteRqt);
    }

    /**
     * 策略管理-策略详情
     *
     * @param detailRqt
     * @return
     */
    @Override
    public DetailResp detail(DetailRqt detailRqt) {
        final LongTailStrategy detail = longTailStrategyApi.detail(detailRqt);
        DetailResp detailResp=new DetailResp();
        BeanCopyUtil.copyProperties(detail, detailResp);
        detailResp.setPushType(detail.getPushType());
        detailResp.setLongTailRule(JSON.parseObject(detail.getStrategyJson(), DetailResp.LongTailRule.class));

        Integer businessLineId = detail.getBusinessLineId();
        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 3) {
                businessLineId = 1;
            } else if (businessLineId == 999) {
                businessLineId = 2;
            }
        }

        DetailResp.LongTailRule.AppointGroup appointGroup = detailResp.getLongTailRule().getAppointGroup();

        if(appointGroup != null && CollectionUtils.isNotEmpty(appointGroup.getItemList())){
            //配置中的师傅人群id
            List<Long> masterGroupIds = appointGroup.getItemList().stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());


            Map<Long, String> groupNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }

            Map<Long,String> finalGroupNameMap = groupNameMap;


            appointGroup.getItemList().forEach(itemVo -> {
                //师傅人群
                String itemTitle = "师傅人群";
                List<DetailResp.TermItem> groupTermItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包�?, "not_in"));
                List<DetailResp.ValueItem> valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(itemVo.getItemValue()), "师傅人群"), itemVo.getItemValue()));
                itemVo.setItemTitle(itemTitle);
                itemVo.setTermList(groupTermItems);
                itemVo.setValueList(valueItems);
                itemVo.setItemType("master_group");
            });
        }

        if (!Strings.isNullOrEmpty(detail.getFilterRule())) {
            List<DetailResp.RuleItem> ruleItems = JSON.parseArray(detail.getFilterRule(), DetailResp.RuleItem.class);
            //配置中的指标code
            List<String> quotaCodes = ruleItems.stream().map(DetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).map(DetailResp.FilterRuleItem::getItemName).distinct().collect(Collectors.toList());

            //配置中的师傅人群id
            List<Long> masterGroupIds = ruleItems.stream().map(DetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());
            Map<Long, String> groupNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }

            Map<String, MasterQuota> masterQuotaMap = Maps.newHashMap();
            Map<Long, List<MasterQuotaValue>> masterQuotaValueMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(quotaCodes)) {
                GetMasterQuotaByCodesRqt getMasterQuotaByCodesRqt = new GetMasterQuotaByCodesRqt();
                getMasterQuotaByCodesRqt.setQuotaCodes(quotaCodes);
                getMasterQuotaByCodesRqt.setBusinessLineId(businessLineId);

                List<MasterQuota> masterQuotas = commonApi.getMasterQuotaByCodes(getMasterQuotaByCodesRqt);
                masterQuotaMap = masterQuotas.stream().collect(Collectors.toMap(MasterQuota::getQuotaCode, Function.identity()));
                List<Long> masterQuotaIds = masterQuotas.stream().map(MasterQuota::getQuotaId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(masterQuotaIds)) {
                    GetMasterQuotaValueRqt getMasterQuotaValueRqt = new GetMasterQuotaValueRqt();
                    getMasterQuotaValueRqt.setMasterQuotaIdList(masterQuotaIds);
                    masterQuotaValueMap = commonApi.getMasterQuotaValue(getMasterQuotaValueRqt).stream().collect(Collectors.groupingBy(MasterQuotaValue::getMasterQuotaId));
                }

            }

            Set<Long> serveIds = ruleItems.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                    .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                    .flatMap(it -> it.getServeIdList().stream())
                    .collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

            Map<Long, String> serveInfoMap =Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(serveIds)){
                serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                        .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
            }
            Map<Long, String> finalServeInfoMap = serveInfoMap;
            ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

                List<LinkedList<Long>> serveIdListList = item.getServeIdList();
                if(CollectionUtils.isNotEmpty(serveIdListList)){
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
            })));

            Map<Long, String> finalGroupNameMap = groupNameMap;
            Map<String, MasterQuota> finalMasterQuotaMap = masterQuotaMap;
            Map<Long, List<MasterQuotaValue>> finalMasterQuotaValueMap = masterQuotaValueMap;
            ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getFilterRule().getItemList()).ifPresent(it -> it.forEach(item -> {
                String quotaCode = item.getItemName();
                String itemTitle = "";
                List<DetailResp.TermItem> termItems = Lists.newArrayList();
                List<DetailResp.ValueItem> valueItems = Lists.newArrayList();

                if (StringUtils.equals(quotaCode, "master_group")) {
                    //师傅人群
                    itemTitle = "师傅人群";
                    termItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包�?, "not_in"));
                    valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(item.getItemValue()), "师傅人群"), item.getItemValue()));
                } else {
                    MasterQuota masterQuota = finalMasterQuotaMap.get(quotaCode);
                    if (masterQuota != null) {
                        itemTitle = masterQuota.getQuotaName();
                        if (StringUtils.equals(masterQuota.getValueType(), "enum_value")) {
                            termItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包�?, "not_in"));
                            valueItems = finalMasterQuotaValueMap.getOrDefault(masterQuota.getQuotaId(), Collections.emptyList()).stream().map(quotaCodeValue -> new DetailResp.ValueItem(quotaCodeValue.getName(), quotaCodeValue.getCode())).collect(Collectors.toList());
                        } else {
                            termItems = Lists.newArrayList(new DetailResp.TermItem("大于等于", ">="), new DetailResp.TermItem("大于", ">"), new DetailResp.TermItem("等于", "="), new DetailResp.TermItem("小于", "<"), new DetailResp.TermItem("小于等于", "<="));
                        }
                    }
                }
                item.setItemTitle(itemTitle);
                item.setTermList(termItems);
                item.setValueList(valueItems);
            })));

            detailResp.getLongTailRule().setRuleList(ruleItems);
        }



        return detailResp;
    }

}
