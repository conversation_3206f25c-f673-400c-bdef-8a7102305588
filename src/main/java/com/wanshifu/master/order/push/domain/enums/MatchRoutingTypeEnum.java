package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 指派类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum MatchRoutingTypeEnum {

    /**
     * 直接雇佣
     */
    GENERIC_ROUTING("generic_routing","通用路由"),

    NORMAL_ROUTING("normal_routing","普通路由"),

    PRICE_PRIORITY_ROUTING("price_priority_routing","价格优先路由");


    public final String code;

    public final String name;

    MatchRoutingTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, MatchRoutingTypeEnum> valueMapping = new HashMap<>((int) (MatchRoutingTypeEnum.values().length / 0.75));

    static {
        for (MatchRoutingTypeEnum instance : MatchRoutingTypeEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static MatchRoutingTypeEnum asValue(String value) {
        return valueMapping.get(value);
    }

    public static Boolean isInclude(Integer value) {
        if(Objects.isNull(value)) {
            return false;
        }
        for (MatchRoutingTypeEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

}
