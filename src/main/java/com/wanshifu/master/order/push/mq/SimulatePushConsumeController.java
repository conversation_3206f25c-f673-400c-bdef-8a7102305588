package com.wanshifu.master.order.push.mq;

import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.SimulatePushRqt;
import com.wanshifu.master.order.push.service.simulatePush.OrderSimulatePushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 推单
 */
@Component
@Topic("${wanshifu.rocketMQ.order-simulate-push-topic}")
@Slf4j
public class SimulatePushConsumeController {


    private static final String SIMULATE_PUSH = "simulate_push";


    @Resource
    private OrderSimulatePushService orderSimulatePushService;



    /**
     * 测算推单
     *
     * @param simulatePushRqt
     * @return
     */
    @Tag(value = {SIMULATE_PUSH}, desc = "测算推单")
    public int simulatePush(@Validated @MessageBody SimulatePushRqt simulatePushRqt) {
        return orderSimulatePushService.simulatePush(simulatePushRqt);
    }


}
