package com.wanshifu.master.order.push.service.common;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Sets;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.*;
import com.wanshifu.order.config.domains.po.Serve;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述 : 商品服务 .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-23 16:41
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServeCommonService {


    private final ServeServiceApi serveServiceApi;

    public List<Serve> getLevel1ServeByBusinessLineId(Integer businessLineId) {
        ServeQueryReq serveQueryReq = new ServeQueryReq();
        serveQueryReq.setBusinessLineId(businessLineId);
        serveQueryReq.setLevel(1);
        serveQueryReq.setParentIds("0");
        return serveServiceApi.query(serveQueryReq);
    }

    public List<TreeServe> getTreeServe(Long businessLineId) {
        getTreeServesReq getTreeServesReq = new getTreeServesReq();
        getTreeServesReq.setBusinessLineId(businessLineId);
        SimplePageInfo<TreeServe> treeServes = serveServiceApi.getTreeServes(getTreeServesReq);
        return treeServes.getList();
    }

    public List<ServeBaseInfoResp> getServeBaseInfoByParentIdsAndBusinessLineId(Set<Long> parentIdSet, Long businessLineId) {

        List<ServeBaseInfoResp> serveBaseInfoRespList = new ArrayList<>();
        List<List<Long>> serveIdList = averageAssign(new ArrayList<>(parentIdSet),200);
        serveIdList.forEach(serveIds -> {
            ServeParentIdSetReq serveParentIdSetReq = new ServeParentIdSetReq();
            serveParentIdSetReq.setParentIdSet(new HashSet<>(serveIds));
            serveParentIdSetReq.setBusinessLineId(businessLineId);
            serveBaseInfoRespList.addAll(serveServiceApi.getServeBaseInfoByParentIdsAndBusinessLineId(serveParentIdSetReq));
        });

        return serveBaseInfoRespList;

    }

    public List<ServeBaseInfoResp> getServeBaseInfoByServeIdSet(Set<Long> serveIdSet) {
        if (CollectionUtils.isEmpty(serveIdSet)) {
            return Collections.emptyList();
        }

        List<ServeBaseInfoResp> serveBaseInfoRespList = new ArrayList<>();
        List<List<Long>> serveIdList = averageAssign(new ArrayList<>(serveIdSet),100);
        serveIdList.forEach(serveIds -> {
            serveBaseInfoRespList.addAll(serveServiceApi.getServeBaseInfo(new HashSet<>(serveIds)));
        });

        return serveBaseInfoRespList;
    }

    public static <T> List<List<T>> averageAssign(List<T> source, int splitItemNum) {
        List<List<T>> result = new ArrayList<List<T>>();

        if (source != null && source.size() > 0 && splitItemNum > 0) {
            if (source.size() <= splitItemNum) {
                // 源List元素数量小于等于目标分组数量
                result.add(source);
            } else {
                // 计算拆分后list数量
                int splitNum = (source.size() % splitItemNum == 0) ? (source.size() / splitItemNum) : (source.size() / splitItemNum + 1);

                List<T> value = null;
                for (int i = 0; i < splitNum; i++) {
                    if (i < splitNum - 1) {
                        value = source.subList(i * splitItemNum, (i + 1) * splitItemNum);
                    } else {
                        // 最后一�?
                        value = source.subList(i * splitItemNum, source.size());
                    }
                    result.add(value);
                }
            }
        }
        return result;
    }


    public List<ServeBaseInfoResp> getServeBaseInfoByGoodsId(Long categoryId) {
        ServeIdSetGoodsIdReq serveIdSetGoodsIdReq = new ServeIdSetGoodsIdReq();
        serveIdSetGoodsIdReq.setGoodsId(categoryId);
        return serveServiceApi.getServeBaseInfoByServeIdAndGoodsId(serveIdSetGoodsIdReq);
    }


    public List<ServeBaseInfoResp> getServeBaseInfoByGoodsId(Long businessLineId,Set<Long> categorySet) {
        final ServeGoodsIdSetReq serveGoodsIdSetReq = new ServeGoodsIdSetReq();
        serveGoodsIdSetReq.setBusinessLineId(businessLineId);
        serveGoodsIdSetReq.setGoodsIdSet(categorySet);
        return serveServiceApi.getServeBaseInfoByGoodIdSet(serveGoodsIdSetReq);
    }

    public String getServeIds(List<List<Long>> serveIds) {
        Set<Long> serveIdList = Sets.newHashSet();
        for (List<Long> sub : serveIds) {
            if (CollectionUtils.isEmpty(sub)) {
                continue;
            }
            Long last = sub.get(sub.size() - 1);
            serveIdList.add(last);
        }
        if (CollectionUtil.isEmpty(serveIdList)) {
            return  "";
        }
        Set<Long> serveIdSet = serveServiceApi.getLeafServeIdsByServeIds(serveIdList);
        if (CollectionUtil.isEmpty(serveIdSet)) {
            return "";
        } else {
            return serveIdSet.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
    }

}
