package com.wanshifu.master.order.push.api.iop;

import com.wanshifu.master.order.push.domain.request.common.*;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 描述 :  iop账号服务.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-25 13:57
 */
@FeignClient(value = "iop-account-service-api",
        url = "${wanshifu.iop-account-service.url}",
        contextId = "iopAccountApi",
        configuration = {DefaultEncoder.class, DefaultErrorDecode.class})
public interface IopAccountApi {

    /**
     * 登录
     *
     * @param loginReq
     * @return
     */
    @PostMapping("/account/login")
    IopAccountResp<IopAccountResp.IopAccount> login(@RequestBody IopAccountLoginReq loginReq);

    /**
     * 登出
     *
     * @param logoutReq
     * @return
     */
    @PostMapping("/account/logout")
    IopAccountResp<?> logout(@RequestBody IopAccountLogoutReq logoutReq);

    /**
     * 根据accountIds批量查询账号信息
     *
     * @param getInfoListByAccountIdsReq
     * @return
     */
    @PostMapping("/account/getInfoListByAccountIds")
    IopAccountResp<List<IopAccountResp.IopAccount>> getInfoListByAccountIds(@RequestBody IopGetInfoListByAccountIdsReq getInfoListByAccountIdsReq);

    /**
     * 根据accountId获取用户信息
     *
     * @param getInfoListByAccountIdsReq
     * @return
     */
    @PostMapping("/account/getUserInfoByAccountId")
    IopAccountResp<IopAccountResp.IopAccount> getUserInfoByAccountId(@RequestBody IopGetInfoListByAccountIdReq getInfoListByAccountIdsReq);

    /**
     * 根据accountId获取用户信息
     *
     * @param getAllAccount
     * @return
     */
    @PostMapping("/account/getAllAccount")
    IopAccountResp<List<IopAccountResp.AccountInfo>> getAllAccount(@RequestBody GetAllAccountReq rqt);
}