package com.wanshifu.master.order.push.service.pushNotice;

import com.wanshifu.master.notice.domains.po.PushNoticeLimitConfig;
import com.wanshifu.master.order.push.domain.request.pushnotice.NoticeLimitConfigUpdateRqt;


import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/21 18:05
 */
public interface PushNoticeLimitConfigService {

    List<PushNoticeLimitConfig> listByBusinessLineId(Integer businessLineId);

    void updateByList(List<NoticeLimitConfigUpdateRqt> configList);
}
