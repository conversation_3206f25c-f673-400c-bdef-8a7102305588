package com.wanshifu.master.order.push.controller.permission;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetMenuListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetListResp;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;
import com.wanshifu.master.order.push.service.permission.PermissionSetService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("permissionSet")
public class PermissionSetController {

    @Resource
    private PermissionSetService permissionSetService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddPermissionSetRqt rqt) {
        return permissionSetService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdatePermissionSetRqt rqt) {
        return permissionSetService.update(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<GetPermissionSetListResp> list(@Valid @RequestBody GetPermissionSetListRqt rqt) {
        return permissionSetService.list(rqt);
    }


    @PostMapping(value = "detail")
    public GetPermissionSetDetailResp detail(@Valid @RequestBody GetPermissionSetDetailRqt rqt) {
        return permissionSetService.detail(rqt);
    }


    @PostMapping(value = "menuList")
    public List<GetMenuListResp> menuList(@Valid @RequestBody GetMenuListRqt rqt) {
        return permissionSetService.menuList(rqt);
    }


    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeletePermissionSetRqt rqt) {
        return permissionSetService.delete(rqt);
    }





}
