package com.wanshifu.master.order.push.service.permission.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.PermissionSetApi;
import com.wanshifu.master.order.push.api.RoleApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.Permission;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetMenuListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetDetailResp;
import com.wanshifu.master.order.push.domain.response.permission.GetPermissionSetListResp;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;
import com.wanshifu.master.order.push.domain.rqt.role.BatchRoleListRqt;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.permission.PermissionSetService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PermissionSetServiceImpl implements PermissionSetService {

    @Resource
    private PermissionSetApi permissionSetApi;

    @Resource
    private RoleApi roleApi;

    @Resource
    private IopAccountApi iopAccountApi;

//    @Resource
//    private AuthHandler authHandler;


    @Override
    public int add(AddPermissionSetRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return permissionSetApi.add(rqt);
    }


    @Override
    public int update(UpdatePermissionSetRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return permissionSetApi.update(rqt);
    }


    @Override
    public SimplePageInfo<GetPermissionSetListResp> list(GetPermissionSetListRqt rqt){

        SimplePageInfo<PermissionSet> simplePageInfo = permissionSetApi.list(rqt);
        List<PermissionSet> permissionSetList = simplePageInfo.getList();

        SimplePageInfo<GetPermissionSetListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(simplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(simplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(simplePageInfo.getPageSize());

        if(!CollectionUtils.isNotEmpty(permissionSetList)){
            return listRespSimplePageInfo;
        }

        List<Integer> roleIdList = permissionSetList.stream().map(PermissionSet::getRoleList)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .map(Integer::parseInt).collect(Collectors.toList());

        BatchRoleListRqt batchRoleListRqt = new BatchRoleListRqt();
        batchRoleListRqt.setRoleIdList(roleIdList);
        List<Role> roleList = roleApi.batchRoleList(batchRoleListRqt);

        Map<Integer, String> roleNameMap = roleList.stream().collect(Collectors.toMap(Role::getRoleId, Role::getRoleName));

        List<Long> updateAccountIds = permissionSetList.stream().map(PermissionSet::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }

        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<GetPermissionSetListResp> listResps = BeanCopyUtil.copyListProperties(permissionSetList, GetPermissionSetListResp.class, (s, t) -> {

            t.setRoleNames(Arrays.stream(s.getRoleList().split(",")).map(it -> roleNameMap.getOrDefault(Integer.parseInt(it), it)).collect(Collectors.joining(",")));
            t.setPermissionSetType("功能权限");
            InterprectChineseUtil.reflexEnum(t);
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });


        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }


    @Override
    public List<GetMenuListResp> menuList(GetMenuListRqt rqt){

        List<Permission> allPermissionList = permissionSetApi.allPermissionList();

        if(StringUtils.isNotBlank(rqt.getMenuName())){
            List<Permission> currentPermissionList = new ArrayList<>();
            List<Permission> permissionList = permissionSetApi.menuList(rqt);
            permissionList.forEach(permission -> currentPermissionList.addAll(getPermissionList(allPermissionList,permission)));
            List<Permission> lastPermissionList = currentPermissionList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Permission::getPermissionId))), ArrayList::new));
            return getMenuListResp(lastPermissionList);
        }else{
            return getMenuListResp(allPermissionList);
        }

    }


    @Override
    public GetPermissionSetDetailResp detail(GetPermissionSetDetailRqt rqt){
        PermissionSet permissionSet = permissionSetApi.detail(rqt);
        if(permissionSet  == null){
            return null;
        }
        GetPermissionSetDetailResp resp = new GetPermissionSetDetailResp();
        resp.setPermissionSetId(permissionSet.getPermissionSetId());
        resp.setPermissionSetName(permissionSet.getPermissionSetName());
        resp.setPermissionSetDesc(permissionSet.getPermissionSetDesc());
        resp.setMenuList(JSON.parseArray(permissionSet.getPermissionSet(), GetPermissionSetDetailResp.PermissionMenu.class));

        List<Integer> roleIdList = Arrays.stream(Optional.ofNullable(permissionSet.getRoleList())
                .orElse("0").split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());

        BatchRoleListRqt batchRoleListRqt = new BatchRoleListRqt();
        batchRoleListRqt.setRoleIdList(roleIdList);
        List<Role> roleList = roleApi.batchRoleList(batchRoleListRqt);
        Map<Integer, String> roleNameMap = roleList.stream().collect(Collectors.toMap(Role::getRoleId, Role::getRoleName));

        IopAccountResp<List<IopAccountResp.IopAccount>> iopAccountResp = iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(Collections.singletonList(permissionSet.getUpdateAccountId())));
        List<IopAccountResp.IopAccount> iopAccountList = Optional.ofNullable(iopAccountResp)
                .map(IopAccountResp::getRetData).orElse(Collections.emptyList());

        IopAccountResp.IopAccount account = CollectionUtils.getFirstSafety(iopAccountList);


        List<GetPermissionSetDetailResp.RoleInfo> roleInfoList = new ArrayList<>();
        roleIdList.forEach(roleId -> {
            GetPermissionSetDetailResp.RoleInfo roleInfo = new GetPermissionSetDetailResp.RoleInfo();
            roleInfo.setRoleId(roleId);
            roleInfo.setRoleName(roleNameMap.getOrDefault(roleId,String.valueOf(roleId)));
            roleInfo.setOperateTime(permissionSet.getUpdateTime());
            roleInfo.setOperateAccountName(account != null ? account.getChineseName() : String.valueOf(permissionSet.getUpdateAccountId()));
            roleInfoList.add(roleInfo);
        });

        resp.setRoleList(roleInfoList);
        return resp;

    }

    @Override
    public List<GetPermissionListResp> permissionList(){
        GetPermissionListRqt rqt = new GetPermissionListRqt();
        rqt.setAccountId(UserInfoUtils.getCurrentLoginAccountId());
        List<Permission> permissionList = permissionSetApi.permissionList(rqt);

        List<Permission> currentPermissionList = new ArrayList<>();
        currentPermissionList.addAll(permissionList);

        List<Permission> allPermissionList = permissionSetApi.allPermissionList();

        List<Permission> menuPermissionList = permissionList.stream().filter(permission -> permission.getPermissionType() == 1).collect(Collectors.toList());


        menuPermissionList.forEach(permission -> currentPermissionList.addAll(getParentPermissionList(allPermissionList,permission)));

        List<Permission> lastPermissionList = currentPermissionList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Permission::getPermissionId))), ArrayList::new));

        return getPermissionListResp(lastPermissionList);
    }


    private List<GetPermissionListResp> getPermissionListResp(List<Permission> permissionList){
        List<Permission> menuList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId() == 0)).sorted((permission1,permission2) ->{
            return permission1.getOrderNum() - permission2.getOrderNum();
        }).collect(Collectors.toList());

        List<GetPermissionListResp> getPermissionListRespList = new ArrayList<>();
        menuList.forEach(menuPermission -> getPermissionListRespList.add(getPermissionListResp(permissionList,menuPermission)));
        return getPermissionListRespList;
    }


    private GetPermissionListResp getPermissionListResp(List<Permission> permissionList, Permission menuPermission){
        GetPermissionListResp getPermissionListResp = new GetPermissionListResp();
        getPermissionListResp.setName(menuPermission.getPermissionName());
        getPermissionListResp.setPath(menuPermission.getUrl());
        List<Permission> subMenuPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(subMenuPermissionList)){
            List<GetPermissionListResp> subMenuList = new ArrayList<>();
            subMenuPermissionList.forEach(subMenuPermission -> {
                subMenuList.add(getPermissionListResp(permissionList,subMenuPermission));
            });
            getPermissionListResp.setRoutes(subMenuList);
        }else{
        }

        List<Permission> buttonPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 2 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(buttonPermissionList)){
            List<GetPermissionListResp.Button> buttonList = new ArrayList<>();
            buttonPermissionList.forEach(buttonPermssion -> buttonList.add(new GetPermissionListResp.Button(buttonPermssion.getPermissionCode(),buttonPermssion.getPermissionName())));
            getPermissionListResp.setButtonList(buttonList);
        }

        List<Permission> tabPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 3 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(tabPermissionList)){
            List<GetPermissionListResp.Tab> tabList = new ArrayList<>();
            tabPermissionList.forEach(tabPermission -> tabList.add(new GetPermissionListResp.Tab(tabPermission.getPermissionCode(),tabPermission.getPermissionName())));
            getPermissionListResp.setTabList(tabList);
        }

        return getPermissionListResp;
    }




    private List<GetMenuListResp> getMenuListResp(List<Permission> permissionList){
        List<Permission> menuList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId() == 0)).sorted((permission1,permission2) ->{
            return permission1.getOrderNum() - permission2.getOrderNum();
        }).collect(Collectors.toList());

        List<GetMenuListResp> getMenuListRespList = new ArrayList<>();
        menuList.forEach(menuPermission -> getMenuListRespList.add(getMenuListResp(permissionList,menuPermission)));
        return getMenuListRespList;
    }

    private List<Permission> getPermissionList(List<Permission> allPermissionList,Permission permission){
        List<Permission> permissionList = new ArrayList<>();
        permissionList.add(permission);
        getParentPermission(allPermissionList,permissionList,permission);
        getChildPermission(allPermissionList,permissionList,permission);
        return permissionList;
    }

    private List<Permission> getParentPermissionList(List<Permission> allPermissionList,Permission permission){
        List<Permission> permissionList = new ArrayList<>();
        getParentPermission(allPermissionList,permissionList,permission);
        return permissionList;
    }

    private void getParentPermission(List<Permission> allPermissionList,List<Permission> permissionList,Permission permission){
        if(permission.getParentPermissionId() != null && permission.getParentPermissionId() > 0){
            Permission parentPermission = allPermissionList.stream().filter(permissionTemp -> permissionTemp.getPermissionId().equals(permission.getParentPermissionId())).findFirst().orElse(null);
            if(parentPermission != null){
                permissionList.add(parentPermission);
                getParentPermission(allPermissionList,permissionList,parentPermission);
            }
        }
    }

    public void getChildPermission(List<Permission> allPermissionList,List<Permission> permissionList,Permission permission){
        List<Permission> childPermissionList = allPermissionList.stream().filter(permissionTemp -> permissionTemp.getParentPermissionId().equals(permission.getPermissionId())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(childPermissionList)){
            permissionList.addAll(childPermissionList);
            childPermissionList.forEach(childPermission -> getChildPermission(allPermissionList,permissionList,childPermission));
        }

    }


    private GetMenuListResp getMenuListResp(List<Permission> permissionList,Permission menuPermission){
        GetMenuListResp getMenuListResp = new GetMenuListResp();
        getMenuListResp.setMenuId(menuPermission.getPermissionId());
        getMenuListResp.setMenuName(menuPermission.getPermissionName());
        getMenuListResp.setMenuLevel(menuPermission.getPermissionLevel());
        List<Permission> subMenuPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(subMenuPermissionList)){
            List<GetMenuListResp> subMenuList = new ArrayList<>();
            subMenuPermissionList.forEach(subMenuPermission -> {
                subMenuList.add(getMenuListResp(permissionList,subMenuPermission));
            });
            getMenuListResp.setSubMenu(subMenuList);
            getMenuListResp.setIsLeaf(false);
        }else{
            getMenuListResp.setIsLeaf(true);
        }

        List<Permission> buttonPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 2 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(buttonPermissionList)){
            List<GetMenuListResp.Button> buttonList = new ArrayList<>();
            buttonPermissionList.forEach(buttonPermssion -> buttonList.add(new GetMenuListResp.Button(buttonPermssion.getPermissionId(),buttonPermssion.getPermissionName())));
            getMenuListResp.setButtonList(buttonList);
        }

        List<Permission> tabPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 3 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(tabPermissionList)){
            List<GetMenuListResp.Tab> tabList = new ArrayList<>();
            tabPermissionList.forEach(tabPermission -> tabList.add(new GetMenuListResp.Tab(tabPermission.getPermissionId(),tabPermission.getPermissionName())));
            getMenuListResp.setTabList(tabList);
        }

        return getMenuListResp;
    }


    @Override
    public  Integer delete(DeletePermissionSetRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return permissionSetApi.delete(rqt);
    }






}
