package com.wanshifu.master.order.push.service.orderPushMonitor.impl;


import com.alibaba.fastjson.JSON;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.BaseSelectStrategyApi;
import com.wanshifu.master.order.push.api.FilterStrategyApi;
import com.wanshifu.master.order.push.api.OrderPushMonitorApi;
import com.wanshifu.master.order.push.api.SortingStrategyApi;
import com.wanshifu.master.order.push.domain.common.PushStrategySnapshot;
import com.wanshifu.master.order.push.domain.enums.AppointTypeEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFromTypeEnum;
import com.wanshifu.master.order.push.domain.enums.ServeTypeEnum;
import com.wanshifu.master.order.push.domain.po.*;

import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.NoPushedMasterOrderListResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.SnapshotRqt;
import com.wanshifu.master.order.push.domain.vo.orderPushMonitor.NoPushedMasterOrderListExcelVo;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.orderPushMonitor.OrderPushMonitorService;
import com.wanshifu.order.config.api.GoodsServiceApi;
import com.wanshifu.order.config.domains.po.Goods;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderPushMonitorServiceImpl implements OrderPushMonitorService {

    private  static Map<Long,String> businessLineMap = new HashMap<>();

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private GoodsCommonService goodsCommonService;


    @Resource
    private GoodsServiceApi goodsServiceApi;

    @Resource
    private BaseSelectStrategyApi baseSelectStrategyApi;


    @Resource
    private FilterStrategyApi filterStrategyApi;


    @Resource
    private SortingStrategyApi sortingStrategyApi;

    static {
        businessLineMap.put(1L,"成品业务线");
        businessLineMap.put(2L,"家庭业务线");
        businessLineMap.put(3L,"创新业务线");
    }


    @Resource
    private OrderPushMonitorApi orderPushMonitorApi;

    @Override
    public SimplePageInfo<NoPushedMasterOrderListResp> noPushedMasterOrderList(NoPushedMasterOrderListRqt rqt){

        parseParams(rqt);

        SimplePageInfo<PushOrderList> simplePageInfo = orderPushMonitorApi.noPushedMasterOrderList(rqt);
        List<PushOrderList> pushOrderListList = simplePageInfo.getList();

        List<NoPushedMasterOrderListResp> respList = null;

        if(CollectionUtils.isNotEmpty(pushOrderListList)){
            respList = this.buildNoPushedMasterOrderListResp(pushOrderListList);
        }

        SimplePageInfo<NoPushedMasterOrderListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(respList);
        listRespSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        listRespSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        listRespSimplePageInfo.setTotal(simplePageInfo.getTotal());
        listRespSimplePageInfo.setPages(simplePageInfo.getPages());
        return listRespSimplePageInfo;

    }

    private void parseParams(NoPushedMasterOrderListRqt rqt){

        if(StringUtils.isNotBlank(rqt.getOrderFrom())){
            rqt.setOrderFromList(
                    Arrays.stream(Optional.ofNullable(rqt.getOrderFrom())
                            .orElse("0").split(",")).map(String::valueOf)
                            .collect(Collectors.toList()));
        }


        if(StringUtils.isNotBlank(rqt.getAppointType())){
            rqt.setAppointTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getAppointType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getServeType())){
            rqt.setServeTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getServeType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

    }

    private List<NoPushedMasterOrderListResp> buildNoPushedMasterOrderListResp(List<PushOrderList> pushOrderListList){


        Set<Long> secondDivisionIdList = pushOrderListList.stream().map(PushOrderList::getSecondDivisionId).collect(Collectors.toSet());
        List<Address> cityAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(secondDivisionIdList,","));
        Map<Long, String> cityDivisionNameMap = cityAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));


        Set<Long> thirdDivisionIdList = pushOrderListList.stream().map(PushOrderList::getThirdDivisionId).collect(Collectors.toSet());
        List<Address> districtAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(thirdDivisionIdList,","));
        Map<Long, String> districtDivisionNameMap = districtAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));

        Set<Long> fourthDivisionIdList = pushOrderListList.stream().map(PushOrderList::getFourthDivisionId).collect(Collectors.toSet());
        fourthDivisionIdList.remove(0L);
        fourthDivisionIdList.remove(null);
        Map<Long, String>  tempStreetDivisionNameMap = null;
        if(CollectionUtils.isNotEmpty(fourthDivisionIdList)){
            List<Address> streetAddressList = addressCommonService.getDivisionInfoListByDivisionIds(StringUtils.join(fourthDivisionIdList,","));
            tempStreetDivisionNameMap = streetAddressList.stream().collect(Collectors.toMap(Address::getDivisionId, Address::getDivisionName));
        }

        Set<String> childGoodsCategoryIdsList = pushOrderListList.stream().map(PushOrderList::getChildGoodsCategoryIds).collect(Collectors.toSet());

        List<Long> goodsIdList = new ArrayList<>();
        childGoodsCategoryIdsList.forEach(childGoodsCategoryIds ->{
            if(StringUtils.isNotBlank(childGoodsCategoryIds)){
                goodsIdList.addAll(Arrays.stream(Optional.ofNullable(childGoodsCategoryIds)
                        .orElse("0").split(",")).map(Long::parseLong)
                        .collect(Collectors.toSet()));
            }

        });

        Map<Long, String> goodsMap = new HashMap<>();
        Map<Long, String[]> goodsLevelMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(goodsIdList)){
            List<Goods> goodsList = goodsCommonService.queryBatch(goodsIdList);

            final HashMap<Long, String> currentGoodsMap = new HashMap<>();
            goodsList.stream().forEach(row->{
                currentGoodsMap.put(row.getGoodsId(),row.getGoodsName());

                String[] goodsNameLevel=new String[3];
                goodsNameLevel[0]=row.getLevel1Name();
                goodsNameLevel[1]=row.getLevel2Name();
                goodsNameLevel[2]=row.getLevel3Name();

                goodsLevelMap.put(row.getGoodsId(),goodsNameLevel);
            });



            goodsMap.putAll(currentGoodsMap);

        }


        Set<Long> set = goodsMap.keySet();

        List<Long> goodsIdSet = goodsIdList.stream().filter(e -> {
            return set == null || !set.contains(e);
        }).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(goodsIdSet)){
            goodsIdSet.forEach(goodsId -> {
                Goods goods = goodsServiceApi.queryById(goodsId);
                if(goods != null){
                    goodsMap.put(goods.getGoodsId(),goods.getGoodsName());
                }

            });
        }

        Map<Long,String> streetDivisionNameMap = tempStreetDivisionNameMap;

        Map<Long,PushStrategySnapshot> strategySnapshotMap = new HashMap<>();
        List<Long> baseSelectStrategyIdList = new ArrayList<>();
        List<Long> filterStrategyIdList = new ArrayList<>();
        List<Long> sortingStrategyIdList = new ArrayList<>();
        pushOrderListList.forEach(pushOrderList -> {
            if(StringUtils.isNotBlank(pushOrderList.getPushStrategy())){
                PushStrategySnapshot pushStrategySnapshot = JSON.parseObject(pushOrderList.getPushStrategy(),PushStrategySnapshot.class);
                strategySnapshotMap.put(pushOrderList.getId(),pushStrategySnapshot);
                baseSelectStrategyIdList.add(pushStrategySnapshot.getBaseSelectStrategySnapshotId());
                if(pushStrategySnapshot.getFilterStrategySnapshotId() != null){
                    filterStrategyIdList.add(pushStrategySnapshot.getFilterStrategySnapshotId());
                }

                if(pushStrategySnapshot.getSortingStrategySnapshotId() != null){
                    sortingStrategyIdList.add(pushStrategySnapshot.getSortingStrategySnapshotId());
                }
            }

        });



        Map<Long, BaseSelectStrategySnapshot> baseSelectStrategySnapshotMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(baseSelectStrategyIdList)){
            SnapshotRqt snapshotRqt = new SnapshotRqt();
            snapshotRqt.setSnapshotIdList(baseSelectStrategyIdList.stream().distinct().collect(Collectors.toList()));
            List<BaseSelectStrategySnapshot> baseSelectStrategySnapshotList = baseSelectStrategyApi.selectBySnapshotIdList(snapshotRqt);
            baseSelectStrategySnapshotMap.putAll(baseSelectStrategySnapshotList.stream().collect(Collectors.toMap(
                    BaseSelectStrategySnapshot::getSnapshotId,
                    obj -> obj,
                    (key1 , key2) -> key1
            )));
        }

        Map<Long, FilterStrategySnapshot> filterStrategySnapshotMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(filterStrategyIdList)){
            SnapshotRqt snapshotRqt = new SnapshotRqt();
            snapshotRqt.setSnapshotIdList(filterStrategyIdList.stream().distinct().collect(Collectors.toList()));
            List<FilterStrategySnapshot> filterStrategySnapshotList = filterStrategyApi.selectBySnapshotIdList(snapshotRqt);
            filterStrategySnapshotMap.putAll(filterStrategySnapshotList.stream().collect(Collectors.toMap(
                    FilterStrategySnapshot::getSnapshotId,
                    obj -> obj,
                    (key1 , key2) -> key1
            )));
        }


        Map<Long, SortingStrategySnapshot> sortingStrategySnapshotMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(sortingStrategyIdList)){
            SnapshotRqt snapshotRqt = new SnapshotRqt();
            snapshotRqt.setSnapshotIdList(sortingStrategyIdList.stream().distinct().collect(Collectors.toList()));
            List<SortingStrategySnapshot> sortingStrategySnapshotList = sortingStrategyApi.selectBySnapshotIdList(snapshotRqt);
            sortingStrategySnapshotMap.putAll(sortingStrategySnapshotList.stream().collect(Collectors.toMap(
                    SortingStrategySnapshot::getSnapshotId,
                    obj -> obj,
                    (key1 , key2) -> key1
            )));

        }



        List<NoPushedMasterOrderListResp> respList = new ArrayList<>();
        pushOrderListList.forEach(pushOrderList -> {
            try{
                NoPushedMasterOrderListResp resp = new NoPushedMasterOrderListResp();
                resp.setPushId(pushOrderList.getId());
                resp.setPushTime(pushOrderList.getPushTime());
                resp.setBusinessLine(businessLineMap.get(pushOrderList.getBusinessLineId()));
                resp.setOrderNo(String.valueOf(pushOrderList.getOrderNo()));
                resp.setCity(cityDivisionNameMap.get(pushOrderList.getSecondDivisionId()));
                resp.setDistrict(districtDivisionNameMap.get(pushOrderList.getThirdDivisionId()));
                resp.setStreet((pushOrderList.getFourthDivisionId() != null && pushOrderList.getFourthDivisionId() > 0 ) ? streetDivisionNameMap.get(pushOrderList.getFourthDivisionId()) : "");
                resp.setServeCategory(getGoodsName(pushOrderList.getChildGoodsCategoryIds(),goodsMap));
                setLevelGoodsName(resp,pushOrderList.getChildGoodsCategoryIds(),goodsLevelMap);
                resp.setServeType(ServeTypeEnum.getDesc(pushOrderList.getServeTypeId()));
                resp.setOrderFrom(OrderFromTypeEnum.asValue(pushOrderList.getOrderFromType()).name);
                resp.setAppointType(AppointTypeEnum.asValue(pushOrderList.getAppointType()).name);
                resp.setPushStrategy(getPushStrategy(pushOrderList,strategySnapshotMap,baseSelectStrategySnapshotMap,filterStrategySnapshotMap,sortingStrategySnapshotMap));
                resp.setBaseSelectMasterNum(pushOrderList.getBaseSelectMasterNum());
                resp.setMasterNumAfterFilter(pushOrderList.getMasterNumAfterFilter());
                respList.add(resp);
            }catch(Exception e){
                e.printStackTrace();
            }

        });

        return respList;
    }


    private String getPushStrategy(PushOrderList pushOrderList,Map<Long,PushStrategySnapshot> strategySnapshotMap,Map<Long, BaseSelectStrategySnapshot> baseSelectStrategySnapshotMap,
                                   Map<Long, FilterStrategySnapshot> filterStrategySnapshotMap,Map<Long, SortingStrategySnapshot> sortingStrategySnapshotMap){

        if(StringUtils.isBlank(pushOrderList.getPushStrategy())){
            return "";
        }

        String pushStrategy;
        String baseSelectStrategyName = baseSelectStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getBaseSelectStrategySnapshotId()).getStrategyName();
        pushStrategy = baseSelectStrategyName;
        if(strategySnapshotMap.get(pushOrderList.getId()).getFilterStrategySnapshotId() != null){
            String filterStrategyName = filterStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getFilterStrategySnapshotId()).getStrategyName();
            pushStrategy = pushStrategy + "-" + filterStrategyName;
        }

        if(strategySnapshotMap.get(pushOrderList.getId()).getSortingStrategySnapshotId() != null){
            String sortingStrategyName = sortingStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getSortingStrategySnapshotId()).getStrategyName();
            pushStrategy = pushStrategy + "-" + sortingStrategyName;

        }
        return pushStrategy;
    }


    private void setLevelGoodsName(
            NoPushedMasterOrderListResp resp,
            String goodsIds,
            Map<Long,String[]> goodsLevelMap){
        if(StringUtils.isBlank(goodsIds)){
            return;
        }
        Set<Long> goodsIdSet = Arrays.stream(Optional.ofNullable(goodsIds)
                        .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        StringJoiner level1GoodsName=new StringJoiner(",");
        StringJoiner level2GoodsName=new StringJoiner(",");
        StringJoiner level3GoodsName=new StringJoiner(",");
        goodsIdSet.forEach(goodsId -> {
            final String[] goodsLevel = goodsLevelMap.get(goodsId);
            if (StringUtils.isNotEmpty(goodsLevel[0])) {
                level1GoodsName.add(goodsLevel[0]);
            }
            if (StringUtils.isNotEmpty(goodsLevel[1])) {
                level2GoodsName.add(goodsLevel[1]);
            }
            if (StringUtils.isNotEmpty(goodsLevel[2])) {
                level3GoodsName.add(goodsLevel[2]);
            }
        });
        resp.setLevel1NameServeCategory(level1GoodsName.toString());
        resp.setLevel2NameServeCategory(level2GoodsName.toString());
        resp.setLevel3NameServeCategory(level3GoodsName.toString());
    }

    private String getGoodsName(String goodsIds,Map<Long,String> goodsMap){
        if(StringUtils.isBlank(goodsIds)){
            return "";
        }
        Set<Long> goodsIdSet = Arrays.stream(Optional.ofNullable(goodsIds)
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());
        List<String> goodsNameList = new ArrayList<>();
        goodsIdSet.forEach(goodsId -> {
            goodsNameList.add(goodsMap.get(goodsId));
        });
        return StringUtils.join(goodsNameList,",");

    }



    @Override
    public Integer exportNoPushMasterOrderList(NoPushedMasterOrderListRqt rqt, HttpServletResponse httpServletResponse) {

        //  TODO: 2020/9/23 暂不支持大数据量同步导出，需要做异步导出！
        //由于之前做的同步导出使用的poi包是11年前的，现在做异步导出easyExcel的包也需要依赖poi包，且需要高版本的包，高版本的poi包对低版本是不兼容的，导致现在的运力不足明细的导出有问题，
        throw new BusException("暂不支持大数据量同步导出，需要做异步导出！");

        /*try{

            parseParams(rqt);

            List<NoPushedMasterOrderListExcelVo> voList = new ArrayList<>();

            int pageNum = 1;
            int pageSize = rqt.getPageSize();
            rqt.setPageSize(pageSize);

            while(true){
                rqt.setPageNum(pageNum);
                SimplePageInfo<PushOrderList> simplePageInfo = orderPushMonitorApi.noPushedMasterOrderList(rqt);
                if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){
                    List<NoPushedMasterOrderListResp> respList = buildNoPushedMasterOrderListResp(simplePageInfo.getList());
                    respList.forEach(resp -> {
                        NoPushedMasterOrderListExcelVo excelVo = new NoPushedMasterOrderListExcelVo();
                        BeanUtils.copyProperties(resp,excelVo);
                        voList.add(excelVo);
                    });
                    pageNum++;
                }else{
                    break;
                }

                if(simplePageInfo.getPageNum() >= simplePageInfo.getPages()){
                    break;
                }
            }
*/
            /*ExportParams exportParams = new ExportParams();
            exportParams.setTitle("未推单明细");
            exportParams.setSheetName("未推单明细");

            Workbook workbook;
            ExcelExportServer excelExportServer = new ExcelExportServer();
            if (ExcelType.HSSF.equals(exportParams.getType())) {
                workbook = new HSSFWorkbook();
            } else if (voList.size() < 1000) {
                workbook = new XSSFWorkbook();
            } else {
                workbook = new SXSSFWorkbook();
            }
            excelExportServer.createSheet(workbook, exportParams, NoPushedMasterOrderListExcelVo.class, voList);

            String fileName = "未推单明细";
            httpServletResponse.setHeader("Content-Disposition", "attachment;Filename=" + fileName + System.currentTimeMillis() + ".xls");
            ServletOutputStream outputStream = httpServletResponse.getOutputStream();
            workbook.write(outputStream);
            outputStream.close();*/

      /*      return 1;
        }catch(Exception e){
            e.printStackTrace();
        }

        return 0;*/
    }


    @Override
    public List<GetFilterDataResp> filterData(GetFilterDataRqt rqt){
        return orderPushMonitorApi.filterData(rqt);
    }


}
