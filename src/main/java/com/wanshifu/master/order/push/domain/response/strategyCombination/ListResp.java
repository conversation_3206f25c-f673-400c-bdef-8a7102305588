package com.wanshifu.master.order.push.domain.response.strategyCombination;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  组合策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     * 组合id
     */
    private Long combinationId;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 组合名称
     */
    private String combinationName;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 策略状�?
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "combinationStatusStr")
    private Integer combinationStatus;

    /**
     * 策略状态中�?
     */
    private String combinationStatusStr;

    /**
     * 测试状�?0: 待测算，1：测算中�?: 测算完成
     */
    private Integer simulationStatus = 0;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @TranslateEnum(enumClass= OrderFlagEnum.class)
    private String orderFlag;

}
