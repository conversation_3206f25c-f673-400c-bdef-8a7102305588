package com.wanshifu.master.order.push.service.filterStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.api.FilterStrategyApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.filterStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaByCodesRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaValueRqt;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FilterStrategyServiceImpl implements FilterStrategyService {


    private final GoodsCommonService goodsCommonApi;

    private final MasterBigDataOpenApi masterBigDataOpenApi;

//    private final AuthHandler authHandler;

    private final IopAccountApi iopAccountApi;

    private final ServeCommonService serveCommonService;

    private final FilterStrategyApi filterStrategyApi;

    private final CommonApi commonApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(CreateRqt rqt) {
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return filterStrategyApi.create(rqt);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UpdateRqt rqt) {
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return filterStrategyApi.update(rqt);
    }


    @Override
    public DetailResp detail(DetailRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        FilterStrategy filterStrategy = filterStrategyApi.detail(rqt);

        DetailResp detailResp = new DetailResp();
        BeanCopyUtil.copyProperties(filterStrategy, detailResp);

        Integer businessLineId = filterStrategy.getBusinessLineId();
        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 3) {
                businessLineId = 1;
            } else if (businessLineId == 999) {
                businessLineId = 2;
            }
        }

        List<DetailResp.RuleItem> ruleItems = JSON.parseArray(filterStrategy.getFilterRule(), DetailResp.RuleItem.class);
        //配置中的指标code
        List<String> quotaCodes = ruleItems.stream().map(DetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).map(DetailResp.FilterRuleItem::getItemName).distinct().collect(Collectors.toList());

        //配置中的师傅人群id
        List<Long> masterGroupIds = ruleItems.stream().map(DetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());
        Map<Long, String> groupNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(masterGroupIds)) {
            groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
        }

        GetMasterQuotaByCodesRqt getMasterQuotaByCodesRqt = new GetMasterQuotaByCodesRqt();
        getMasterQuotaByCodesRqt.setQuotaCodes(quotaCodes);
        getMasterQuotaByCodesRqt.setBusinessLineId(businessLineId);

        List<MasterQuota> masterQuotas = commonApi.getMasterQuotaByCodes(getMasterQuotaByCodesRqt);

        Map<String, MasterQuota> masterQuotaMap = Maps.newHashMap();
        Map<Long, List<MasterQuotaValue>> masterQuotaValueMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(masterQuotas)) {
            masterQuotaMap = masterQuotas.stream().collect(Collectors.toMap(MasterQuota::getQuotaCode, Function.identity()));
            List<Long> masterQuotaIds = masterQuotas.stream().map(MasterQuota::getQuotaId).collect(Collectors.toList());
            GetMasterQuotaValueRqt getMasterQuotaValueRqt = new GetMasterQuotaValueRqt();
            getMasterQuotaValueRqt.setMasterQuotaIdList(masterQuotaIds);
            masterQuotaValueMap = commonApi.getMasterQuotaValue(getMasterQuotaValueRqt).stream().collect(Collectors.groupingBy(MasterQuotaValue::getMasterQuotaId));
        }


        Set<Long> serveIds = ruleItems.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;
        ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

            List<LinkedList<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));

        Map<Long, String> finalGroupNameMap = groupNameMap;
        Map<String, MasterQuota> finalMasterQuotaMap = masterQuotaMap;
        Map<Long, List<MasterQuotaValue>> finalMasterQuotaValueMap = masterQuotaValueMap;
        ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getFilterRule().getItemList()).ifPresent(it -> it.forEach(item -> {
            String quotaCode = item.getItemName();
            String itemTitle = "";
            List<DetailResp.TermItem> termItems = Lists.newArrayList();
            List<DetailResp.ValueItem> valueItems = Lists.newArrayList();

            if (StringUtils.equals(quotaCode, "master_group")) {
                //师傅人群
                itemTitle = "师傅人群";
                termItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包含", "not_in"));
                valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(item.getItemValue()), "师傅人群"), item.getItemValue()));
            } else {
                MasterQuota masterQuota = finalMasterQuotaMap.get(quotaCode);
                if (masterQuota != null) {
                    itemTitle = masterQuota.getQuotaName();
                    if (StringUtils.equals(masterQuota.getValueType(), "enum_value")) {
                        termItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包含", "not_in"));
                        valueItems = finalMasterQuotaValueMap.getOrDefault(masterQuota.getQuotaId(), Collections.emptyList()).stream().map(quotaCodeValue -> new DetailResp.ValueItem(quotaCodeValue.getName(), quotaCodeValue.getCode())).collect(Collectors.toList());
                    } else {
                        termItems = Lists.newArrayList(new DetailResp.TermItem("大于等于", ">="), new DetailResp.TermItem("大于", ">"), new DetailResp.TermItem("等于", "="), new DetailResp.TermItem("小于", "<"), new DetailResp.TermItem("小于等于", "<="));
                    }
                }
            }
            item.setItemTitle(itemTitle);
            item.setTermList(termItems);
            item.setValueList(valueItems);
        })));

        detailResp.setRuleList(ruleItems);
        return detailResp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        String categoryIds = rqt.getCategoryIds();

        SimplePageInfo<FilterStrategy> strategySimplePageInfo = filterStrategyApi.list(rqt);
        List<FilterStrategy> filterStrategyList = strategySimplePageInfo.getList();
        //类目名称
        List<Long> goodsIds = filterStrategyList.stream().map(FilterStrategy::getCategoryIds).flatMap(it -> Arrays.stream(it.split(","))).distinct().filter(it -> !StringUtils.equals("all", it)).map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommonApi.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        List<Long> updateAccountIds = filterStrategyList.stream().map(FilterStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;
        List<ListResp> listResps = BeanCopyUtil.copyListProperties(filterStrategyList, ListResp.class, (s, t) -> {
            //规则条数
            t.setRuleNum(JSON.parseArray(s.getFilterRule()).size());
            if (StringUtils.equals(s.getCategoryIds(), "all")) {
                t.setCategoryName("全部(不限类目)");
            } else {
                t.setCategoryName(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });
        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(strategySimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(strategySimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(strategySimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(strategySimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return filterStrategyApi.enable(rqt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(DeleteRqt rqt) {
        return filterStrategyApi.delete(rqt);
    }
}