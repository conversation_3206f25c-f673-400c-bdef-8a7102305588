package com.wanshifu.master.order.push.service.sortingStrategy;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.response.sortingStrategy.DetailV2Resp;
import com.wanshifu.master.order.push.domain.response.sortingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface SortingStrategyService {


    int enable(EnableRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    int delete(DeleteRqt rqt);

    int createV2(CreateV2Rqt rqt);

    int updateV2(UpdateV2Rqt rqt);

    DetailV2Resp detailV2(DetailRqt rqt);



}