package com.wanshifu.master.order.push.domain.response.pushNoticeCombination;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.notice.domains.dto.AbTestStrategyRelationDto;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 15:28
 */
@Data
public class ListResp {
    /**
     *策略组合id
     */
    private Integer  strategyId;
    /**
     * 策略名称
     */
    private String  strategyName;
    /**
     * 类目名称
     */
    private String  categoryNames;
    /**
     *城市名称
     */
    private String  cityNames;

    /**
     * 策略状态
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer strategyStatus;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否ab实验，1：是，0：否
     */
    private Integer testFlag;

    /**
     * 大数据ab实验编号
     */
    private Integer testId;


    /**
     * 策略关联大数据实验组别信息
     */
    private List<AbTestStrategyRelationDto> strategyRelationList;
}