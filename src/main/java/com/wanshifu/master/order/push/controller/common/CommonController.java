package com.wanshifu.master.order.push.controller.common;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.request.common.GetGroupByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GetServeByServeIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GroupListReq;
import com.wanshifu.master.order.push.domain.request.common.MasterQuotaReq;
import com.wanshifu.master.order.push.domain.request.common.ServeListReq;
import com.wanshifu.master.order.push.domain.request.common.ServeReq;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionListResp;
import com.wanshifu.master.order.push.domain.response.common.*;
import com.wanshifu.master.order.push.service.common.BackendCommonService;
import com.wanshifu.master.order.push.service.permission.PermissionSetService;
import com.wanshifu.master.order.sort.domains.api.response.common.OrderSortItemListResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 描述 :  公共类接口.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:40
 */
@RestController
@RequestMapping("/common")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonController {

    private final BackendCommonService backendCommonService;

    private final PermissionSetService permissionSetService;

    /**
     * 查询用户人群数据
     *
     * @param rqt
     * @return
     */
    @PostMapping("/userGroupList")
    public SimplePageInfo<UserGroupListResp> userGroupList(@RequestBody @Valid GroupListReq rqt) {
        return backendCommonService.userGroupList(rqt);
    }

    /**
     * 查询师傅人群数据
     *
     * @param rqt
     * @return
     */
    @PostMapping("/masterGroupList")
    public SimplePageInfo<UserGroupListResp> masterGroupList(@RequestBody @Valid GroupListReq rqt) {
        return backendCommonService.masterGroupList(rqt);
    }

    /**
     * 查询师傅指标
     *
     * @return
     */
    @PostMapping("/masterQuota")
    public SimplePageInfo<MasterQuotaResp> masterQuota(@RequestBody @Valid MasterQuotaReq rqt) {
        return backendCommonService.masterQuota(rqt);
    }


    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @GetMapping("/masterItemList")
    public List<MasterItemListResp> masterItemList(@RequestParam(value = "itemName", required = false) String itemName) {
        return backendCommonService.masterItemList(itemName);
    }


    /**
     * 查询匹配项-智能排序
     *
     * @return
     */
    @GetMapping("/orderSortItemList")
    public List<OrderSortItemListResp> orderSortItemList(@RequestParam(value = "itemName", required = false) String itemName) {
        return backendCommonService.orderSortItemList(itemName);
    }

    /**
     * 查询类目
     *
     * @return
     */
    @GetMapping("/categoryList")
    public List<CategoryListResp> categoryList(@RequestParam(value = "businessLineId",required = false) Integer businessLineId) {
        return backendCommonService.categoryList(businessLineId);
    }

    /**
     * 查询服务-全部
     *
     * @return
     */
    @PostMapping("/serveList")
    public List<ServeListResp> serveList(@RequestBody @Valid ServeListReq rqt) {
        return backendCommonService.serveList(rqt);
    }

    /**
     * 查询服务-分级
     *
     * @return
     */
    @PostMapping("/serve")
    public List<ServeResp> serveList(@RequestBody @Valid ServeReq rqt) {
        return backendCommonService.serve(rqt);
    }

    /**
     * 批量根据人群id查询人群名称
     *
     * @return
     */
    @PostMapping("/getGroupByGroupIds")
    public List<GetGroupByGroupIdsResp> getGroupByGroupIds(@RequestBody @Valid GetGroupByGroupIdsReq rqt) {
        return backendCommonService.getGroupByGroupIds(rqt);
    }

    /**
     * 批量根据服务id查询服务
     *
     * @return
     */
    @PostMapping("/getServeByServeIds")
    public List<GetServeByServeIdsResp> getServeByServeIds(@RequestBody @Valid GetServeByServeIdsReq rqt) {
        return backendCommonService.getServeByServeIds(rqt);
    }


    /**
     * 查询商品列表
     * @param businessLineId
     * @return
     */
    @GetMapping("/goodsList")
    public List<GetGoodsListResp> goodsList(@RequestParam("businessLineId") Integer businessLineId) {
        return backendCommonService.getGoodsList(businessLineId);
    }


    @GetMapping("/serveTypeList")
    public List<GetServeTypeListResp> serveTypeList(@RequestParam("businessLineId") Long businessLineId,@RequestParam("categoryId") Long categoryId) {
        return backendCommonService.getServeTypeList(businessLineId,categoryId);
    }

    @GetMapping("/batchServeTypeList")
    public List<GetServeTypeListResp> batchServeTypeList(
            @RequestParam("businessLineId") Long businessLineId,
            @RequestParam(value = "categoryIds",required = false) String categoryIds) {
        return backendCommonService.batchGetServeTypeList(businessLineId,categoryIds);
    }


    @PostMapping(value = "permissionList")
    public List<GetPermissionListResp> permissionList() {
        return permissionSetService.permissionList();
    }


}