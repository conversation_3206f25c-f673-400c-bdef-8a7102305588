package com.wanshifu.master.order.push.config;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.wanshifu.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
@Slf4j
public class UserInfoInterceptor implements HandlerInterceptor {

    public static TransmittableThreadLocal<String> accountIdLocal = new TransmittableThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        String accountId = httpServletRequest.getHeader("account-id");

        log.info(">>>>>>>拦截到api相关请求头，请求用户account-id：<<<<<<<<{}", accountId);

        if(StringUtils.isNotEmpty(accountId)){
            // 直接搂下来，放到ThreadLocal中 后续直接从中获取
            accountIdLocal.set(accountId);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {

    }
}
