package com.wanshifu.master.order.push.domain.response.pushShuntRule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2025-03-10 15:28
 */
@Data
public class ListResp {
    /**
     * 规则id
     */
    private Integer  ruleId;
    
    /**
     * 规则名称
     */
    private String  ruleName;
    
    /**
     * 业务线
     */
    private Integer  businessLineId;


    /**
     * 限制城市名称
     */
    private String  cityNames;


    /**
     * 限制人群
     */
    private String crowdValue;


    /**
     * 规则状态
     */
    private Integer ruleStatus;


    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}