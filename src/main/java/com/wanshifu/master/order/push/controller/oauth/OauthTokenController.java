//package com.wanshifu.master.order.push.controller.oauth;
//
//import cn.hutool.core.util.StrUtil;
//import com.wanshifu.framework.core.BusException;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.api.iop.IopAccountApi;
//import com.wanshifu.master.order.push.domain.dto.UserInfoDto;
//import com.wanshifu.master.order.push.domain.request.common.IopAccountLoginReq;
//import com.wanshifu.master.order.push.domain.request.common.IopAccountLogoutReq;
//import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdReq;
//import com.wanshifu.master.order.push.domain.response.common.GetUserInfoResp;
//import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
////import com.wanshifu.master.order.push.service.common.AuthHandler;
//import com.wanshifu.master.order.push.util.ThreadLocalUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.userdetails.User;
//import org.springframework.security.oauth2.common.OAuth2AccessToken;
//import org.springframework.security.oauth2.common.OAuth2RefreshToken;
//import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.security.Principal;
//import java.util.ArrayList;
//import java.util.Map;
//import java.util.Optional;
//
///**
// * 描述 :  自定义token请求和统一返回.
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-17 16:42
// */
//@Slf4j
//@RestController
//@RequestMapping("/oauth")
//public class OauthTokenController {
//
//    //令牌请求的端�?
//    @Autowired
//    private TokenEndpoint tokenEndpoint;
//    @Autowired
//    private IopAccountApi iopAccountApi;
//    @Autowired
//    private AuthHandler authHandler;
//    @Autowired
//    private TokenStore tokenStore;
//
//    @Value("${spring.oauth.client_id:push_backend}")
//    private String clientId;
//
//    @Value("${spring.oauth.client_secret:123456}")
//    private String clientSecret;
//
//    /**
//     * 重写/oauth/token这个默认接口，返回的数据格式统一
//     */
//    @PostMapping(value = "/token")
//    public OAuth2AccessToken postAccessToken(Principal principal, @RequestParam Map<String, String> parameters) {
//
//        String username = parameters.get("username");
//        String password = parameters.get("password");
//        IopAccountResp<IopAccountResp.IopAccount> accountResp;
//        try {
//            //iop认证通过则认为登录成�?
//            accountResp = iopAccountApi.login(new IopAccountLoginReq(username, password));
//            String retCode = Optional.ofNullable(accountResp).map(IopAccountResp::getRetCode).orElse(null);
//            if (!StringUtils.equals(retCode, "200")) {
//                log.error("账号密码错误!");
//                throw new BusException("账号密码错误!");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new BusException("账号密码错误!");
//        }
//        //oauth认证时使用提交的密码
//        ThreadLocalUtil.set(StrUtil.format("iop_account_login_username_{}", username), new UserInfoDto(accountResp.getRetData().getAccountId(), password));
//        User clientUser = new User(clientId, clientSecret, new ArrayList<>());
//
//        //生成已经认证的client
//        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(clientUser, null, new ArrayList<>());
//        try {
//            return tokenEndpoint.postAccessToken(token, parameters).getBody();
//        } catch (Exception e) {
//            log.error("账号密码错误!");
//            throw new BusException("账号密码错误!");
//        }
//    }
//
//    /**
//     * 退出登�?
//     *
//     * @param authHeader Authorization
//     */
//    @DeleteMapping("/logout")
//    public Integer logout(@RequestHeader(value = HttpHeaders.AUTHORIZATION) String authHeader) {
//
//        try {
//            //iop退出登�?
//            iopAccountApi.logout(new IopAccountLogoutReq(authHandler.getLoginUserName()));
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("iop退出登录异�?");
//        }
//        if (StrUtil.isBlank(authHeader)) {
//            return 0;
//        }
//        String tokenValue = authHeader.replace(OAuth2AccessToken.BEARER_TYPE, "").trim();
//
//        OAuth2AccessToken accessToken = tokenStore.readAccessToken(tokenValue);
//        if (accessToken == null || StrUtil.isBlank(accessToken.getValue())) {
//            return 1;
//        }
//        // OAuth2Authentication auth2Authentication = tokenStore.readAuthentication(accessToken);
//        // 清空用户信息 清空access token
//        tokenStore.removeAccessToken(accessToken);
//        // 清空 refresh token
//        OAuth2RefreshToken refreshToken = accessToken.getRefreshToken();
//        tokenStore.removeRefreshToken(refreshToken);
//        return 1;
//    }
//
//    /**
//     * 获取用户信息
//     *
//     * @param authHeader Authorization
//     */
//    @GetMapping("/getUserInfo")
//    public GetUserInfoResp getUserInfo(@RequestHeader(value = HttpHeaders.AUTHORIZATION) String authHeader) {
//
//        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
//        if (loginUserId == null) {
//            return null;
//        }
//        IopAccountResp<IopAccountResp.IopAccount> userInfoByAccountId = iopAccountApi.getUserInfoByAccountId(new IopGetInfoListByAccountIdReq(loginUserId));
//        if (userInfoByAccountId != null && userInfoByAccountId.getRetData() != null) {
//            IopAccountResp.IopAccount iopAccount = userInfoByAccountId.getRetData();
//            return new GetUserInfoResp(loginUserId, iopAccount.getUsername(), iopAccount.getChineseName(), iopAccount.getPhone(), iopAccount.getEmail());
//        }
//        return null;
//    }
//}
