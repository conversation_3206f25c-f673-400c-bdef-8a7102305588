package com.wanshifu.master.order.push.service.orderMatchRoute;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;

public interface OrderMatchRouteService {

    Integer create(CreateOrderMatchRouteRqt rqt);


    Integer update(UpdateOrderMatchRouteRqt rqt);


    OrderMatchRouteDetailResp detail(OrderMatchRouteDetailRqt rqt);

    SimplePageInfo<OrderMatchRouteListResp> list(OrderMatchRouteListRqt rqt);



}
