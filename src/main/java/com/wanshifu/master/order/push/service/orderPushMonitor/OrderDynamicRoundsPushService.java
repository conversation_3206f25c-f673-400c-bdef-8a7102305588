package com.wanshifu.master.order.push.service.orderPushMonitor;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.response.orderPushMonitory.OrderDynamicRoundsPushListResp;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface OrderDynamicRoundsPushService {

    SimplePageInfo<OrderDynamicRoundsPushListResp> list(ListRqt rqt);


    Integer exportMasterLackList(ListRqt rqt, HttpServletResponse httpServletResponse);

}
