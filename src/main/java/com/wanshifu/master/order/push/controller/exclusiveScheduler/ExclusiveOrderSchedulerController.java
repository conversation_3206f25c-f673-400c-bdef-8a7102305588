package com.wanshifu.master.order.push.controller.exclusiveScheduler;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.exclusiveScheduler.ListResp;
import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;
import com.wanshifu.master.order.push.service.baseSelectStrategy.BaseSelectStrategyService;
import com.wanshifu.master.order.push.service.exclusiveScheduler.ExclusiveOrderSchedulerService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/exclusiveOrderScheduler")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExclusiveOrderSchedulerController {

    private final ExclusiveOrderSchedulerService exclusiveOrderSchedulerService;


    /**
     * 初筛策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return exclusiveOrderSchedulerService.list(rqt);
    }

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return exclusiveOrderSchedulerService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return exclusiveOrderSchedulerService.update(rqt);
    }

}
