package com.wanshifu.master.order.push.service.orderpush;

import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushPushNumberRqt;
import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushRecordDetailRqt;
import com.wanshifu.master.order.push.domain.vo.orderpush.InfoOrderPushRecordDetailVo;
import com.wanshifu.master.order.push.domain.vo.orderpush.OrderPushRecordDetailVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18 11:07
 */
public interface OrderPushService {

    /**
     * 推单记录明细
     * @param rqt
     * @return
     */
    List<OrderPushRecordDetailVo> listOrderPushRecordDetail(OrderPushRecordDetailRqt rqt);

    /**
     * 推单记录明细
     * @param rqt
     * @return
     */
    List<InfoOrderPushRecordDetailVo> listInfoOrderPushRecordDetail(OrderPushRecordDetailRqt rqt);

    /**
     * ocs查询师傅推单数量
     * @param rqt
     * @return
     */
    int getOrderPushPushNumber(OrderPushPushNumberRqt rqt);

}
