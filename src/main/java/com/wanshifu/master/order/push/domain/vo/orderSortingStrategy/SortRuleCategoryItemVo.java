package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SortRuleCategoryItemVo {
    /**
     * 整体权重 (0,1]
     */
    @DecimalMin(inclusive = false, value = "0")
    @DecimalMax(value = "1")
    @NotNull
    private BigDecimal weight;

    /**
     * 排序规则
     */
    @NotEmpty
    @Valid
    private List<RuleItem> itemList;
}