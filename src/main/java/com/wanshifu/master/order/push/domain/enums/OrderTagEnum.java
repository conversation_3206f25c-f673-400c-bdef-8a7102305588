package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 指派类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum OrderTagEnum {

    /**
     * 直接雇佣
     */
    NONE("none","无特定标�?),

    /**
     * 新师傅订�?
     */
    NEW_MASTER_SUPPORT("new_master_support","新师傅订�?),

    /**
     * 样板城市订单
     */
    NEW_MODEL("new_model","样板城市订单"),


    ENTERPRISE_APPOINT("enterprise_appoint","总包直接指派");




    public final String code;

    public final String name;

    OrderTagEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final Map<String, OrderTagEnum> valueMapping = new HashMap<>((int) (OrderTagEnum.values().length / 0.75));

    static {
        for (OrderTagEnum instance : OrderTagEnum.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static OrderTagEnum asValue(String value) {
        return valueMapping.get(value);
    }

    public static Boolean isInclude(Integer value) {
        if(Objects.isNull(value)) {
            return false;
        }
        for (OrderTagEnum appointType : valueMapping.values()) {
            if(appointType.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

}
