//package com.wanshifu.master.order.push.domain.request.orderSelectStrategy;
//
//import lombok.Data;
//
//import jakarta.validation.constraints.NotNull;
//import java.util.Date;
//
//@Data
//public class GetOrderSelectStrategyListRqt {
//
//    @NotNull
//    private Integer businessLineId;
//
//    private String strategyName;
//
//    private Date createTimeStart;
//
//    private Date createTimeEnd;
//
//    private Integer pageNum = 1;
//
//    private Integer pageSize = 20;
//
//}
