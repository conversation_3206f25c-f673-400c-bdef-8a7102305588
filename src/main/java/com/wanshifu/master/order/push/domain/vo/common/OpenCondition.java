package com.wanshifu.master.order.push.domain.vo.common;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 描述 :  共用的开启条件.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-28 16:00
 */
@Data
public class OpenCondition {
    /**
     * 或且关系
     */
    @NotEmpty
    @ValueIn("and,or")
    private String condition;

    /**
     *规则项
     */
    @NotEmpty
    @Valid
    private List<OpenConditionItem> itemList;


    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,time_liness_tag,appoint_user")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }
}