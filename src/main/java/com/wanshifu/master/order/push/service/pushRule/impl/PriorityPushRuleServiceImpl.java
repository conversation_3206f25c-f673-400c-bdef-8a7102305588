package com.wanshifu.master.order.push.service.pushRule.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.PriorityPushRuleApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.AppointTypeEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFromTypeEnum;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.OrderSelectStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.priorityPushRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.priorityPushRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaByCodesRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaValueRqt;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.*;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroupsRuleItem;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.pushRule.PriorityPushRuleService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
public class PriorityPushRuleServiceImpl implements PriorityPushRuleService {

    @Resource
    private PriorityPushRuleApi priorityPushRuleApi;

    @Resource
    private IopAccountApi iopAccountApi;


    @Resource
    private AddressCommonService addressCommon;


    @Resource
    private GoodsCommonService goodsCommon;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;


    @Resource
    private ServeCommonService serveCommonService;

    @Override
    public int create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return priorityPushRuleApi.create(rqt);
    }

    @Override
    public int update(UpdateRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return priorityPushRuleApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt){
        PriorityPushRule priorityPushRule = priorityPushRuleApi.detail(rqt);
        if(Objects.nonNull(priorityPushRule)){
            DetailResp detailResp = new DetailResp();
            BeanUtils.copyProperties(priorityPushRule,detailResp,"pushGroups","pushRule");
            detailResp.setRuleList(JSON.parseArray(priorityPushRule.getPushRule(), DetailResp.PushGroupsRuleItem.class));



            Set<Long> serveIds = detailResp.getRuleList().stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                    .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                    .flatMap(it -> it.getServeIdList().stream())
                    .collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

            Map<Long, String> serveInfoMap =Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(serveIds)){
                serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                        .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
            }
            Map<Long, String> finalServeInfoMap = serveInfoMap;
            detailResp.getRuleList().forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

                List<LinkedList<Long>> serveIdListList = item.getServeIdList();
                if(CollectionUtils.isNotEmpty(serveIdListList)){
                    List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                    item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
            })));

            //配置中的师傅人群id
            List<List<DetailResp.RuleItem>> ruleItemListList = detailResp.getRuleList().stream().map(DetailResp.PushGroupsRuleItem::getPushGroups).map(DetailResp.PushGroups::getItemList).collect(Collectors.toList());
            List<Long> masterGroupIds = new ArrayList<>();
            ruleItemListList.forEach(ruleItemList -> masterGroupIds.addAll(ruleItemList.stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList())));

            Map<Long, String> groupNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }

            Map<Long,String> finalGroupNameMap = groupNameMap;

            detailResp.getRuleList().forEach(pushGroupsRuleItem -> {
                pushGroupsRuleItem.getPushGroups().getItemList().forEach(item -> {
                    String quotaCode = item.getItemName();
                    String itemTitle = "";
                    List<DetailResp.TermItem> termItems = Lists.newArrayList();
                    List<DetailResp.ValueItem> valueItems = Lists.newArrayList();

                    if (StringUtils.equals(quotaCode, "master_group")) {
                        //师傅人群
                        itemTitle = "师傅人群";
                        termItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包含", "not_in"));
                        valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(item.getItemValue()), "师傅人群"), item.getItemValue()));
                    }
//                else {
//                    MasterQuota masterQuota = masterQuotaMap.get(quotaCode);
//                    if (masterQuota != null) {
//                        itemTitle = masterQuota.getQuotaName();
//                        if (StringUtils.equals(masterQuota.getValueType(), "enum_value")) {
//                            termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("包含", "in"), new OrderSelectStrategyDetailResp.TermItem("不包含", "not_in"));
//                            valueItems = masterQuotaValueMap.getOrDefault(masterQuota.getQuotaId(), Collections.emptyList()).stream().map(quotaCodeValue -> new OrderSelectStrategyDetailResp.ValueItem(quotaCodeValue.getName(), quotaCodeValue.getCode())).collect(Collectors.toList());
//                        } else {
//                            termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("大于等于", ">="), new OrderSelectStrategyDetailResp.TermItem("大于", ">"), new OrderSelectStrategyDetailResp.TermItem("等于", "="), new OrderSelectStrategyDetailResp.TermItem("小于", "<"), new OrderSelectStrategyDetailResp.TermItem("小于等于", "<="));
//                        }
//                    }
//                }
                    item.setItemTitle(itemTitle);
                    item.setTermList(termItems);
                    item.setValueList(valueItems);
                });
            });


            return detailResp;
        }
        return null;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){
        SimplePageInfo<PriorityPushRule> simplePageInfo = priorityPushRuleApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PriorityPushRule> priorityPushRuleList = simplePageInfo.getList();


        List<Long> updateAccountIds = priorityPushRuleList.stream().map(PriorityPushRule::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        String cityIdsStr = priorityPushRuleList.stream().map(PriorityPushRule::getCityIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .collect(Collectors.joining(","));

        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(cityIdsStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));



        //类目名称
        List<Long> goodsIds = priorityPushRuleList.stream().map(PriorityPushRule::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));



        final Map<Integer,List<Long>> pushGroupMap = new HashMap<>();
        Set<Long> masterGroupIds = new HashSet<>();
         priorityPushRuleList.stream().forEach(priorityPushRule -> {

             if(StringUtils.isBlank(priorityPushRule.getPushRule())){
                 return ;
             }
             //配置中的师傅人群id
             List<List<DetailResp.RuleItem>> ruleItemListList = JSON.parseArray(priorityPushRule.getPushRule(), DetailResp.PushGroupsRuleItem.class).stream().map(DetailResp.PushGroupsRuleItem::getPushGroups).map(DetailResp.PushGroups::getItemList).collect(Collectors.toList());
             List<Long> masterGroupIdList = new ArrayList<>();
             ruleItemListList.forEach(ruleItemList -> {
                 masterGroupIdList.addAll(ruleItemList.stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList()));
             });

             masterGroupIds.addAll(masterGroupIdList);
             pushGroupMap.put(priorityPushRule.getRuleId(),masterGroupIdList);
         });



        Map<Long, String> groupNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(masterGroupIds)) {
            groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(new ArrayList<>(masterGroupIds)))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
        }

        Map<Long,String> finalGroupNameMap = groupNameMap;


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(priorityPushRuleList, ListResp.class, (s, t) -> {



            //省份
            String cityIds = s.getCityIds();
            if (StringUtils.equals(cityIds, "all")) {
                t.setCityNames("全国");
            } else {
                List<Address> addresses = Arrays.stream(s.getCityIds().split(",")).map(it -> addressMap.get(Long.parseLong(it))).filter(Objects::nonNull).collect(Collectors.toList());
                t.setCityNames(addresses.stream().map(Address::getDivisionName).distinct().collect(Collectors.joining(",")));
            }
            if (StringUtils.equals(s.getCategoryIds(), "all")) {
                t.setCategoryNames("全部(不限类目)");
            } else {
                t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }

            if(StringUtils.isNotBlank(s.getPushRule())){
                t.setPushGroups(pushGroupMap.get(s.getRuleId()).stream().map(it -> finalGroupNameMap.getOrDefault(it, String.valueOf(it))).collect(Collectors.joining(",")));
            }


            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

    @Override
    public Integer enable(EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return priorityPushRuleApi.enable(rqt);
    }


    @Override
    public Integer delete(DeleteRqt rqt){
        return priorityPushRuleApi.delete(rqt);
    }





}