package com.wanshifu.master.order.push.domain.response.longTailStrategy;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.FilterRuleItemVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.PushRuleVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.RangeSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.StatusSelectVo;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailRule;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.Trigger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  长尾策略详情Resp.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class DetailResp {

    @NotEmpty
    private String longTailStrategyName;
    @NotEmpty
    private String longTailStrategyDesc;
    private String pushType;
    @NotNull
    private LongTailRule longTailRule;
    @NotNull
    private Integer businessLineId;
    private Long createAccountId;


    @Data
    public static class ItemVo {

        private String itemName;

        private String term;

        private String itemValue;

        private String itemType;

        private String itemTitle;

        private List<TermItem> termList;

        private List<ValueItem> valueList;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }


    @Data
    public static class LongTailRule {

        /**
         * 触发规则
         */
        @NotEmpty
        private Trigger trigger;

        /**
         * 范围初筛
         */
        @Valid
        @NotNull
        private RangeSelectVo rangeSelect;

        /**
         * 状态初筛
         */
        @Valid
        private StatusSelectVo statusSelect;

        /**
         * 过滤规则
         */
        private List<DetailResp.RuleItem> ruleList;

        /**
         * 推送规则
         */
        @Valid
        @NotNull
        private PushRuleVo pushRule;

        @Valid
        @NotNull
        private AppointGroup appointGroup;


        @Data
        public static class AppointGroup{

            @NotNull
            private Integer isAppointGroup;

            @NotNull
            private String condition;


            @Valid
            private List<RuleItem> itemList;

        }


        @Data
        public static class RuleItem{

            private String itemName;

            private String term;

            private String itemValue;

            private String itemType;

            private String itemTitle;

            private List<TermItem> termList;

            private List<ValueItem> valueList;
        }
    }

    @Data
    public static class RuleItem {
        private String ruleName;
        private OpenCondition openCondition;
        private FilterRule filterRule;
    }

    @Data
    public static class FilterRule {
        private String condition;
        private List<FilterRuleItem> itemList;
    }
    @Data
    public static class OpenCondition {
        private String condition;
        private List<OpenConditionItem> itemList;
    }

    @Data
    public static class FilterRuleItem {
        private String itemType;
        private String itemName;
        private String term;
        private String itemValue;

        private String itemTitle;
        private List<TermItem> termList;
        private List<ValueItem> valueList;

    }

    @Data
    public static class OpenConditionItem {
        private String itemName;
        private String term;
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;
    }

}