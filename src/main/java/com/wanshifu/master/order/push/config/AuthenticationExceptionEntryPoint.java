package com.wanshifu.master.order.push.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanshifu.master.order.push.domain.vo.common.R;
import com.wanshifu.master.order.push.domain.vo.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.authentication.InsufficientAuthenticationException;
//import org.springframework.security.core.AuthenticationException;
//import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
//import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 描述 :   用来解决匿名用户访问无权限资源时的异�?
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-18 14:17
 */

//@Component
//@Slf4j
//public class AuthenticationExceptionEntryPoint implements AuthenticationEntryPoint {
//
//    @Resource
//    private ObjectMapper objectMapper;
//
//    @Override
//    public void commence(HttpServletRequest request,
//                         HttpServletResponse response, AuthenticationException e)
//            throws IOException {
//        log.error("MooseAuthenticationEntryPoint :: {} {} ", e.getMessage(), request.getRequestURL());
//        response.setContentType("application/json;charset=UTF-8");
//        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//        ServletOutputStream writer = response.getOutputStream();
//        String message = e.getMessage();
//        Integer code = HttpServletResponse.SC_UNAUTHORIZED;
//        if (e instanceof InsufficientAuthenticationException) {
//            message = ResultCode.NOT_LOGIN.getMsg();
//
//            Throwable cause = e.getCause();
//            if (cause instanceof InvalidTokenException) {
//                message = ResultCode.TOKEN_INVALID.getMsg();
//                code = ResultCode.TOKEN_INVALID.getCode();
//            }
//        }
//        objectMapper.writeValue(writer, R.fail(code, message));
//    }
//}
