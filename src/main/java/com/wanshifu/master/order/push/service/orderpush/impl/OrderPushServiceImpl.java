package com.wanshifu.master.order.push.service.orderpush.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanshifu.master.order.push.api.address.CommonAddressService;
import com.wanshifu.master.order.push.domain.po.OrderPush;
import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushPushNumberRqt;
import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushRecordDetailRqt;
import com.wanshifu.master.order.push.domain.vo.orderpush.InfoOrderPushRecordDetailVo;
import com.wanshifu.master.order.push.domain.vo.orderpush.OrderPushRecordDetailVo;
import com.wanshifu.master.order.push.repository.OrderPushRepository;
import com.wanshifu.master.order.push.service.orderpush.OrderPushService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18 11:07
 */
@Service
public class OrderPushServiceImpl implements OrderPushService {

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private OrderPushRepository orderPushRepository;


    @Override
    public List<OrderPushRecordDetailVo> listOrderPushRecordDetail(OrderPushRecordDetailRqt rqt) {
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getThirdDivisionId(), "listOrderPushRecordDetail", JSONUtil.toJsonStr(rqt));

        List<OrderPush> orderPushList = orderPushRepository.selectByOrderId(provinceNextIds, rqt.getMasterOrderId());
        if (CollectionUtil.isEmpty(orderPushList)) {
            return null;
        }
        List<OrderPushRecordDetailVo> recordDetailVoList = Lists.newArrayList();
        orderPushList.forEach(orderPush -> {
            OrderPushRecordDetailVo vo = new OrderPushRecordDetailVo();
            vo.setOrderId(orderPush.getOrderId());
            vo.setPushDistance(orderPush.getPushDistance());
            vo.setPushTime(orderPush.getPushTime());
            vo.setMasterId(orderPush.getMasterId());
            vo.setAccordingTechnologyPushFlag(orderPush.getAccordingTechnologyPushFlag());
            vo.setFirstViewTime(orderPush.getFirstViewTime());
            vo.setOfferTime(orderPush.getOfferTime());
            recordDetailVoList.add(vo);
        });

        return recordDetailVoList;
    }

    @Override
    public List<InfoOrderPushRecordDetailVo> listInfoOrderPushRecordDetail(OrderPushRecordDetailRqt rqt) {
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getThirdDivisionId(), "listInfoOrderPushRecordDetail", JSONUtil.toJsonStr(rqt));

        List<OrderPush> orderPushList = orderPushRepository.selectByOrderIdV2(provinceNextIds, rqt.getMasterOrderId());
        if (CollectionUtil.isEmpty(orderPushList)) {
            return null;
        }
        List<InfoOrderPushRecordDetailVo> recordDetailVoList = Lists.newArrayList();
        orderPushList.forEach(orderPush -> {
            InfoOrderPushRecordDetailVo vo = new InfoOrderPushRecordDetailVo();
            vo.setPushTime(orderPush.getPushTime());
            vo.setMasterId(orderPush.getMasterId());
            recordDetailVoList.add(vo);
        });

        return recordDetailVoList;
    }

    @Override
    public int getOrderPushPushNumber(OrderPushPushNumberRqt rqt) {

        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getMasterDivisionId(), "getOrderPushPushNumber", JSONUtil.toJsonStr(rqt));

        return orderPushRepository.selectPushNumber(provinceNextIds, rqt.getMasterId());
    }
}
