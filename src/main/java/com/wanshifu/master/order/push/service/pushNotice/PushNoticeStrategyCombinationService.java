package com.wanshifu.master.order.push.service.pushNotice;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.*;
import com.wanshifu.master.order.push.domain.request.pushnotice.AbTestGroupInfoRqt;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.AbTestGroupInfoResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.ListResp;
import java.util.List;

public interface PushNoticeStrategyCombinationService {


    Integer create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    Integer enable(EnableRqt rqt);

    Integer delete(DeleteRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    List<AbTestGroupInfoResp> listAbTestGroupInfoByTestId(AbTestGroupInfoRqt rqt);

}
