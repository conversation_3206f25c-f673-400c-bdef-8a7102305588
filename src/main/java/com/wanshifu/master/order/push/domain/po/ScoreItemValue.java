package com.wanshifu.master.order.push.domain.po;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 
 */
@Data
@ToString
@Table(name = "score_item_value")
public class ScoreItemValue {

    /**
     * 匹配项枚举值id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "value_id")
    private Long valueId;

    /**
     * 匹配项id
     */
    @Column(name = "score_item_id")
    private Long scoreItemId;

    /**
     * 匹配项枚举值编�?
     */
    @Column(name = "code")
    private String code;

    /**
     * 匹配项枚举值名�?
     */
    @Column(name = "name")
    private String name;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
