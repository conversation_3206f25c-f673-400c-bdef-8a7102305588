package com.wanshifu.master.order.push.domain.response.common;

import lombok.Data;

import java.util.List;

/**
 * 描述 :  大数据人群列表.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 15:29
 */
@Data
public class BigdataGetAllGroupListForPageResp<T> {

    private List<T> data;
    private Integer errCode;
    private String errMessage;
    private Boolean error;
    private Boolean isError;
    private Integer total;

    @Data
    public static class GroupInfo {
        private String appIdName;
        private String createUser;
        private Long groupId;
        private String groupName;
        private Long groupNum;
    }
}