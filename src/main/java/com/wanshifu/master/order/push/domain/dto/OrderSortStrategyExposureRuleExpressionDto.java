package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/27 11:30
 */
@Data
public class OrderSortStrategyExposureRuleExpressionDto {

    /**
     * 干预规则表达式list
     */
    private List<ScoreRuleItemExpression> exposureRuleItemExpressionList;

    @Data
    public static class ScoreRuleItemExpression {
        private String ruleName;
        private String ruleCode;
        private String openConditionRuleExpression;
        private String openConditionRuleParams;
        private String scoreRuleExpression;
        private String scoreRuleParams;
    }
}
