package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 描述 :  订单排序规则Dto.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-15 15:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSortingStrategyRuleDto {
    private List<RuleItem> itemList;
    private List<RuleItem> specialItemList;
}
