package com.wanshifu.master.order.push.domain.response.agentDistributeStrategy;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/6 9:57
 */
@Data
public class GetTobGroupAgentListResp {

    /**
     * B端团队代理商 id（团队师傅队长masterId�?
     */
    private Long agentId;

    /**
     * B端团队代理商代理商名�?
     * 师傅名称拼接（B端团队师傅）
     */
    private String agentName;

    /**
     * 团队师傅队长masterId
     */
    private Long groupMasterId;

    /**
     * 团队师傅队长姓名
     */
    private String groupMasterName;

    /**
     * 团队师傅队长手机号，登录账号
     */
    private String phone;

    /**
     * 省id
     */
    private Long provinceDivisionId;

    /**
     * 省名�?
     */
    private String province;

    /**
     * 市id
     */
    private Long cityDivisionId;

    /**
     * 市名�?
     */
    private String city;

    /**
     * 区域名称，以逗号隔开
     */
    private String divisionNames;

    private Integer agentStatus;


    private List<GetTobGroupAgentListResp.AgentDivision> divisionList;


    @Data
    public static class AgentDivision{

        private Long thirdDivisionId;

        private String thirdDivisionName;

        private List<GetTobGroupAgentListResp.AgentFourthDivision> fourthDivisionList;

    }

    @Data
    public static class AgentFourthDivision{

        private Long fourthDivisionId;

        private String fourthDivisionName;

        private boolean isSelected;

    }
}
