package com.wanshifu.master.order.push.util;


import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.config.UserInfoInterceptor;

/**
 * 通用工具类
 * <AUTHOR>
 */
public class UserInfoUtils {

    /**
     * 获取当前登录账号id
     * @return accountId
     */
    public static Long getCurrentLoginAccountId() {
        String accountId = UserInfoInterceptor.accountIdLocal.get();
        if (StringUtils.isNotBlank(accountId)) {
            return Long.parseLong(accountId);
        }
        return null;
    }
}
