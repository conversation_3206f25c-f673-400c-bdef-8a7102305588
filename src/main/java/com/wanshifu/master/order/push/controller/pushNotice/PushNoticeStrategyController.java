package com.wanshifu.master.order.push.controller.pushNotice;


import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.notice.domains.request.pushNotice.*;
import com.wanshifu.master.order.push.domain.request.pushnotice.TemplateListRqt;
import com.wanshifu.master.order.push.domain.response.pushNotice.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.ListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.MonitorListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.TemplateListResp;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("pushNoticeStrategy")
public class PushNoticeStrategyController {

    @Resource
    private PushNoticeStrategyService pushNoticeStrategyService;

    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return pushNoticeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return pushNoticeStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableRqt rqt) {
        return pushNoticeStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteRqt rqt) {
        return pushNoticeStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody StrategyIdRqt rqt) {
        return pushNoticeStrategyService.detail(rqt);
    }


    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt){
        return pushNoticeStrategyService.list(rqt);
    }

    @PostMapping(value = "monitorList")
    public SimplePageInfo<MonitorListResp> monitorList(@Valid @RequestBody MonitorListRqt rqt){
        return pushNoticeStrategyService.monitorList(rqt);
    }

    @GetMapping(value = "exportPushNoticeList")
    public Integer exportPushNoticeList(@Valid MonitorListRqt rqt, HttpServletResponse httpServletResponse){
        return pushNoticeStrategyService.exportPushNoticeList(rqt,httpServletResponse);
    }


    @PostMapping(value = "templateList")
    public List<TemplateListResp> templateList(@Valid @RequestBody TemplateListRqt rqt){
        return pushNoticeStrategyService.templateList(rqt);
    }

}
