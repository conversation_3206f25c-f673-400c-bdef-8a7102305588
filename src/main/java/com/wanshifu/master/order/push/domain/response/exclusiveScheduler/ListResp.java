package com.wanshifu.master.order.push.domain.response.exclusiveScheduler;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import com.wanshifu.master.order.push.domain.enums.RangeTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ListResp {

    private Long configId;

    private Integer categoryId;
    private String categoryName;

    private Integer scheduleTime;


    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}