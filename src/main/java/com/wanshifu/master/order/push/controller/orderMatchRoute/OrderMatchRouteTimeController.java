package com.wanshifu.master.order.push.controller.orderMatchRoute;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderMatchRouteTimeApi;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteTimeListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.service.orderMatchRoute.OrderMatchRouteTimeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRouteTime")
public class OrderMatchRouteTimeController {

    @Resource
    private OrderMatchRouteTimeService orderMatchRouteTimeService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderMatchRouteTimeRqt rqt) {
        return orderMatchRouteTimeService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderMatchRouteTimeRqt rqt) {
        return orderMatchRouteTimeService.update(rqt);
    }






    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public OrderMatchRouteTimeDetailResp detail(@Valid @RequestBody OrderMatchRouteTimeDetailRqt rqt) {
        return orderMatchRouteTimeService.detail(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<OrderMatchRouteTimeListResp> list(@Valid @RequestBody OrderMatchRouteTimeListRqt rqt){
        return orderMatchRouteTimeService.list(rqt);
    }


}
