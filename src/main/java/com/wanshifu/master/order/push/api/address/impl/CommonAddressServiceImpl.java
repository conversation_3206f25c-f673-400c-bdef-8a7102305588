package com.wanshifu.master.order.push.api.address.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.master.order.push.api.address.CommonAddressService;
import com.wanshifu.master.order.push.domain.constant.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/19 9:51
 */
@Service
@Slf4j
public class CommonAddressServiceImpl implements CommonAddressService {
    @Resource
    private AddressApi addressApi;

    /**
     * 上下级地址id缓存天数，默认7天
     */
    @Value("${provinceNextId.redisDays:7}")
    private int provinceNextIdRedisDays;

    @Autowired
    private RedisHelper redisHelper;

    @Override
    public List<Long> getProvinceNextIdV2(Long divisionId, String sourceMethod, String params) {
        List<Long> provinceNextIds = Lists.newArrayList();
        if (Objects.isNull(divisionId) || divisionId == 0L) {
            String errMsg = String.format("getProvinceNextIdFailError,because divisionId param error! \ndivisionId:%s,\nsourceMethod:%s,\nparams:%s", divisionId, sourceMethod, params);
            log.error(errMsg);
            throw new BusException("分片字段传参有误！");
        }
        String provinceNextId = redisHelper.get(RedisKeyConstant.PROVINCE_NEXT_ID_KEY.concat(divisionId.toString()));
        if (Strings.isNullOrEmpty(provinceNextId)) {
            Long resId = addressApi.getProvinceNextId(divisionId);
            if (Objects.isNull(resId) || resId == 0L) {
                String errMsg = String.format("getProvinceNextIdFailValid,because division param valid! \ndivisionId:%s,\nsourceMethod:%s,\nparams:%s", divisionId, sourceMethod, params);
                log.error(errMsg);
                throw new BusException("分片字段传参无效值！");
            } else {
                //默认缓存7天,并控制在0点~6点的时间段失效
                int expireSeconds = getRandomExpireSeconds();
                redisHelper.set(RedisKeyConstant.PROVINCE_NEXT_ID_KEY.concat(divisionId.toString()), resId.toString(), expireSeconds);
                provinceNextIds.add(resId);
            }
        } else {
            provinceNextIds.add(Long.valueOf(provinceNextId));
        }
        //跨城分表
        provinceNextIds.add(99999L);
        return provinceNextIds;
    }


    /**
     * 到第{provinceNextIdRedisDays}天的0点~6点时间段的随机秒数过期
     * @return 缓存过期秒数
     */
    private int getRandomExpireSeconds() {
        // 获取当前时间
        Instant now = Instant.now();
        LocalDateTime nowDateTime = LocalDateTime.ofInstant(now, ZoneId.systemDefault());

        // 计算当前时间到当天24点的时间差（秒）
        LocalDateTime endOfDay = nowDateTime.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Duration durationToEndOfDay = Duration.between(nowDateTime.atZone(ZoneId.systemDefault()).toInstant(), endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        long secondsToEndOfDay = durationToEndOfDay.getSeconds();

        // 计算指定天数对应的秒数
        long daysInSeconds = (provinceNextIdRedisDays - 1) * 24 * 60 * 60L;

        // 生成0点到6点之间的随机秒数
        Random random = new Random();
        int randomSeconds = random.nextInt(6 * 60 * 60);

        // 总秒数 = 当前时间到当天24点的秒数 + 指定天数对应的秒数 + 0到6点之间的随机秒数
        long totalSeconds = secondsToEndOfDay + daysInSeconds + randomSeconds;

        // 检查是否溢出（由于我们仅计算了几天的秒数，所以这里几乎不会溢出）
        if (totalSeconds > Integer.MAX_VALUE) {
            throw new ArithmeticException("Expiration time exceeds Integer.MAX_VALUE");
        }
        return (int) totalSeconds;
    }

}
