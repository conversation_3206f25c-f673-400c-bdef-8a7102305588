package com.wanshifu.master.order.push.domain.response.specialGroup;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class ListResp {

    private Long strategyId;

    private String strategyName;

    private String serveNames;

    private String cityNames;

    private String pushGroups;

    private Integer strategyStatus;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     *创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}