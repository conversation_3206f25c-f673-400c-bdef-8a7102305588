package com.wanshifu.master.order.push.controller.agreementOrderDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.service.agreementOrderDistributeStrategy.AgreementOrderDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.CreateRqt;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.UpdateRqt;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.DeleteRqt;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.ListRqt;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.EnableRqt;


/**
 * Title：
 * 协议接单调度
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("agreementOrderDistributeStrategy")
public class AgreementOrderDistributeStrategyController {

    @Resource
    private AgreementOrderDistributeStrategyService agreementOrderDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return agreementOrderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return agreementOrderDistributeStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return agreementOrderDistributeStrategyService.detail(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
       return agreementOrderDistributeStrategyService.enable(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt){
        return agreementOrderDistributeStrategyService.list(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return agreementOrderDistributeStrategyService.delete(rqt);
    }

}
