package com.wanshifu.master.order.push.controller.normalOrderDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.normalOrderDistributeStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.service.normalOrderDistributeStrategy.NormalOrderDistributeStrategyService;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * Title�?
 *
 * @Auther <PERSON>
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("normalOrderDistributeStrategy")
public class NormalOrderDistributeStrategyController {

    @Resource
    private NormalOrderDistributeStrategyService normalOrderDistributeStrategyService;

    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return normalOrderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return normalOrderDistributeStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return normalOrderDistributeStrategyService.detail(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return normalOrderDistributeStrategyService.enable(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt){
        return normalOrderDistributeStrategyService.list(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return normalOrderDistributeStrategyService.delete(rqt);
    }

}
