package com.wanshifu.master.order.push.service.compensateDistribute;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.DetailResp;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.ListResp;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;

public interface CompensateDistributeService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);


    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

}
