package com.wanshifu.master.order.push.service.simulatePush;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.SendResult;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.api.*;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.MasterType;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.request.simulatePush.ExportSimulatePushRqt;
import com.wanshifu.master.order.push.domain.request.simulatePush.SimulateRqt;
import com.wanshifu.master.order.push.domain.resp.simulatePush.GetSimulatePushResp;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.*;
import com.wanshifu.master.order.push.domain.rqt.strategyCombination.DetailRqt;
import com.wanshifu.master.order.push.domain.vo.simulatePush.SimulatePushDetailExcelVo;
import com.wanshifu.master.order.push.domain.vo.simulatePush.SimulatePushExcelVo;
import com.wanshifu.master.order.push.service.BaseSelector;
import com.wanshifu.master.order.push.service.FeatureQueryFacade;
import com.wanshifu.master.order.push.service.PolarDBService;
import com.wanshifu.master.order.push.service.QLExpressHandler;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderSimulatePushService {

    @Resource
    private StrategyCombinationApi strategyCombinationApi;

    @Resource
    private BaseSelectStrategyApi baseSelectStrategyApi;

    @Resource
    private FilterStrategyApi filterStrategyApi;

    @Resource
    private SortingStrategyApi sortingStrategyApi;

    @Resource
    private BaseSelector baseSelector;



    @Resource
    private FeatureQueryFacade featureQueryFacade;


    @Resource
    private SimulatePushPolarDBService simulatePushPolarDBService;

    private PolarDBService polarDBService;

    @Resource
    private SimulatePushApi simulatePushApi;

    @Resource
    private QLExpressHandler qlExpressHandler;

    @Value("${simulatePush.orderNum}")
    private Integer simulatePushOrderNum;


    @Value("${wanshifu.rocketMQ.order-simulate-push-topic}")
    private String orderSimulatePushTopic;


    @Resource
    private RocketMqSendService rocketMqSendService;


    @Value("${simulate.push.detail.insert.batchSize}")
    private int simulatePushDetailInsertBatchSize;




    public int simulate(SimulateRqt simulateRqt){

        Long strategyCombinationId = simulateRqt.getStrategyCombinationId();
        GetCombinationLastSimulateRqt getCombinationLastSimulateRqt = new GetCombinationLastSimulateRqt();
        getCombinationLastSimulateRqt.setStrategyCombinationId(strategyCombinationId);
        StrategyCombinationSimulate simulate = simulatePushApi.getCombinationLastSimulate(getCombinationLastSimulateRqt);
        if(simulate != null && simulate.getSimulateStatus() == 1){
            return 0;
        }


        StrategyCombination strategyCombination = strategyCombinationApi.detail(new DetailRqt(strategyCombinationId));
        if(strategyCombination == null){
            return 0;
        }


        AddCombinationSimulateRqt addCombinationSimulateRqt = new AddCombinationSimulateRqt();
        addCombinationSimulateRqt.setStrategyCombinationId(strategyCombinationId);
        addCombinationSimulateRqt.setSimulateTime(new Date());
        Long simulateId = simulatePushApi.addCombinationSimulate(addCombinationSimulateRqt);


        CompletableFuture.runAsync(() -> {

            boolean inContinue;
            List<Long> simuleOrderIdList = new ArrayList<>();

            Date orderCreateTimeStart = DateUtils.addDays(new Date(), -30);
            Date orderCreateTimeEnd = DateUtils.addDays(orderCreateTimeStart, 1);
            do {
                List<Long> orderIdList = polarDBService.getOrderListByCategoryIdAndSecondDivisionId(Long.valueOf(strategyCombination.getBusinessLineId()), strategyCombination.getCategoryIds(), orderCreateTimeStart, orderCreateTimeEnd, strategyCombination.getCityIds());
                simuleOrderIdList.addAll(orderIdList);
                orderCreateTimeStart = DateUtils.addDays(orderCreateTimeStart, 1);
                orderCreateTimeEnd = DateUtils.addDays(orderCreateTimeEnd, 1);
                inContinue = simuleOrderIdList.size() < simulatePushOrderNum && orderCreateTimeEnd.before(new Date());
            } while (inContinue);


            int orderNum = 0;


            for (Long orderId : simuleOrderIdList) {
                try{
                    SimulatePushRqt rqt = new SimulatePushRqt();
                    rqt.setMasterOrderId(orderId);
                    rqt.setSimulateId(simulateId);
                    rqt.setBusinessLineId(strategyCombination.getBusinessLineId());
                    Message message = new Message(orderSimulatePushTopic, "simulate_push", JSON.toJSONString(rqt).getBytes(StandardCharsets.UTF_8));
                    String key = UUID.randomUUID().toString();
                    message.setKey(key);
                    SendResult sendResult = rocketMqSendService.sendDelayMessage(orderSimulatePushTopic,"simulate_push",JSON.toJSONString(rqt),1000L);
//                    SendResult sendResult = rocketMqSendService.sendSyncMessage(orderSimulatePushTopic, "simulate_push", JSON.toJSONString(rqt));
                    if (Objects.nonNull(sendResult)) {
                        orderNum = orderNum + 1;
                        log.info("sendSyncMessage success:[sendResult={}]", sendResult.toString());
                    }
                }catch(Exception e){
                    log.error("enableAlternativeStrategy error",e);
                }
            }

            if(orderNum > 0){

                UpdateOrderNumRqt updateOrderNumRqt = new UpdateOrderNumRqt();
                updateOrderNumRqt.setSimulateId(simulateId);
                updateOrderNumRqt.setOrderNum(orderNum);
                simulatePushApi.updateOrderNum(updateOrderNumRqt);
            }

            if (orderNum == 0) {
                //无可测算订单
                UpdateSimulateFinishRqt updateSimulateFinishRqt = new UpdateSimulateFinishRqt();
                updateSimulateFinishRqt.setSimulateId(simulateId);
                simulatePushApi.updateSimulateFinish(updateSimulateFinishRqt);
            }

        });

        return 1;
    }




    public int simulatePush(SimulatePushRqt rqt){

        Long simulateId = rqt.getSimulateId();
        Long masterOrderId = rqt.getMasterOrderId();

        try{
            GetSimulatePushRqt getSimulatePushRqt = new GetSimulatePushRqt();
            getSimulatePushRqt.setOrderId(masterOrderId);
            getSimulatePushRqt.setSimulateId(simulateId);
            GetSimulatePushResp getSimulatePushResp = simulatePushApi.getSimulatePush(getSimulatePushRqt);
            if(getSimulatePushResp != null && getSimulatePushResp.getSimulatePush() != null){
                return 0;
            }
            StrategyCombinationSimulate simulate = getSimulatePushResp.getStrategyCombinationSimulate();
            if(simulate == null || simulate.getSimulateStatus() != 1){
                log.error("simulatePush error,StrategyCombinationSimulate not exist");
                return 0;
            }

            Long strategyCombinationId = simulate.getStrategyCombinationId();
            StrategyCombination strategyCombination = strategyCombinationApi.detail(new DetailRqt(strategyCombinationId));

            Long timeStamp = System.currentTimeMillis();
            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);

            OrderDetailData orderDetailData = new OrderDetailData(rqt,null);
            MasterMatchCondition masterMatchCondition = baseSelector.buildMasterMatchCondition(orderDetailData,null,null);
            PushCommonObject pushCommonObject = new PushCommonObject();
            pushCommonObject.setOrderDetailData(orderDetailData);
            pushCommonObject.setMasterMatchCondition(masterMatchCondition);

            pushCommonObject.setOrderVersion(null);
            pushCommonObject.setTimestamp(timeStamp);


            //非重推时匹配策略组合
            if(strategyCombination != null){
                PushStrategyRule priorityPushStrategyRule = JSON.parseObject(strategyCombination.getPriorityStrategyCombination(),PushStrategyRule.class);
                PushStrategyRule alternatePushStrategyRule = JSON.parseObject(strategyCombination.getAlternateStrategyCombination(),PushStrategyRule.class);
                StrategyEntityList strategyEntityList = getStrategyEntityList(priorityPushStrategyRule.getStrategyList(),priorityPushStrategyRule.getPushRule(),orderDetailData.getAppointType());
                pushByStrategy(strategyEntityList,alternatePushStrategyRule,pushCommonObject,timeMark,true,simulateId);
            }

            AddSimulateOrderNumRqt addSimulateOrderNumRqt = new AddSimulateOrderNumRqt();
            addSimulateOrderNumRqt.setSimulateId(simulateId);
            simulatePushApi.addSimulatedOrderNum(addSimulateOrderNumRqt);


        }catch(DuplicateKeyException e){
            log.error("simulatePush error",e);
            return 0;
        }catch(Exception e){
            log.error("simulatePush error",e);
            AddSimulateOrderNumRqt addSimulateOrderNumRqt = new AddSimulateOrderNumRqt();
            addSimulateOrderNumRqt.setSimulateId(simulateId);
            simulatePushApi.addSimulatedOrderNum(addSimulateOrderNumRqt);
        }
        return 1;
    }


    private PushConfig getPushConfig(List<OrderPushRule> pushRuleList, Integer appointType){
        PushConfig pushConfig = new PushConfig();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(pushRuleList)){
            OrderPushRule pushRule = pushRuleList.stream().filter(currentPushRule -> currentPushRule.getAppointType().equals(appointType)).findFirst().orElse(null);
            if(pushRule != null){
                Integer firstPushNewMasterNum = 0;
                Integer firstPushOldMasterNum = 0;
                Integer delayPushNewMasterNum = 0;
                Integer delayPushOldMasterNum = 0;
                if(MasterType.MASETR_NEW.getCode().equals(pushRule.getFirstPushMasterFlag())){
                    //首轮推送模式：推送新师傅
                    firstPushNewMasterNum = (new BigDecimal(pushRule.getFirstPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getFirstPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    firstPushOldMasterNum = pushRule.getFirstPushMasterNumPerRound() - firstPushNewMasterNum;
                }else if(MasterType.MASTER_OLD.getCode().equals(pushRule.getFirstPushMasterFlag())){
                    //首轮推送模式：推送老师傅
                    firstPushOldMasterNum = (new BigDecimal(pushRule.getFirstPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getFirstPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    firstPushNewMasterNum = pushRule.getFirstPushMasterNumPerRound() - firstPushOldMasterNum;
                }else{
                    //首轮推送模式：不区分新老师傅
                    firstPushOldMasterNum = pushRule.getFirstPushMasterNumPerRound();
                }

                if(MasterType.MASETR_NEW.getCode().equals(pushRule.getDelayPushMasterFlag())){
                    //非首轮推送模式：推送新师傅
                    delayPushNewMasterNum = (new BigDecimal(pushRule.getDelayPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getDelayPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    delayPushOldMasterNum = pushRule.getDelayPushMasterNumPerRound() - delayPushNewMasterNum;
                }else if(MasterType.MASTER_OLD.getCode().equals(pushRule.getDelayPushMasterFlag())){
                    //非首轮推送模式：推送老师傅
                    delayPushOldMasterNum = (new BigDecimal(pushRule.getDelayPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getDelayPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    delayPushNewMasterNum = pushRule.getDelayPushMasterNumPerRound() - delayPushOldMasterNum;
                }else{
                    //非首轮推送模式：不区分新老师傅
                    delayPushOldMasterNum = pushRule.getDelayPushMasterNumPerRound();
                }

                return PushConfig.builder().setBestOfferNum(pushRule.getBestOfferNum())
                        .setDelayMinutesBetweenRounds(pushRule.getDelayMinutesBetweenRounds())
                        .setFirstPushNewMasterNum(firstPushNewMasterNum)
                        .setFirstPushOldMasterNum(firstPushOldMasterNum)
                        .setDelayPushNewMasterNumPerRound(delayPushNewMasterNum)
                        .setDelayPushOldMasterNumPerRound(delayPushOldMasterNum)
                        .setFirstPushMasterType(pushRule.getFirstPushMasterFlag())
                        .setDelayPushMasterType(pushRule.getDelayPushMasterFlag())
                        .build();
            }
        }
        return pushConfig;
    }


    private StrategyEntityList getStrategyEntityList(StrategyIdList strategyIdList,List<OrderPushRule> pushRuleList,Integer appointType){
        BaseSelect baseSelect = getBaseSelect(strategyIdList.getBaseSelectStrategyId());
        PushFilterList pushFilterList = getPushFilterList(strategyIdList.getFilterStrategyId());
        PushScorerList pushScorerList = getPushScorerList(strategyIdList.getSortingStrategyId());
        PushConfig pushConfig = getPushConfig( pushRuleList,appointType);
        return new StrategyEntityList(baseSelect,pushFilterList,pushScorerList,pushConfig);
    }

    private BaseSelect getBaseSelect(Long baseSelectStrategyId){
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyApi.detail(new com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.DetailRqt(baseSelectStrategyId));
        RangeSelect rangeSelect = JSON.parseObject(baseSelectStrategy.getRangeSelect(),RangeSelect.class);
        TechniqueSelect techniqueSelect = JSON.parseObject(baseSelectStrategy.getTechniqueSelect(),TechniqueSelect.class);
        StatusSelect statusSelect = JSON.parseObject(baseSelectStrategy.getStatusSelect(),StatusSelect.class);
        ServeDataSelect serveDataSelect = JSON.parseObject(baseSelectStrategy.getServeDataSelect(),ServeDataSelect.class);
        return new BaseSelect(baseSelectStrategy.getSnapshotId(),baseSelectStrategy.getStrategyName(),rangeSelect,techniqueSelect,statusSelect,serveDataSelect);
    }


    public PushFilterList getPushFilterList(Long filterStrategyId){
        FilterStrategy filterStrategy = filterStrategyApi.detail(new com.wanshifu.master.order.push.domain.rqt.filterStrategy.DetailRqt(filterStrategyId));
        PushFilterList pushFilterList = new PushFilterList();
        pushFilterList.setStrategyName(filterStrategy.getStrategyName());
        List<FilterRuleExpression> expressionList = JSON.parseArray(filterStrategy.getRuleExpression(),FilterRuleExpression.class);
        expressionList.stream().forEach(expression -> {
            pushFilterList.addFilter(new PushFilter(qlExpressHandler.getExpressRunner(),expression.getOpenConditionRuleExpression(),expression.getFilterRuleExpression(),
                    "test",expression.getOpenConditionRuleParams(), expression.getFilterRuleParams()));

        });
        return pushFilterList;
    }


    public PushScorerList getPushScorerList(Long sortingStrategyId){
        SortingStrategy sortingStrategy = sortingStrategyApi.detailV2(new com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt(sortingStrategyId));
        PushScorerList pushScorerList = new PushScorerList();
        pushScorerList.setSnapshotId(sortingStrategy.getSnapshotId());
        pushScorerList.setStrategyName(sortingStrategy.getStrategyName());
        if(sortingStrategy.getStrategyVersion() == 1){
            PushScorerObject pushScorerObject = new PushScorerObject();
            List<PushScorer> scorerList = new ArrayList<>();
            List<ScoreRuleExpression> expressionList = JSON.parseArray(sortingStrategy.getRuleExpression(),ScoreRuleExpression.class);
            expressionList.forEach(expression -> scorerList.add(new PushScorer(qlExpressHandler.getExpressRunner()
                    ,expression.getOpenConditionRuleExpression()
                    ,expression.getScoreRuleExpression()
                    ,expression.getRuleName()
                    ,expression.getOpenConditionRuleParams()
                    ,expression.getScoreRuleParams())));
            pushScorerObject.setScorerList(scorerList);
            pushScorerList.addPushScorerObject(pushScorerObject);
        }else{
            List<SortRuleExpression> expressionList = JSON.parseArray(sortingStrategy.getRuleExpression(),SortRuleExpression.class);
            expressionList.forEach(expression -> pushScorerList.addPushScorerObject(buildPushScoreObject(expression)));
        }

        return pushScorerList;
    }


    private PushScorerObject buildPushScoreObject(SortRuleExpression sortRuleExpression){
        OpenCondition openCondition = new OpenCondition(sortRuleExpression.getOpenConditionRuleExpression(),sortRuleExpression.getOpenConditionRuleParams());
        List<PushScorer> pushScorerList = new ArrayList<>(sortRuleExpression.getScoreRuleList().size());
        sortRuleExpression.getScoreRuleList().forEach(scoreRuleExpression -> pushScorerList.add(new PushScorer(qlExpressHandler.getExpressRunner()
                ,scoreRuleExpression.getOpenConditionRuleExpression()
                ,scoreRuleExpression.getScoreRuleExpression()
                ,scoreRuleExpression.getRuleName()
                ,scoreRuleExpression.getOpenConditionRuleParams()
                ,scoreRuleExpression.getScoreRuleParams())));
        return  new PushScorerObject(openCondition,pushScorerList);
    }




    private void pushByStrategy(StrategyEntityList strategyEntityList, PushStrategyRule alternatePushStrategyRule,
                                PushCommonObject pushCommonObject, String timeMark, boolean isPriorityStrategyPush,Long simulateId){

        OrderDetailData orderDetailData = pushCommonObject.getOrderDetailData();
        String orderVersion = pushCommonObject.getOrderVersion();
        Long timeStamp = pushCommonObject.getTimestamp();

        BaseSelect baseSelect;
        if(isPriorityStrategyPush) {
            baseSelect = strategyEntityList.getBaseSelect();
        }else{
            baseSelect = getBaseSelect(alternatePushStrategyRule.getStrategyList().getBaseSelectStrategyId());

        }

        pushCommonObject.setBaseSelect(baseSelect);
        baseSelector.baseSelectByStrategy(pushCommonObject,null);


        boolean isEnableAlternativeStrategy = isPriorityStrategyPush && alternatePushStrategyRule != null &&
                enableAlternativeStrategy(pushCommonObject.getMasterSet().size(),alternatePushStrategyRule.getOpenConditionQlExpression().getQlExpression());
        if(isEnableAlternativeStrategy){
            timeStamp = System.currentTimeMillis();
            orderVersion = String.valueOf(timeStamp);
            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushCommonObject.setOrderVersion(orderVersion);
            pushByStrategy(strategyEntityList,alternatePushStrategyRule,pushCommonObject,timeMark,false,simulateId);
            return;
        }

        PushFilterList pushFilterList;
        PushScorerList pushScorerList;
        PushConfig pushConfig;

        if(strategyEntityList != null) {
            pushFilterList = strategyEntityList.getPushFilterList();
            pushScorerList = strategyEntityList.getPushScorerList();
            pushConfig = strategyEntityList.getPushConfig();
        }else{
            pushFilterList = getPushFilterList(alternatePushStrategyRule.getStrategyList().getFilterStrategyId());
            pushScorerList = getPushScorerList(alternatePushStrategyRule.getStrategyList().getSortingStrategyId());
            pushConfig = getPushConfig(alternatePushStrategyRule.getPushRule(),orderDetailData.getAppointType());
        }



        Set<String> orderFeatureSet = new HashSet<>();
        orderFeatureSet.addAll(pushFilterList.getOrderFeatureSet());
        orderFeatureSet.addAll(pushScorerList.getOrderFeatureSet());


        PushFeature pushFeature = featureQueryFacade.getOrderFeatures(pushCommonObject , orderFeatureSet);

        final Integer bigOrderTag = orderDetailData.getTeamMasterOrderPush();
        if (bigOrderTag!=null) {
            pushFeature.addOrderFeature(FieldConstant.TEAM_MASTER_ORDER_PUSH, bigOrderTag);
        }


        checkCondition(pushFilterList,pushScorerList,pushFeature);


        Set<String> masterFeatureSet = new HashSet<>();
        masterFeatureSet.addAll(pushFilterList.getMasterFeatureSet());
        masterFeatureSet.addAll(pushScorerList.getMasterFeatureSet());
        masterFeatureSet.add("skill_degree");
        masterFeatureSet.add("smc_serve_degree");
        masterFeatureSet.add("is_forced_push");


        featureQueryFacade.getMasterFeatures(pushFeature,pushCommonObject.getMasterSet(),masterFeatureSet);


        List<PushMaster> engineMasterList = pushHandle(pushCommonObject,pushFilterList,pushScorerList,null,pushFeature,timeMark,orderVersion);

        isEnableAlternativeStrategy = isPriorityStrategyPush && alternatePushStrategyRule != null &&
                enableAlternativeStrategy(pushCommonObject.getMasterSet().size(),alternatePushStrategyRule.getOpenConditionQlExpression().getQlExpression());
        if(isEnableAlternativeStrategy){
            timeStamp = System.currentTimeMillis();
            orderVersion = String.valueOf(timeStamp);
            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushCommonObject.setOrderVersion(orderVersion);
            pushByStrategy(null,alternatePushStrategyRule,pushCommonObject,timeMark,false,simulateId);
            return;
        }


        Long orderId = pushCommonObject.getOrderDetailData().getMasterOrderId();


        // 初始化待处理师傅列表
        List<PushMaster> listToBeHandle = new ArrayList<>();
        // 构造待处理师傅列表
        engineMasterList.forEach(engineMaster -> {
            // 构造推送师傅
            PushMaster pushMaster = PushMaster.builder().setMasterId(engineMaster.getMasterId())
                    .setIsForcePushMaster(engineMaster.isForcePushMaster()).setScore(engineMaster.getScore())
                    .setIsNewMaster(engineMaster.isNewMaster())
                    .build();
            // 加入待处理列表
            listToBeHandle.add(pushMaster);
        });

        Collections.sort(listToBeHandle);


        PushMasterList toBePushedMasterList = new PushMasterList(listToBeHandle);


        List<PushMaster> finalList = toBePushedMasterList.getFirstPushMasterList(pushConfig);


        PushProgress pushProgress = toBePushedMasterList.getPushProgress();


        pushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + finalList.size());
        pushProgress.setPushedRound(pushProgress.getPushedRound() + 1);


        List<SimulatePushDetail> simulatePushDetailList = new ArrayList<>();
        finalList.stream().forEach(pushMaster -> {
            SimulatePushDetail simulatePushDetail = new SimulatePushDetail();
            simulatePushDetail.setOrderId(orderId);
            simulatePushDetail.setMasterId(Long.valueOf(pushMaster.getMasterId()));
            simulatePushDetail.setPushRound(1);
            simulatePushDetail.setPushTime(new Date());
            simulatePushDetail.setCreateTime(new Date());
            simulatePushDetail.setUpdateTime(new Date());
            simulatePushDetailList.add(simulatePushDetail);
        });

        int pushedNum = pushProgress.getFilteredMasterNum() + pushProgress.getPushedMasterNum();
        // 总长度小于已推数量
        boolean isHaveMoreMaster = pushProgress.getListLength() > pushedNum;
        int index = 0;

        while(isHaveMoreMaster){

            index = index + 1;
            List<PushMaster> pushMasterList = getDelayPushMasterList(pushProgress,pushConfig,toBePushedMasterList);
            if(!CollectionUtils.isEmpty(pushMasterList)){
                pushMasterList.stream().forEach(pushMaster -> {
                    SimulatePushDetail simulatePushDetail = new SimulatePushDetail();
                    simulatePushDetail.setOrderId(orderId);
                    simulatePushDetail.setMasterId(Long.valueOf(pushMaster.getMasterId()));
                    simulatePushDetail.setPushRound(pushProgress.getPushedRound());
                    simulatePushDetail.setPushTime(new Date());
                    simulatePushDetail.setCreateTime(new Date());
                    simulatePushDetail.setUpdateTime(new Date());
                    simulatePushDetailList.add(simulatePushDetail);
                });
            }

            // 总长度小于已推数量
            isHaveMoreMaster = pushProgress.getListLength() > (pushProgress.getFilteredMasterNum() + pushProgress.getPushedMasterNum());
            if(index > 100000){
                log.error("分轮推送了s100000次以上");
                break;
            }
        }


        AddSimulatePushRqt addSimulatePushRqt = new AddSimulatePushRqt();
        addSimulatePushRqt.setOrderId(orderId);
        addSimulatePushRqt.setSimulateId(simulateId);
        addSimulatePushRqt.setPushMasterNum(engineMasterList.size());
        addSimulatePushRqt.setPushRounds(pushProgress.getPushedRound());
        addSimulatePushRqt.setIsAlternateStrategy(isPriorityStrategyPush?0:1);
        addSimulatePushRqt.setStrategyListName(baseSelect.getStrategyName() + " - " + pushFilterList.getStrategyName() + " - " + pushScorerList.getStrategyName());
        Long simulatePushId = simulatePushApi.addSimulatePush(addSimulatePushRqt);

        simulatePushDetailList.stream().forEach(simulatePushDetail -> simulatePushDetail.setSimulatePushId(simulatePushId));


        batchSimulatePushDetailList(simulatePushDetailList);
    }


    private int batchSimulatePushDetailList(List<SimulatePushDetail> simulatePushDetailList){
        int count = 0;
        int batchNum = simulatePushDetailList.size() / simulatePushDetailInsertBatchSize;
        int leftNum = simulatePushDetailList.size() % simulatePushDetailInsertBatchSize;
        for(int index = 0;index < batchNum;index++){
            BatchAddSimulatePushDetailRqt batchAddSimulatePushDetailRqt = new BatchAddSimulatePushDetailRqt();
            batchAddSimulatePushDetailRqt.setSimulatePushDetailList(simulatePushDetailList.subList(index * simulatePushDetailInsertBatchSize,(index + 1) * simulatePushDetailInsertBatchSize));
            count = simulatePushApi.batchAddSimulatePushDetail(batchAddSimulatePushDetailRqt) + count;
        }

        if(leftNum > 0){
            BatchAddSimulatePushDetailRqt batchAddSimulatePushDetailRqt = new BatchAddSimulatePushDetailRqt();
            batchAddSimulatePushDetailRqt.setSimulatePushDetailList(simulatePushDetailList.subList(batchNum * simulatePushDetailInsertBatchSize,(batchNum * simulatePushDetailInsertBatchSize) + leftNum));
            count = simulatePushApi.batchAddSimulatePushDetail(batchAddSimulatePushDetailRqt) + count;
        }

        return count;
    }




    private List<PushMaster> getDelayPushMasterList(PushProgress pushProgress,PushConfig pushConfig,PushMasterList toBePushedMasterList){
        List<PushMaster> pushMasterList = new ArrayList<>();
        if(!MasterType.ALL.code.equals(pushConfig.getDelayPushMasterType())){

            List<PushMaster> toPushNewMasterList;
            if(pushProgress.getNewMasterOffset()+pushConfig.getDelayPushNewMasterNumPerRound() < toBePushedMasterList.getNewMasterList().size()){
                toPushNewMasterList = toBePushedMasterList.getNewMasterList().subList(pushProgress.getNewMasterOffset(),pushProgress.getNewMasterOffset()+pushConfig.getDelayPushNewMasterNumPerRound());
            }else{
                toPushNewMasterList = toBePushedMasterList.getNewMasterList().subList(pushProgress.getNewMasterOffset(),toBePushedMasterList.getNewMasterList().size());
            }

            List<PushMaster> toPushOldMasterList;

            if(pushProgress.getOldMasterOffset()+pushConfig.getDelayPushOldMasterNumPerRound() < toBePushedMasterList.getOldMasterList().size()){
                toPushOldMasterList = toBePushedMasterList.getOldMasterList().subList(pushProgress.getOldMasterOffset(),pushProgress.getOldMasterOffset()+pushConfig.getDelayPushOldMasterNumPerRound());
            }else{
                toPushOldMasterList = toBePushedMasterList.getOldMasterList().subList(pushProgress.getOldMasterOffset(),toBePushedMasterList.getOldMasterList().size());
            }

            pushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + toPushNewMasterList.size() + toPushOldMasterList.size());
            pushProgress.setPushedRound(pushProgress.getPushedRound() + 1);
            pushProgress.setNewMasterOffset(pushProgress.getNewMasterOffset() + toPushNewMasterList.size());
            pushProgress.setOldMasterOffset(pushProgress.getOldMasterOffset() + toPushOldMasterList.size());

            pushMasterList.addAll(toPushNewMasterList);
            pushMasterList.addAll(toPushOldMasterList);


        }else{
            Integer endOffset = pushProgress.getMasterOffset() + pushConfig.getDelayPushOldMasterNumPerRound();
            if(endOffset > toBePushedMasterList.getMasterList().size()){
                endOffset = toBePushedMasterList.getMasterList().size();
            }
            pushMasterList.addAll(toBePushedMasterList.getMasterList().subList(pushProgress.getMasterOffset(),endOffset));
            pushProgress.setPushedRound(pushProgress.getPushedRound() + 1);
            pushProgress.setMasterOffset(pushProgress.getMasterOffset() + pushMasterList.size());
            pushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + pushMasterList.size());
        }
        return pushMasterList;

    }


    private static final String CAN_PUSH_NUMBER = "canPushNumber";
    private boolean enableAlternativeStrategy(Integer canPushNumber,String openConditionExpression){
        DefaultContext<String, Object> context = new DefaultContext<>();
        context.put(CAN_PUSH_NUMBER,canPushNumber);
        try{
            return (Boolean)qlExpressHandler.getExpressRunner().execute(openConditionExpression, context,null,true,false);
        }catch(Exception e){
            log.error("enableAlternativeStrategy error",e);
            return false;
        }


    }


    public List<PushMaster> pushHandle(PushCommonObject pushCommonObject, PushFilterList pushFilterList, PushScorerList pushScorerList, PushScorerList listScorerList, PushFeature pushFeature, String timeMark, String orderVersion) {

        List<PushMaster> pushMasterList = new ArrayList<PushMaster>();
        Long globalOrderId = pushCommonObject.getOrderDetailData().getGlobalOrderId();
        DefaultContext<String, DefaultContext<String, Object>> masterFeatureListMap = pushFeature.getMasterFeature();
        DefaultContext<String, Object> orderFeature = pushFeature.getOrderFeature();
        // 构建师傅处理进度
        for (String masterId : masterFeatureListMap.keySet()) {
            DefaultContext<String, Object> masterFeatureMap = masterFeatureListMap.get(masterId);
            PushMaster pushMaster = new PushMaster();
            pushMaster.setMasterId(masterId);
            // 强推标记。若是强推师傅，直接进入待推送列表，不走后续流程。
            this.markDirectPushMaster(globalOrderId, masterFeatureMap, pushMaster);
            this.markMasterDegree(orderFeature, masterFeatureMap, pushMaster);//新老师傅标记
            if (pushMaster.isForcePushMaster()) {
                pushMasterList.add(pushMaster);

                continue;
            }
            // 过滤。若是被淘汰掉，则不进入待推送列表，也不走后面的评分器
            boolean isOut = this.filterMaster(masterId, pushFilterList, masterFeatureMap);
            if (isOut) {
                // outMasterCounter++;
                continue;
            }

            this.scoreToMaster(pushScorerList, masterFeatureMap, pushMaster);

            pushMasterList.add(pushMaster);
            // restMasterCounter++;

        }

        return pushMasterList;
    }


    public void checkCondition(PushFilterList pushFilterList, PushScorerList pushScorerList, PushFeature pushFeature) {
        DefaultContext<String, Object> conditionContext = pushFeature.getOrderFeature();

        //校验过滤器开启条件
        pushFilterList.setFilterList(pushFilterList.getFilterList().stream().filter(pushFilter -> {
            String conditionExpression = pushFilter.getConditionExpression();
            if (StringUtils.isBlank(conditionExpression)) {
                return true;
            }
            boolean checkConditionSucc = false;
            try {
                checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
            } catch (Exception e) {
                log.error("校验召回策略开启条件失败",e);
            }
            return checkConditionSucc;
        }).collect(Collectors.toList()));

        //校验评分器开启条件
        pushScorerList.setPushScorerObjectList(pushScorerList.getPushScorerObjectList().stream().filter(pushScorerObject -> {
            if(pushScorerObject.getOpenCondition() == null || StringUtils.isBlank(pushScorerObject.getOpenCondition().getConditionExpression())){
                return true;
            }
            String conditionExpression = pushScorerObject.getOpenCondition().getConditionExpression();
            boolean checkConditionSucc = false;
            try {
                checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
            } catch (Exception e) {
                log.error("校验精排策略匹配项开启条件失败",e);
            }

            return checkConditionSucc;
        }).collect(Collectors.toList()));


        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(pushScorerList.getPushScorerObjectList())){
            pushScorerList.getPushScorerObjectList().stream().forEach(pushScorerObject -> {
                pushScorerObject.setScorerList(pushScorerObject.getScorerList().stream().filter(pushScorer -> {
                    String conditionExpression = pushScorer.getConditionExpression();
                    if (StringUtils.isBlank(conditionExpression)) {
                        return true;
                    }
                    boolean checkConditionSucc = false;
                    try {
                        checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
                    } catch (Exception e) {
                        log.error("校验精排策略匹配项开启条件失败",e);
                    }

                    return checkConditionSucc;
                }).collect(Collectors.toList()));
            });
        }
    }

    private static final String KEY_NAME_IS_FORCED_PUSH = "is_forced_push";


    private void markDirectPushMaster(Long orderId, DefaultContext<String,Object> masterFeature,PushMaster pushMaster) {
        String isForcedPush;
        try {
            isForcedPush = masterFeature.get(KEY_NAME_IS_FORCED_PUSH).toString();
            if (isForcedPush != null && "1".equals(isForcedPush)) {
                pushMaster.setForcePushMaster();
//				LOGGER.info("[DirectPushMark] userOrderId={}, masterId={}", orderId, engineMaster.getMasterId());
            }
        } catch (Exception e) {
        }
    }

    /**
     * 标记师傅类型
     *
     * @param engineContextMaster
     *            待标记师傅
     * @return 标记后的师傅
     */
    private static final String SKILL_DEGREE = "skill_degree";
    private static final String SMC_SERVE_DEGREE = "smc_serve_degree";
    private void markMasterDegree(DefaultContext<String,Object> orderFeature, DefaultContext<String,Object> masterFeatureMap, PushMaster pushMaster) {

        String skillDegree;
        try {
            String busId = orderFeature.get(FieldConstant.BUSINESS_LINE_ID).toString();
            if ("2".equals(busId)) {
                skillDegree = masterFeatureMap.get(SMC_SERVE_DEGREE).toString();
                if (skillDegree != null && "0".equals(skillDegree)) {
                    pushMaster.setNewMaster();
                }
            } else {
                skillDegree = masterFeatureMap.get(SKILL_DEGREE).toString();
                if (skillDegree != null && "0".equals(skillDegree)) {
                    pushMaster.setNewMaster();
                }
            }
        } catch (Exception e) {
            log.error(String.format("markMasterDegree failed, masterId:%s,masterFeature:%s", pushMaster.getMasterId(), masterFeatureMap), e);
        }
    }


            /**
             * 过滤师傅
             *
             * @param pushFilterList
             *            待过滤师傅
             * @return true=淘汰掉,false=保留
             */
    private boolean filterMaster(String masterId,PushFilterList pushFilterList, DefaultContext<String, Object> masterFeatureMap) {
        for (PushFilter filter : pushFilterList.getFilterList()) {
            //todo 此处暂时改下
            if (filter.execute(masterFeatureMap)){
                return true;
            }
        }
        return false;
    }


    /**
     * 给师傅评分
     *
     * @param pushScorerList
     *            规则引擎
     * @param masterFeatureMap
     *            待评分师傅
     * @return 评分后的师傅
     */
    private void scoreToMaster(PushScorerList pushScorerList, DefaultContext<String, Object> masterFeatureMap, PushMaster pushMaster) {

        if(pushScorerList == null || CollectionUtils.isEmpty(pushScorerList.getPushScorerObjectList()) ||
                CollectionUtils.isEmpty(pushScorerList.getPushScorerObjectList().get(0).getScorerList())){
            log.info("未找到评分器");
            return ;
        }

        pushScorerList.getPushScorerObjectList().get(0).getScorerList().forEach(scorer -> {
            scorer.getMasterFeatureList().forEach(featureCode -> {
                Object masterFeatureValue = masterFeatureMap.get(featureCode);
                if (masterFeatureValue instanceof List) {
                    masterFeatureMap.put(featureCode, masterFeatureMap.get(featureCode + ":calculateValue"));
                }
            });
            BigDecimal score = scorer.execute(masterFeatureMap);
            if (score!=null) {
                pushMaster.addScore(score);
                // 更新处理进度
            }
        });
    }



    public void exportSimulatePush(ExportSimulatePushRqt rqt, HttpServletResponse httpServletResponse) throws Exception{
        GetCombinationLastSimulateRqt getCombinationLastSimulateRqt = new GetCombinationLastSimulateRqt();
        getCombinationLastSimulateRqt.setStrategyCombinationId(rqt.getStrategyCombinationId());
        StrategyCombinationSimulate simulate = simulatePushApi.getCombinationLastSimulate(getCombinationLastSimulateRqt);
        if(simulate == null || simulate.getSimulateStatus() != 2){
            return ;
        }


        Long simulateId = simulate.getSimulateId();

        Integer pageNum = 1;

        Integer pageSize = rqt.getPageSize();

        List<SimulatePush> simulatePushList = simulatePushPolarDBService.getSimulatePushList(simulateId,pageNum,pageSize);
        List<SimulatePushExcelVo> exportSimulatePushList = new ArrayList<>();
        List<SimulatePushDetailExcelVo> exportSimulatePushDetailList = new ArrayList<>();
        while(!CollectionUtils.isEmpty(simulatePushList)){
            Set<Long> orderIds =  simulatePushList.stream().map(SimulatePush::getOrderId).collect(Collectors.toSet());
            Map<Long,String> orderNoMap = polarDBService.getOrderNo(orderIds);
            simulatePushList.stream().forEach(simulatePush -> {
                SimulatePushExcelVo simulatePushExcelVo = new SimulatePushExcelVo();
                BeanUtils.copyProperties(simulatePush,simulatePushExcelVo);
                simulatePushExcelVo.setIsAlternativeStrategy(simulatePush.getIsAlternateStrategy() == 1 ? "是" : "否");
                simulatePushExcelVo.setOrderNo(orderNoMap.get(simulatePush.getOrderId()));
                exportSimulatePushList.add(simulatePushExcelVo);


                List<SimulatePushDetail> simulatePushDetailList  = simulatePushPolarDBService.getSimulatePushDetailList(simulatePush.getSimulatePushId());
                if(!CollectionUtils.isEmpty(simulatePushDetailList)){
                    Set<Long> masterIds =  simulatePushDetailList.stream().map(SimulatePushDetail::getMasterId).collect(Collectors.toSet());
                    Map<Long,Map<String,Object>> masterMap = polarDBService.getMasterList(masterIds);
                    simulatePushDetailList.forEach(simulatePushDetail -> {
                        Long masterId = simulatePushDetail.getMasterId();
                        SimulatePushDetailExcelVo simulatePushDetailExcelVo = new SimulatePushDetailExcelVo();
                        simulatePushDetailExcelVo.setOrderNo(simulatePushExcelVo.getOrderNo());
                        simulatePushDetailExcelVo.setMasterId(masterId);
                        Map<String,Object> map = masterMap.get(masterId);
                        simulatePushDetailExcelVo.setMasterPhone(map != null ? (String)map.get("masterPhone") : "");
                        simulatePushDetailExcelVo.setMasterName(map != null ? (String)map.get("masterName") :"");
                        simulatePushDetailExcelVo.setPushTime(simulatePushDetail.getPushTime());
                        simulatePushDetailExcelVo.setPushRound(simulatePushDetail.getPushRound());
                        exportSimulatePushDetailList.add(simulatePushDetailExcelVo);
                    });
                }

            });
            pageNum = pageNum + 1;
            simulatePushList = simulatePushPolarDBService.getSimulatePushList(simulateId,pageNum,pageSize);
        }

        /*ExportParams exportParams = new ExportParams();
        exportParams.setTitle("测算推单");
        exportParams.setSheetName("测算主表");

        Workbook workbook;
        ExcelExportServer excelExportServer = new ExcelExportServer();
        if (ExcelType.HSSF.equals(exportParams.getType())) {
            workbook = new HSSFWorkbook();
        } else if (exportSimulatePushList.size() < 1000) {
            workbook = new XSSFWorkbook();
        } else {
            workbook = new SXSSFWorkbook();
        }
        excelExportServer.createSheet(workbook, exportParams, SimulatePushExcelVo.class, exportSimulatePushList);
        exportParams.setTitle("测算推单");
        exportParams.setSheetName("测算明细");
        excelExportServer.createSheet(workbook, exportParams, SimulatePushDetailExcelVo.class, exportSimulatePushDetailList);*/


     /*   String fileName = "simulatePush";
        httpServletResponse.setHeader("Content-Disposition", "attachment;Filename=" + fileName + System.currentTimeMillis() + ".xls");
        ServletOutputStream outputStream = httpServletResponse.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();*/




    }



}
