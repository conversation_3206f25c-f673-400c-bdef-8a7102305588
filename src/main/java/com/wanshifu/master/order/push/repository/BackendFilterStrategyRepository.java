//package com.wanshifu.master.order.push.repository;
//
//import cn.hutool.core.lang.Assert;
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.constant.CommonConstant;
//import com.wanshifu.master.order.push.domain.po.FilterStrategy;
//import com.wanshifu.master.order.push.mapper.BackendFilterStrategyMapper;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
//@Repository
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class BackendFilterStrategyRepository extends BaseRepository<FilterStrategy> {
//
//    private final BackendFilterStrategyMapper backendFilterStrategyMapper;
//
//
//    public FilterStrategy selectByStrategyId(Long strategyId) {
//        FilterStrategy filterStrategy = this.selectByPrimaryKey(strategyId);
//        Assert.isTrue(filterStrategy != null && Objects.equals(filterStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
//        return filterStrategy;
//    }
//
//    public FilterStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Long strategyId) {
//        Example example = new Example(FilterStrategy.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("strategyName", strategyName)
//                .andEqualTo("businessLineId", businessLineId)
//                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
//        if (strategyId != null) {
//            criteria.andNotEqualTo("strategyId", strategyId);
//        }
//        return CollectionUtils.getFirstSafety(this.selectByExample(example));
//    }
//
//    public int create(String strategyName, Long snapshotId,String strategyDesc, String filterStrategyJson, String ruleExpression, Integer businessLineId, String categoryIds, Long accountId,Integer strategyVersion) {
//        FilterStrategy filterStrategy = new FilterStrategy();
//        filterStrategy.setSnapshotId(snapshotId);
//        filterStrategy.setStrategyName(strategyName);
//        filterStrategy.setStrategyDesc(strategyDesc);
//        filterStrategy.setCategoryIds(categoryIds);
//        filterStrategy.setFilterRule(filterStrategyJson);
//        filterStrategy.setRuleExpression(ruleExpression);
//        filterStrategy.setBusinessLineId(businessLineId);
//        filterStrategy.setStrategyStatus(CommonConstant.STRATEGY_STATUS_0);
//        filterStrategy.setStrategyVersion(strategyVersion);
//        filterStrategy.setCreateAccountId(accountId);
//        filterStrategy.setUpdateAccountId(accountId);
//        return this.insertSelective(filterStrategy);
//    }
//
//    public int update(Long strategyId,Long snapshotId, String strategyName, String strategyDesc, String filterStrategyJson, String ruleExpression, Integer businessLineId, String categoryIds, Long loginUserId) {
//        FilterStrategy filterStrategy = new FilterStrategy();
//        filterStrategy.setStrategyId(strategyId);
//        filterStrategy.setSnapshotId(snapshotId);
//        filterStrategy.setStrategyName(strategyName);
//        filterStrategy.setStrategyDesc(strategyDesc);
//        filterStrategy.setCategoryIds(categoryIds);
//        filterStrategy.setFilterRule(filterStrategyJson);
//        filterStrategy.setRuleExpression(ruleExpression);
//        filterStrategy.setBusinessLineId(businessLineId);
//        filterStrategy.setUpdateAccountId(loginUserId);
//        return this.updateByPrimaryKeySelective(filterStrategy);
//    }
//
//    public List<FilterStrategy> selectList(Long businessLineId, String strategyName, Integer strategyStatus, Date createStartTime, Date createEndTime, List<Long> categoryIds) {
//        return backendFilterStrategyMapper.selectList(businessLineId, strategyName, strategyStatus, createStartTime, createEndTime, categoryIds);
//    }
//
//    public int updateStatus(Long strategyId, Integer strategyStatus) {
//        FilterStrategy filterStrategy = new FilterStrategy();
//        filterStrategy.setStrategyId(strategyId);
//        filterStrategy.setStrategyStatus(strategyStatus);
//        return this.updateByPrimaryKeySelective(filterStrategy);
//    }
//
//    public int softDeleteByStrategyId(Long strategyId) {
//        FilterStrategy filterStrategy = new FilterStrategy();
//        filterStrategy.setStrategyId(strategyId);
//        filterStrategy.setIsDelete(CommonConstant.DELETE_STATUS_1);
//        return updateByPrimaryKeySelective(filterStrategy);
//    }
//
//    public List<FilterStrategy> selectByStrategyIds(List<Long> filterStrategyIds) {
//        Example example = new Example(FilterStrategy.class);
//        example.createCriteria().andIn("strategyId",filterStrategyIds);
//        return this.selectByExample(example);
//    }
//}
