package com.wanshifu.master.order.push.service.orderSortingStrategy;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.request.orderSortingStrategy.*;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.ListResp;
import com.wanshifu.master.order.push.domain.response.orderSortingStrategy.feature.SpecialCategoryResp;
import com.wanshifu.master.order.sort.domains.api.request.orderSortStrategy.ListRqt;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface OrderSortingStrategyService {

    Integer create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    Integer enable(EnableRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    SpecialCategoryResp specialCategoryConfigQuery();

    Integer specialCategoryConfigUpdate(UpdateSpecialCategoryRqt updateSpecialCategoryRqt);
}