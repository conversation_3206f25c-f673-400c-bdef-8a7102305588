package com.wanshifu.master.order.push.domain.vo.common;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-18 14:15
 */
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(200, "请求成功"),

    /**
     * 失败
     */
    FAIL(400, "请求失败"),
    /**
     * 登录
     */
    ACCOUNT_IS_EMPTY(10001, "账号不能为空"),
    ACCOUNT_OR_PASSWORD_ERROR(10002, "账号或密码错�?),
    ACCOUNT_NAME_EXITS(10003, "账号已存�?),
    ACCOUNT_DISABLED(10004, "账号已禁�?),
    USER_INFO_NOT_EXIST(10005, "用户信息不存�?),
    USER_INFO_UPDATE_FAIL(10006, "更新用户信息失败"),
    USER_INFO_SAVE_FAIL(10007, "保存用户信息失败"),


    LOGIN_FAIL(20001, "登录失败"),
    NOT_LOGIN(20002, "未登录，请先登录"),
    LOGIN_METHOD_IS_EMPTY(20004, "登录方式不能为空"),
    AUTH_SERVER_ERROR(20005, "授权服务异常"),


    /**
     * permission
     */
    PERMISSION_ACCESS_DENIED(50001, "permission access denied"),

    /**
     * 参数校验
     */
    PARAMS_VALIDATE_FAIL(-10000, "参数校验失败"),
    PARAMS_REQUEST_VALIDATE_FAIL(-10002, "请求参数错误"),

    /**
     * token
     */
    TOKEN_IS_EMPTY(-10101, "token must not be null"),
    TOKEN_VALIDATE_FAIL(-10102, "token check fail"),
    TOKEN_INVALID(-10103, "登录已过�?),
    REFRESH_TOKEN_NOT_EXIST(-10104, "refresh token not exist"),
    ACCESS_TOKEN_IS_EMPTY(-10105, "access token is empty"),

    /**
     * 未知错误
     */
    UN_KNOWN_ERROR(-1, "未知错误"),
    ;

    private final Integer code;

    private final String msg;

    ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
