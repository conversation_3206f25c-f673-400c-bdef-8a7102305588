package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述 :  订单标识枚举.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 16:44
 */
@AllArgsConstructor
@Getter
public enum BusinessLineEnum {

    BUSINESS_LINE_ID_1(1, "企业业务线"),
    BUSINESS_LINE_ID_2(2, "家庭业务线"),
    BUSINESS_LINE_ID_999(999, "家庭业务线"),
    BUSINESS_LINE_ID_3(3, "创新业务线");

    private final Integer code;

    private final String desc;
}