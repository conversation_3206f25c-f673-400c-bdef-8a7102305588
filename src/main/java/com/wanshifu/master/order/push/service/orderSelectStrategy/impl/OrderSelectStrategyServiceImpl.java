package com.wanshifu.master.order.push.service.orderSelectStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.api.OrderSelectStrategyApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.MasterResourcesEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFromEnum;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.filterStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.GetOrderSelectStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.OrderSelectStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaByCodesRqt;
import com.wanshifu.master.order.push.domain.rqt.common.GetMasterQuotaValueRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.orderSelectStrategy.OrderSelectStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderSelectStrategyServiceImpl implements OrderSelectStrategyService {

    @Resource
    private OrderSelectStrategyApi orderSelectStrategyApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private IopAccountApi iopAccountApi;


    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;



    @Override
   public SimplePageInfo<GetOrderSelectStrategyListResp> list(GetOrderSelectStrategyListRqt rqt){

        SimplePageInfo<OrderSelectStrategy> simplePageInfo = orderSelectStrategyApi.list(rqt);


        SimplePageInfo<GetOrderSelectStrategyListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderSelectStrategy> orderSelectStrategyList = simplePageInfo.getList();

        //类目名称

        List<Long> updateAccountIds = orderSelectStrategyList.stream().map(OrderSelectStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<GetOrderSelectStrategyListResp> listResps = BeanCopyUtil.copyListProperties(orderSelectStrategyList, GetOrderSelectStrategyListResp.class, (s, t) -> {
            //规则条数
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            if(StringUtils.isNotBlank(s.getSelectRule())){
                OrderSelectStrategyDetailResp.SelectStrategyVo selectStrategyVo = JSON.parseObject(s.getSelectRule(),OrderSelectStrategyDetailResp.SelectStrategyVo.class);
                t.setIsAppointGroup(selectStrategyVo.getAppointGroup().getIsAppointGroup());
            }
            MasterResourcesEnum masterResourcesEnum = MasterResourcesEnum.asValue(s.getMasterResources());
            t.setMasterResources(masterResourcesEnum != null ? masterResourcesEnum.getDesc() : "-");
            OrderFromEnum orderFromEnum = OrderFromEnum.asValue(s.getOrderFrom());
            t.setOrderFrom(orderFromEnum != null ? orderFromEnum.name : "-");

            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }


    @Override
    public Integer create(CreateOrderSelectStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setCreateAccountId(loginUserId);
        return orderSelectStrategyApi.create(rqt);
    }


    @Override
    public Integer update(UpdateOrderSelectStrategyRqt rqt){

        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderSelectStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableOrderSelectStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderSelectStrategyApi.enable(rqt);
    }

    @Resource
    private CommonApi commonApi;

    @Resource
    private ServeCommonService serveCommonService;

    @Override
    public OrderSelectStrategyDetailResp detail(OrderSelectStrategyDetailRqt rqt){

        OrderSelectStrategy orderSelectStrategy = orderSelectStrategyApi.detail(rqt);
        if(orderSelectStrategy == null){
            return null;
        }

        Long businessLineId = orderSelectStrategy.getBusinessLineId();

        OrderSelectStrategyDetailResp detailResp = new OrderSelectStrategyDetailResp();
        detailResp.setStrategyId(orderSelectStrategy.getStrategyId());
        detailResp.setBusinessLineId(businessLineId);
        detailResp.setStrategyName(orderSelectStrategy.getStrategyName());
        detailResp.setStrategyDesc(orderSelectStrategy.getStrategyDesc());
        detailResp.setOrderFrom(orderSelectStrategy.getOrderFrom());
        detailResp.setMasterResources(orderSelectStrategy.getMasterResources());
        if(StringUtils.isBlank(orderSelectStrategy.getSelectRule())){
            return detailResp;
        }

        detailResp.setSelectStrategy(JSON.parseObject(orderSelectStrategy.getSelectRule(),OrderSelectStrategyDetailResp.SelectStrategyVo.class));


        List<OrderSelectStrategyDetailResp.RuleItem> ruleItemList = detailResp.getSelectStrategy().getRuleList();

//        List<String> quotaCodes = ruleItemVoList.stream().map(OrderSelectStrategyDetailResp.RuleItem::getItemName).distinct().collect(Collectors.toList());
//
//        GetMasterQuotaByQuotaCodesRqt getMasterQuotaByQuotaCodesRqt = new GetMasterQuotaByQuotaCodesRqt();
//        getMasterQuotaByQuotaCodesRqt.setMasterQuotaCodelist(quotaCodes);
//        List<MasterQuotaResp> masterQuotaRespList = orderSelectStrategyApi.getMasterQuotaByQuotaCodes(getMasterQuotaByQuotaCodesRqt);
//        Map<String, MasterQuotaResp> masterQuotaMap = masterQuotaRespList.stream().collect(Collectors.toMap(MasterQuotaResp::getQuotaCode,MasterQuotaResp->MasterQuotaResp));




        //配置中的师傅人群id
        List<Long> masterGroupIds = ruleItemList.stream().map(OrderSelectStrategyDetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());


        List<OrderSelectStrategyDetailResp.GroupItemVo> groupItemVoList = detailResp.getSelectStrategy().getAppointGroup().getItemList();

        if(CollectionUtils.isNotEmpty(groupItemVoList)){
            masterGroupIds.addAll(groupItemVoList.stream().map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList()));
        }

        Map<Long, String> groupNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(masterGroupIds)) {
            groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
        }

        Map<Long,String> finalGroupNameMap = groupNameMap;


        if(CollectionUtils.isNotEmpty(groupItemVoList)){
            groupItemVoList.forEach(groupItemVo -> {
                String itemTitle = "师傅人群";
                List<OrderSelectStrategyDetailResp.TermItem> termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("包含", "in"), new OrderSelectStrategyDetailResp.TermItem("不包含", "not_in"));
                groupItemVo.setTermList(termItems);
                List<OrderSelectStrategyDetailResp.ValueItem> valueItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(groupItemVo.getItemValue()), "师傅人群"), groupItemVo.getItemValue()));
                groupItemVo.setValueList(valueItems);
                groupItemVo.setItemTitle(itemTitle);


            });
        }


        List<OrderSelectStrategyDetailResp.RuleItem> ruleItems = detailResp.getSelectStrategy().getRuleList();
        //配置中的指标code
        List<String> quotaCodes = ruleItems.stream().map(OrderSelectStrategyDetailResp.RuleItem::getFilterRule).filter(Objects::nonNull).flatMap(it -> it.getItemList().stream()).map(OrderSelectStrategyDetailResp.FilterRuleItem::getItemName).distinct().collect(Collectors.toList());

        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 3L) {
                businessLineId = 1L;
            } else if (businessLineId == 999L) {
                businessLineId = 2L;
            }
        }

        GetMasterQuotaByCodesRqt getMasterQuotaByCodesRqt = new GetMasterQuotaByCodesRqt();
        getMasterQuotaByCodesRqt.setQuotaCodes(quotaCodes);
        getMasterQuotaByCodesRqt.setBusinessLineId(businessLineId.intValue());

        List<com.wanshifu.master.order.push.domain.po.MasterQuota> masterQuotas = commonApi.getMasterQuotaByCodes(getMasterQuotaByCodesRqt);
        Map<String, com.wanshifu.master.order.push.domain.po.MasterQuota> masterQuotaMap = masterQuotas.stream().collect(Collectors.toMap(com.wanshifu.master.order.push.domain.po.MasterQuota::getQuotaCode, Function.identity()));
        List<Long> masterQuotaIds = masterQuotas.stream().map(com.wanshifu.master.order.push.domain.po.MasterQuota::getQuotaId).collect(Collectors.toList());
        GetMasterQuotaValueRqt getMasterQuotaValueRqt = new GetMasterQuotaValueRqt();
        getMasterQuotaValueRqt.setMasterQuotaIdList(masterQuotaIds);
        Map<Long, List<com.wanshifu.master.order.push.domain.po.MasterQuotaValue>> masterQuotaValueMap = commonApi.getMasterQuotaValue(getMasterQuotaValueRqt).stream().collect(Collectors.groupingBy(MasterQuotaValue::getMasterQuotaId));

        Set<Long> serveIds = ruleItems.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;
        ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

            List<LinkedList<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));

        ruleItems.forEach(ruleItem -> Optional.ofNullable(ruleItem.getFilterRule().getItemList()).ifPresent(it -> it.forEach(item -> {
            String quotaCode = item.getItemName();
            String itemTitle = "";
            List<OrderSelectStrategyDetailResp.TermItem> termItems = Lists.newArrayList();
            List<OrderSelectStrategyDetailResp.ValueItem> valueItems = Lists.newArrayList();

            if (StringUtils.equals(quotaCode, "master_group")) {
                //师傅人群
                itemTitle = "师傅人群";
                termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("包含", "in"), new OrderSelectStrategyDetailResp.TermItem("不包含", "not_in"));
                valueItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(item.getItemValue()), "师傅人群"), item.getItemValue()));
            } else {
                MasterQuota masterQuota = masterQuotaMap.get(quotaCode);
                if (masterQuota != null) {
                    itemTitle = masterQuota.getQuotaName();
                    if (StringUtils.equals(masterQuota.getValueType(), "enum_value")) {
                        termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("包含", "in"), new OrderSelectStrategyDetailResp.TermItem("不包含", "not_in"));
                        valueItems = masterQuotaValueMap.getOrDefault(masterQuota.getQuotaId(), Collections.emptyList()).stream().map(quotaCodeValue -> new OrderSelectStrategyDetailResp.ValueItem(quotaCodeValue.getName(), quotaCodeValue.getCode())).collect(Collectors.toList());
                    } else {
                        termItems = Lists.newArrayList(new OrderSelectStrategyDetailResp.TermItem("大于等于", ">="), new OrderSelectStrategyDetailResp.TermItem("大于", ">"), new OrderSelectStrategyDetailResp.TermItem("等于", "="), new OrderSelectStrategyDetailResp.TermItem("小于", "<"), new OrderSelectStrategyDetailResp.TermItem("小于等于", "<="));
                    }
                }
            }
            item.setItemTitle(itemTitle);
            item.setTermList(termItems);
            item.setValueList(valueItems);
        })));



        return detailResp;

    }

    @Override
    public Integer delete(DeleteOrderSelectStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderSelectStrategyApi.delete(rqt);
    }


    @Override
    public SimplePageInfo<MasterQuotaResp> quotaList(MasterQuotaListRqt rqt){
        return orderSelectStrategyApi.quotaList(rqt);
    }


}
