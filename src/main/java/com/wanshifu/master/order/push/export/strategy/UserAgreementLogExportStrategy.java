package com.wanshifu.master.order.push.export.strategy;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushMatchLogApi;
import com.wanshifu.master.order.push.domain.dto.UserAgreementPushMatchLogDto;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.export.exceldto.UserAgreementPushMatchLogExcelDto;
import com.wanshifu.master.order.push.export.convert.PushMatchLogExportConvert;
import com.wanshifu.master.order.push.export.convert.UserAgreementLogExportConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.rmi.server.ExportException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 平台协议派单日志导出策略
 * @date 2025/5/27 17:34
 */
@Component
@Slf4j
public class UserAgreementLogExportStrategy implements PushMatchLogExportStrategy<UserAgreementPushMatchLogDto, UserAgreementPushMatchLogExcelDto>{

    @Resource
    private PushMatchLogApi pushMatchLogApi;

    @Override
    public List<UserAgreementPushMatchLogDto> queryData(PushMatchLogRqt pushMatchLogRqt, int pageNum) {
        pushMatchLogRqt.setPageNum(pageNum);
        SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList = pushMatchLogApi.userAgreementPushMatchLogList(pushMatchLogRqt);
        if (Objects.isNull(userAgreementPushMatchLogList)) {
            return new ArrayList<>();
        }
        List<UserAgreementPushMatchLogDto> list = userAgreementPushMatchLogList.getList();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return list;
        }
    }

    @Override
    public PushMatchLogExportConvert<UserAgreementPushMatchLogDto, UserAgreementPushMatchLogExcelDto> getConverter() {
        return new UserAgreementLogExportConvert();
    }

    @Override
    public FileUploadResp exportAndUpload(PushMatchLogRqt pushMatchLogRqt, HorizontalCellStyleStrategy styleStrategy) {

        SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogDtoSimplePageInfo = pushMatchLogApi.userAgreementPushMatchLogList(pushMatchLogRqt);

        if (Objects.isNull(userAgreementPushMatchLogDtoSimplePageInfo)
                || userAgreementPushMatchLogDtoSimplePageInfo.getTotal() == 0) {

            throw new BusException("没有数据可导出！");
        }

        long totalCount = userAgreementPushMatchLogDtoSimplePageInfo.getTotal();
        if (totalCount > 300000) {
            throw new BusException("导出数据量大于30w,文件太大上传七牛云有风险，暂不予导出！");
        }

        List<UserAgreementPushMatchLogExcelDto> dataChunk = new ArrayList<>(10000);

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            try (ExcelWriter excelWriter = EasyExcel.write(baos, UserAgreementPushMatchLogExcelDto.class)
                    .registerWriteHandler(styleStrategy)
                    .build()) {

                WriteSheet writeSheet = EasyExcel.writerSheet("平台协议派单").build();

                int pageNum = 1;

                while (true) {
                    //分页查询原始数据（每次 200 条）
                    List<UserAgreementPushMatchLogDto> rawData = this.queryData(pushMatchLogRqt, pageNum);

                    //数据转换
                    rawData.forEach(item -> {
                        UserAgreementPushMatchLogExcelDto excelDto = this.getConverter().convert(item);
                        dataChunk.add(excelDto);
                    });


                    //达到 1 万条时写入 Excel 并清空缓存
                    if (dataChunk.size() >= 10000) {
                        excelWriter.write(dataChunk, writeSheet);
                        //清空缓存，准备下一批次
                        dataChunk.clear();
                    }

                    //终止条件：无更多数据
                    if (rawData.isEmpty()) {
                        if (!dataChunk.isEmpty()) {
                            //写入最后一批不足1万条的数据
                            excelWriter.write(dataChunk, writeSheet);
                        }
                        break;
                    }
                    pageNum++;
                }
                //显式 finish!关闭文件锁
                excelWriter.finish();

            } catch (Exception e) {
                log.error("easyExcel导出时ExcelWriter创建失败", e);
                throw e;
            }
            //上传到七牛云
            return FileUploadUtils.upload(baos.toByteArray(), "xlsx");

        } catch (Exception e) {
            throw new BusException(String.format("easyExcel导出时或文件上传七牛云失败！，msg:%s", e.getMessage()));
        }


    }

    @Override
    public String getExportType() {
        return PushLogMatchType.USER_AGREEMENT.getCode();
    }
}
