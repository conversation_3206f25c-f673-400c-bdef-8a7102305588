package com.wanshifu.master.order.push.service.specialGroupStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.response.specialGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;

/**
 * 特殊人群策略服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
public interface SpecialGroupStrategyService {

    /**
     * 创建特殊人群策略
     * 
     * @param rqt 创建请求
     * @return 策略id
     */
    int create(CreateRqt rqt);

    /**
     * 更新特殊人群策略
     * 
     * @param rqt 更新请求
     * @return 更新结果
     */
    int update(UpdateRqt rqt);

    /**
     * 获取特殊人群策略详情
     * 
     * @param rqt 详情请求
     * @return 策略详情
     */
    DetailResp detail(DetailRqt rqt);

    /**
     * 分页查询特殊人群策略列表
     * 
     * @param rqt 列表查询请求
     * @return 分页结果
     */
    SimplePageInfo<ListResp> list(ListRqt rqt);

    /**
     * 启用/禁用特殊人群策略
     * 
     * @param rqt 启用/禁用请求
     * @return 操作结果
     */
    Integer enable(EnableRqt rqt);

    /**
     * 删除特殊人群策略
     * 
     * @param rqt 删除请求
     * @return 删除结果
     */
    Integer delete(DeleteRqt rqt);
}
