package com.wanshifu.master.order.push.service.compensateDistribute.impl;

import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.CompensateDistributeApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.AppointTypeEnum;
import com.wanshifu.master.order.push.domain.enums.OrderPushFlagEnum;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.DetailResp;
import com.wanshifu.master.order.push.domain.response.compensateDistribute.ListResp;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.compensateDistribute.CompensateDistributeService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CompensateDistributeServiceImpl implements CompensateDistributeService {


    @Resource
    private CompensateDistributeApi compensateDistributeApi;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private GoodsCommonService goodsCommonService;

//    @Resource
//    private AuthHandler authHandler;

    @Override
    public int create(CreateRqt rqt){
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return compensateDistributeApi.create(rqt);
    }

    @Override
    public int update(UpdateRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return compensateDistributeApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt){
        CompensateDistribute compensateDistribute = compensateDistributeApi.detail(rqt);
        if(compensateDistribute == null){
            return null;
        }
        DetailResp resp = new DetailResp();
        BeanUtils.copyProperties(compensateDistribute,resp);
        return resp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){
        SimplePageInfo<CompensateDistribute> compensateDistributeSimplePageInfo = compensateDistributeApi.list(rqt);
        List<CompensateDistribute> compensateDistributeList = compensateDistributeSimplePageInfo.getList();
        List<Long> updateAccountIds = compensateDistributeList.stream().map(CompensateDistribute::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        //类目名称
        List<Long> goodsIds = compensateDistributeList.stream().map(CompensateDistribute::getCategoryIds)
                .flatMap(it -> Arrays.stream(it.split(","))).distinct()
                .filter(it -> !StringUtils.equals("all", it))
                .map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);

        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(compensateDistributeList, ListResp.class, (d, v) -> {

            InterprectChineseUtil.reflexEnum(v);

            if (StringUtils.equals(d.getCategoryIds(), "all")) {
                v.setCategoryNames("全部(不限类目)");
            } else {
                v.setCategoryNames(Arrays.stream(d.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));
            }

            if (StringUtils.equals(d.getAppointTypes(), "all")) {
                v.setAppointTypes("全部(不限类型)");
            } else {
                v.setAppointTypes(Arrays.stream(d.getAppointTypes().split(",")).map(it -> AppointTypeEnum.asValue(Integer.parseInt(it)).name).collect(Collectors.joining(",")));
            }

            v.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(d.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            v.setOrderPushFlag(OrderPushFlagEnum.asValue(d.getOrderPushFlag()).name);

            v.setCompensateType("none_receive".equals(d.getCompensateType()) ? "规定时间内未接单" : "规定时间内未指派");


            v.setHasPrice(d.getHasPrice() == 1 ? "有价�? : d.getHasPrice() == 2 ? "全部" : "无价�?);

            v.setHasCooperationUser(d.getHasCooperationUser() == 1 ? "有合作商�? : d.getHasCooperationUser() == 2 ? "全部" : "无合作商�?);
        });

        SimplePageInfo<ListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(compensateDistributeSimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(compensateDistributeSimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(compensateDistributeSimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(compensateDistributeSimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;
    }

    @Override
    public Integer delete(DeleteRqt rqt){
        return compensateDistributeApi.delete(rqt);
    }

}
