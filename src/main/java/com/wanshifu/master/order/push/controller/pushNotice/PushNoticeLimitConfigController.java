package com.wanshifu.master.order.push.controller.pushNotice;

import com.wanshifu.master.notice.domains.po.PushNoticeLimitConfig;
import com.wanshifu.master.order.push.domain.request.pushnotice.NoticeLimitConfigUpdateReq;
import com.wanshifu.master.order.push.domain.request.pushnotice.NoticeLimitConfigUpdateRqt;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeLimitConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/21 18:04
 */
@RestController
@RequestMapping("pushNoticeConfig")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushNoticeLimitConfigController {


    @Resource
    private PushNoticeLimitConfigService pushNoticeLimitConfigService;

    /**
     * 根据业务线查询配置，1：用户�?：家�?
     * @param businessLineId
     * @return
     */
    @PostMapping(value = "listByBusinessLineId")
    public List<PushNoticeLimitConfig> listByBusinessLineId(@RequestParam(value = "businessLineId") Integer businessLineId) {
        return pushNoticeLimitConfigService.listByBusinessLineId(businessLineId);
    }

    /**
     * 修改配置
     * @param configList
     */
    @PostMapping(value = "updateByList")
    public void updateByList(@Valid @RequestBody NoticeLimitConfigUpdateReq configList) {
        pushNoticeLimitConfigService.updateByList(configList.getConfigList());
    }
}
