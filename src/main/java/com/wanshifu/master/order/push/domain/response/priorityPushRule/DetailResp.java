package com.wanshifu.master.order.push.domain.response.priorityPushRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.OrderSelectStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.CreateRqt;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroups;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroupsRuleItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 10:44
 */
@Data
public class DetailResp {

    private Integer ruleId;

    private String ruleName;

    private Integer businessLineId;

    private String ruleDesc;

    private String categoryIds;

    private String cityIds;


    /**
     * 规则json
     */
    //private JSONArray ruleList;

    private List<PushGroupsRuleItem> ruleList;

    @Data
    public static class PushGroupsRuleItem {
        private OpenCondition openCondition;
        private PushGroups pushGroups;
    }


    @Data
    public static class OpenCondition {
        private String condition;
        private List<OpenConditionItem> itemList;
    }


    @Data
    public static class OpenConditionItem {
        private String itemName;
        private String term;
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }



    @Data
    public static class PushGroups{

        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;


        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<RuleItem> itemList;


    }


    @Data
    public static class RuleItem{

        private String itemType;


        private String itemTitle;

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("master_group")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;


        private List<TermItem> termList;

        private List<ValueItem> valueList;
    }





}