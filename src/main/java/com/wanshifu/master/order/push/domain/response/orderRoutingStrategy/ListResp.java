package com.wanshifu.master.order.push.domain.response.orderRoutingStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  召回策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     *策略组合id
     */
    private Integer  strategyId;
    /**
     * 策略名称
     */
    private String  strategyName;
    /**
     * 类目名称
     */
    private String  categoryNames;

    /**
     *城市名称
     */
    private String  cityNames;

    private String orderFrom;

    private String orderTag;

    /**
     * 策略状态
     */
    private Integer strategyStatus;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName;

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}