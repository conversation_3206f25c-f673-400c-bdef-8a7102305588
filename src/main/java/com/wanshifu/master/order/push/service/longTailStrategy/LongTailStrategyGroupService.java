package com.wanshifu.master.order.push.service.longTailStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;

/**
 * <AUTHOR>
 */
public interface LongTailStrategyGroupService {
    /**
     * 策略组管�?获取列表
     * @return
     */
    SimplePageInfo<ListResp> list(ListRqt rqt);

    /**
     * 策略组管�?创建长尾单策�?
     * @return
     */
    int create(CreateRqt createRqt);

    /**
     * 策略组管�?修改长尾单策�?
     * @return
     */
    int update(UpdateRqt updateRqt);

    /**
     * 策略组管�?更新策略状态（启用/禁用�?
     * @return
     */
    int updateStatus(EnableRqt enableRqt);

    /**
     * 策略组管�?删除长尾单策�?
     * @return
     */
    int delete(DeleteRqt deleteRqt);

    /**
     * 策略组管�?策略详情
     * @return
     */
    DetailResp detail(DetailRqt detailRqt);
}
