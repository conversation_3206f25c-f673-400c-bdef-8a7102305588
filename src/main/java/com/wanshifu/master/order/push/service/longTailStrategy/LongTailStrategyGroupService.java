package com.wanshifu.master.order.push.service.longTailStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategyGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;

/**
 * <AUTHOR>
 */
public interface LongTailStrategyGroupService {
    /**
     * 策略组管理-获取列表
     * @return
     */
    SimplePageInfo<ListResp> list(ListRqt rqt);

    /**
     * 策略组管理-创建长尾单策略
     * @return
     */
    int create(CreateRqt createRqt);

    /**
     * 策略组管理-修改长尾单策略
     * @return
     */
    int update(UpdateRqt updateRqt);

    /**
     * 策略组管理-更新策略状态（启用/禁用）
     * @return
     */
    int updateStatus(EnableRqt enableRqt);

    /**
     * 策略组管理-删除长尾单策略
     * @return
     */
    int delete(DeleteRqt deleteRqt);

    /**
     * 策略组管理-策略详情
     * @return
     */
    DetailResp detail(DetailRqt detailRqt);
}
