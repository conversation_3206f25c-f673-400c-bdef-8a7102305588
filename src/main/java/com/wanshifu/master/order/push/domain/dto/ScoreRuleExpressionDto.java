package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述 :  评分表达式dto.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-08 16:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoreRuleExpressionDto {

    /**
     * 规则名称
     */
    private String ruleName;

    private String ruleCode;

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;

    /**
     * 匹配项分值表达式
     */
    private String scoreRuleExpression;

    /**
     * 匹配项分值表达式参数
     */
    private String scoreRuleParams;
}