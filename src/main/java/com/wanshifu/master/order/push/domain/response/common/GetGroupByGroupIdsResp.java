package com.wanshifu.master.order.push.domain.response.common;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.AllArgsConstructor;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  批量根据人群id查询人群名称.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-23 17:51
 */
@Data
@AllArgsConstructor
public class GetGroupByGroupIdsResp {

    private Long groupId;
    private String groupName;
}
