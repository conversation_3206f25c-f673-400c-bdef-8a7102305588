package com.wanshifu.master.order.push.service.pushNotice.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.base.push.message.api.PushContentTemplateApi;
import com.wanshifu.base.push.message.domain.dto.BasePushContentTmplDto;
import com.wanshifu.base.push.message.domain.req.TmplQueryReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.notice.domains.enums.PushNoticeChannel;
import com.wanshifu.master.notice.domains.enums.PushNoticeChannelType;
import com.wanshifu.master.notice.domains.enums.PushNoticeGroupType;
import com.wanshifu.master.notice.domains.enums.PushNoticeTarget;
import com.wanshifu.master.notice.domains.po.OrderPushNotice;
import com.wanshifu.master.notice.domains.po.PushNoticeStrategy;
import com.wanshifu.master.notice.domains.po.PushNoticeStrategySnapshot;
import com.wanshifu.master.notice.domains.request.pushNotice.*;
import com.wanshifu.master.notice.service.api.PushNoticeStrategyApi;
import com.wanshifu.master.order.push.api.base.SmsTmplApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.request.common.SmsTemplateRqt;
import com.wanshifu.master.order.push.domain.request.common.SmsTmplListResp;
import com.wanshifu.master.order.push.domain.request.pushnotice.TemplateListRqt;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.ListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.MonitorListResp;
import com.wanshifu.master.order.push.domain.response.pushNotice.TemplateListResp;
import com.wanshifu.master.order.push.domain.vo.pushNotice.OrderNoticeMonitorListExcelVo;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.api.ServeTypeServiceApi;
import com.wanshifu.order.config.domains.po.Goods;
import com.wanshifu.order.config.domains.po.ServeType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PushNoticeStrategyServiceImpl implements PushNoticeStrategyService {
    
    @Resource
    private PushNoticeStrategyApi pushNoticeStrategyApi;

    @Resource
    private IopAccountApi iopAccountApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;

    @Resource
    private AddressCommonService addressCommon;
    @Resource
    private GoodsCommonService goodsCommon;

    @Resource
    private ServeTypeServiceApi serveTypeServiceApi;

    @Override
    public Integer create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyApi.create(rqt);
    }

    @Override
    public Integer update(UpdateRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushNoticeStrategyApi.delete(rqt);
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){
        SimplePageInfo<PushNoticeStrategy> simplePageInfo = pushNoticeStrategyApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PushNoticeStrategy> pushNoticeStrategyList = simplePageInfo.getList();

        List<Long> updateAccountIds = pushNoticeStrategyList.stream().map(PushNoticeStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<ListResp> listResps = BeanCopyUtil.copyListProperties(pushNoticeStrategyList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
            t.setPushNoticeTarget(PushNoticeTarget.asCode(s.getNoticeTarget()).getName());

          String  noticeChannelType = Arrays.stream(JSON.parseObject(s.getPushNoticeMode(), DetailResp.PushNoticeMode.class)
                  .getNoticeChannelType().split(",")).map(it-> PushNoticeChannelType.asCode(it).getName()).collect(Collectors.joining(","));

            t.setNoticeChannelType(noticeChannelType);
            String noticeGroup = Optional.ofNullable(JSON.parseObject(s.getNoticeGroup(), DetailResp.NoticeGroup.class))
                    .map(DetailResp.NoticeGroup::getGroupType).orElse(null);
            String groupType = StringUtils.isNotBlank(noticeGroup) ? PushNoticeGroupType.asCode(noticeGroup).getName() : "";
            t.setGroupType(groupType);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

    @Override
    public SimplePageInfo<MonitorListResp> monitorList(MonitorListRqt rqt) {

        SimplePageInfo<OrderPushNotice> simplePageInfo = pushNoticeStrategyApi.monitorList(rqt);

        SimplePageInfo<MonitorListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderPushNotice> pushNoticeStrategyList = simplePageInfo.getList();

        String divisionIdStr = pushNoticeStrategyList.stream().flatMap(it -> Lists.newArrayList(it.getLv1DivisionId(), it.getLv2DivisionId(),
                        it.getLv3DivisionId(), it.getLv4DivisionId()).stream())
                .distinct().filter(it -> it != 0)
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIds(divisionIdStr);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        //类目名称
        List<Long> goodsIds = pushNoticeStrategyList.stream().map(OrderPushNotice::getCategoryId)
               .distinct()
                .filter(it->it!=0)
                .map(it->Long.parseLong(String.valueOf(it))).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

          String serveTypeIds =  pushNoticeStrategyList.stream().map(OrderPushNotice::getServeType)
                .distinct().filter(it -> it != 0)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        Map<Long, String> serveTypeMap= Maps.newHashMap();
          if(StringUtils.isNotBlank(serveTypeIds)){
              serveTypeMap = serveTypeServiceApi.queryBatch(serveTypeIds).stream().
                      collect(Collectors.toMap(ServeType::getServeTypeId,ServeType::getServeTypeName));
          }

        final Set<Integer> snapshotIds = pushNoticeStrategyList.stream()
                .map(OrderPushNotice::getStrategySnapshotId).collect(Collectors.toSet());
        final QueryRqt queryRqt = new QueryRqt();
        queryRqt.setStrategySnapshotIds(snapshotIds);
        queryRqt.setPageSize(100);
        final SimplePageInfo<PushNoticeStrategySnapshot> PushNoticeStrategySnapshots = pushNoticeStrategyApi.query(queryRqt);
        Map<Integer, String> snapshotNameMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(PushNoticeStrategySnapshots.getList())) {
            snapshotNameMap = PushNoticeStrategySnapshots.getList().stream()
                    .collect(Collectors.toMap(PushNoticeStrategySnapshot::getSnapshotId, PushNoticeStrategySnapshot::getStrategyName));
        }


        Map<Long, String> finalServeTypeMap = serveTypeMap;
        Map<Integer, String> finalSnapshotNameMap = snapshotNameMap;
        List<MonitorListResp> monitorListResps = BeanCopyUtil.copyListProperties(pushNoticeStrategyList, MonitorListResp.class, (s, t) -> {
            t.setDate(s.getNoticeTime());
            t.setBusinessLine(String.valueOf(s.getBusinessLineId()));
            t.setLv1Division(Optional.ofNullable(addressMap.get(s.getLv1DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv2Division(Optional.ofNullable(addressMap.get(s.getLv2DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv3Division(Optional.ofNullable(addressMap.get(s.getLv3DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv4Division(Optional.ofNullable(addressMap.get(s.getLv4DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setCategory(Optional.ofNullable(goodsNameMap.get(Long.parseLong(String.valueOf(s.getCategoryId())))).orElse(""));
            t.setServeType(finalServeTypeMap.getOrDefault(Long.parseLong(String.valueOf(s.getServeType())),""));
            t.setNoticeStrategyCombination(s.getStrategyCombinationName());
            t.setPushNoticeNum(s.getNoticeTargetIds().split(",").length);
            t.setNoticeStrategyName(finalSnapshotNameMap.get(s.getStrategySnapshotId()));
            InterprectChineseUtil.reflexEnum(t);
        });
        simplePageInfoResp.setList(monitorListResps);
        return simplePageInfoResp;
    }

    @Override
    public Integer exportPushNoticeList(MonitorListRqt rqt, HttpServletResponse httpServletResponse) {
        //由于之前做的同步导出使用的poi包是11年前的，现在做异步导出easyExcel的包也需要依赖poi包，且需要高版本的包，高版本的poi包对低版本是不兼容的，导致现在的运力不足明细的导出有问题，
        throw new BusException("暂不支持大数据量同步导出，需要做异步导出！");
//        exportPushNoticeListOld(rqt,httpServletResponse);
//        return 0;
    }


    public Integer exportPushNoticeListOld(MonitorListRqt rqt, HttpServletResponse httpServletResponse) {
        try{
            List<OrderNoticeMonitorListExcelVo> voList = new ArrayList<>();

            int pageNum = 1;
            int pageSize = 10000;
            rqt.setPageSize(pageSize);

            while(true){
                rqt.setPageNum(pageNum);
                SimplePageInfo<OrderPushNotice> simplePageInfo = pushNoticeStrategyApi.monitorList(rqt);
                if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){
                    List<MonitorListResp> respList = buildMonitorListResps(simplePageInfo.getList());
                    respList.forEach(resp -> {
                        OrderNoticeMonitorListExcelVo excelVo = new OrderNoticeMonitorListExcelVo();
                        BeanUtils.copyProperties(resp,excelVo);
                        voList.add(excelVo);
                    });
                    pageNum++;
                }else{
                    break;
                }

//                if(simplePageInfo.getPageNum() >= simplePageInfo.getPages()){
//                    break;
//                }
            }
            if (voList.size()>100000) {
                Assert.isNull(rqt, "最多支持导出10万条数据!");
            }
            String title="触达效果监控";

//            ExcelExportUtil.exportExcel(title,voList,OrderNoticeMonitorListExcelVo.class,httpServletResponse);
            return 1;
        }catch(Exception e){
            e.printStackTrace();
        }

        return 0;
    }

    public Integer exportPushNoticeListMass(MonitorListRqt rqt, HttpServletResponse httpServletResponse) {
        String title="触达效果监控";
        final SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(100);
        try {
            final Sheet sheet = sxssfWorkbook.createSheet(title);
            final Row headerRow = sheet.createRow(0);
            String[] headers=
                    {"日期","业务线","订单编号","城市","区县","街道","服务类目",
                            "服务类型","订单来源","下单模式","命中触达组合","触达渠道","触达师傅人数","触达师傅拉取人数","触达师傅查看人数","触达师傅报价人数"};
            for (int i = 0; i < headers.length; i++) {
                final Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            int rowCount=1;
            int pageNum = 1;
            int pageSize = 10000;
            rqt.setPageSize(pageSize);
            while (true){
                rqt.setPageNum(pageNum);
                SimplePageInfo<OrderPushNotice> simplePageInfo = pushNoticeStrategyApi.monitorList(rqt);
                final List<OrderPushNotice> list = simplePageInfo.getList();
                if(CollectionUtils.isEmpty(list)){
                    break;
                }
                pageNum++;
                List<MonitorListResp> respList = buildMonitorListResps(list);
                for (MonitorListResp monitorListResp : respList) {
                    final Row row = sheet.createRow(rowCount++);

                    final Date date = monitorListResp.getDate();
                    final String businessLine = monitorListResp.getBusinessLine();
                    final String orderNo = monitorListResp.getOrderNo();
                    final String lv2Division = monitorListResp.getLv2Division();
                    final String lv3Division = monitorListResp.getLv3Division();
                    final String lv4Division = monitorListResp.getLv4Division();
                    final String category = monitorListResp.getCategory();
                    final String serveType = monitorListResp.getServeType();
                    final String orderFrom = monitorListResp.getOrderFrom();
                    final String appointType = monitorListResp.getAppointType();
                    final String noticeStrategyCombination = monitorListResp.getNoticeStrategyCombination();
                    final String pushNoticeChannel = monitorListResp.getPushNoticeChannel();
                    final Integer pushNoticeNum = monitorListResp.getPushNoticeNum();
                    final Integer pullMasterNum = monitorListResp.getPullMasterNum();
                    final Integer viewMasterNum = monitorListResp.getViewMasterNum();
                    final Integer offerMasterNum = monitorListResp.getOfferMasterNum();

                    row.createCell(0).setCellValue(date);
                    row.createCell(1).setCellValue(businessLine);
                    row.createCell(2).setCellValue(orderNo);
                    row.createCell(3).setCellValue(lv2Division);
                    row.createCell(4).setCellValue(lv3Division);
                    row.createCell(5).setCellValue(lv4Division);
                    row.createCell(6).setCellValue(category);
                    row.createCell(7).setCellValue(serveType);
                    row.createCell(8).setCellValue(orderFrom);
                    row.createCell(9).setCellValue(appointType);
                    row.createCell(10).setCellValue(noticeStrategyCombination);
                    row.createCell(11).setCellValue(pushNoticeChannel);
                    row.createCell(12).setCellValue(pushNoticeNum);
                    row.createCell(13).setCellValue(pullMasterNum);
                    row.createCell(14).setCellValue(viewMasterNum);
                    row.createCell(15).setCellValue(offerMasterNum);

                }
                ((SXSSFSheet)sheet).flushRows();
            }
            ServletOutputStream outputStream = httpServletResponse.getOutputStream();
            sxssfWorkbook.write(outputStream);
            outputStream.close();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            sxssfWorkbook.dispose();
        }
        return 1;
    }

    private List<MonitorListResp> buildMonitorListResps(List<OrderPushNotice> pushNoticeStrategyList){

        final List<String> divisionList = pushNoticeStrategyList.stream().flatMap(it -> Lists.newArrayList(it.getLv1DivisionId(), it.getLv2DivisionId(),
                        it.getLv3DivisionId(), it.getLv4DivisionId()).stream())
                .distinct().filter(it -> it != 0)
                .map(String::valueOf)
                .collect(Collectors.toList());

        //城市地址
        List<Address> divisionInfoListByDivisionIds = addressCommon.getDivisionInfoListByDivisionIdsMatch(divisionList);
        Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        //类目名称
        List<Long> goodsIds = pushNoticeStrategyList.stream().map(OrderPushNotice::getCategoryId)
                .distinct()
                .filter(it->it!=0)
                .map(it->Long.parseLong(String.valueOf(it))).collect(Collectors.toList());
        List<Goods> goods = goodsCommon.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        String serveTypeIds =  pushNoticeStrategyList.stream().map(OrderPushNotice::getServeType)
                .distinct().filter(it -> it != 0)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        Map<Long, String> serveTypeMap= Maps.newHashMap();
        if(StringUtils.isNotBlank(serveTypeIds)){
            serveTypeMap = serveTypeServiceApi.queryBatch(serveTypeIds).stream().
                    collect(Collectors.toMap(ServeType::getServeTypeId,ServeType::getServeTypeName));
        }

        Map<Long, String> finalServeTypeMap = serveTypeMap;

        final Set<Integer> snapshotIds = pushNoticeStrategyList.stream()
                .map(OrderPushNotice::getStrategySnapshotId).collect(Collectors.toSet());
        final QueryRqt queryRqt = new QueryRqt();
        queryRqt.setStrategySnapshotIds(snapshotIds);
        queryRqt.setPageSize(100);
        final SimplePageInfo<PushNoticeStrategySnapshot> PushNoticeStrategySnapshots = pushNoticeStrategyApi.query(queryRqt);
        Map<Integer, String> snapshotNameMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(PushNoticeStrategySnapshots.getList())) {
            snapshotNameMap = PushNoticeStrategySnapshots.getList().stream()
                    .collect(Collectors.toMap(PushNoticeStrategySnapshot::getSnapshotId, PushNoticeStrategySnapshot::getStrategyName));
        }
        Map<Integer, String> finalSnapshotNameMap = snapshotNameMap;
        List<MonitorListResp> monitorListResps = BeanCopyUtil.copyListProperties(pushNoticeStrategyList, MonitorListResp.class, (s, t) -> {
            t.setDate(s.getNoticeTime());
            t.setBusinessLine(String.valueOf(s.getBusinessLineId()));
            t.setLv1Division(Optional.ofNullable(addressMap.get(s.getLv1DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv2Division(Optional.ofNullable(addressMap.get(s.getLv2DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv3Division(Optional.ofNullable(addressMap.get(s.getLv3DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setLv4Division(Optional.ofNullable(addressMap.get(s.getLv4DivisionId())).map(Address::getDivisionName).orElse(""));
            t.setCategory(Optional.ofNullable(goodsNameMap.get(Long.parseLong(String.valueOf(s.getCategoryId())))).orElse(""));
            t.setServeType(finalServeTypeMap.getOrDefault(Long.parseLong(String.valueOf(s.getServeType())),""));
            t.setNoticeStrategyCombination(s.getStrategyCombinationName());
            t.setPushNoticeNum(s.getNoticeTargetIds().split(",").length);
            t.setNoticeStrategyName(finalSnapshotNameMap.get(s.getStrategySnapshotId()));
            t.setOrderFrom(translateOrderFrom(s.getOrderFrom()));
            t.setAppointType(translateAppointType(s.getAppointType()));
            InterprectChineseUtil.reflexEnum(t);
        });
        return monitorListResps;
    }

    private String translateOrderFrom(String source){
        switch(source){
            case "enterprise_inside":
                return "内部总包";
            case "enterprise_outside":
                return "外部总包";
            case "site":
                return "企业";
            default:
                return "";
        }
    }

    /**
     * 2-发布任务，3-直接雇佣,4-一口价,5-预付款
     * @param source
     * @return
     */
    private String translateAppointType(Integer source){
        switch(source){
            case 2:
                return "报价招标";
            case 3:
                return "直接雇佣";
            case 4:
                return "一口价";
            case 5:
                return "预付款";
            default:
                return "";
        }
    }

    @Override
    public DetailResp detail(StrategyIdRqt rqt){

        PushNoticeStrategy pushNoticeStrategy = pushNoticeStrategyApi.detail(rqt);
        if(pushNoticeStrategy == null){
            return null;
        }

        DetailResp detailResp = new DetailResp();
        detailResp.setStrategyId(pushNoticeStrategy.getStrategyId());
        detailResp.setStrategyName(pushNoticeStrategy.getStrategyName());
        detailResp.setStrategyDesc(pushNoticeStrategy.getStrategyDesc());
        detailResp.setBusinessLineId(pushNoticeStrategy.getBusinessLineId());
        detailResp.setTriggerCondition(JSON.parseObject(pushNoticeStrategy.getTriggerCondition(),DetailResp.TriggerCondition.class));
        detailResp.setPushNoticeMode(JSON.parseObject(pushNoticeStrategy.getPushNoticeMode(),DetailResp.PushNoticeMode.class));
        detailResp.setNoticeGroup(JSON.parseObject(pushNoticeStrategy.getNoticeGroup(),DetailResp.NoticeGroup.class));


        List<DetailResp.TermItem> termItems = Lists.newArrayList(new DetailResp.TermItem("大于等于", ">="), new DetailResp.TermItem("大于", ">"), new DetailResp.TermItem("等于", "="), new DetailResp.TermItem("小于", "<"), new DetailResp.TermItem("小于等于", "<="));


        DetailResp.ItemVo triggerTime = detailResp.getTriggerCondition().getTriggerTime();
        triggerTime.setItemTitle("推单时间距今");
        triggerTime.setTermList(termItems);



        List<DetailResp.ItemVo> triggerParamItemList = detailResp.getTriggerCondition().getTriggerParam().getItemList();


        triggerParamItemList.forEach(itemVo -> {
            String itemTitle = "";
            itemVo.setItemTitle(itemTitle);
            itemVo.setTermList(termItems);
        });


        DetailResp.RuleItem ruleItem = Optional.ofNullable(detailResp.getNoticeGroup())
                .map(DetailResp.NoticeGroup::getAppointMasterGroup)
                .orElse(null);
        if(ruleItem != null && CollectionUtils.isNotEmpty(ruleItem.getItemList())){
           //配置中的师傅人群id
            List<Long> masterGroupIds = ruleItem.getItemList().stream().filter(it -> "master_group".equals(it.getItemName())).map(it -> Long.parseLong(it.getItemValue())).distinct().collect(Collectors.toList());


            Map<Long, String> groupNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(masterGroupIds)) {
                groupNameMap = Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(masterGroupIds))).map(BigdataGetAllGroupListForPageResp::getData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BigdataGetAllGroupListByGroupIdsResp::getGroupId, BigdataGetAllGroupListByGroupIdsResp::getGroupName));
            }

            Map<Long,String> finalGroupNameMap = groupNameMap;


            ruleItem.getItemList().forEach(itemVo -> {
                //师傅人群
                String itemTitle = "师傅人群";
                List<DetailResp.TermItem> groupTermItems = Lists.newArrayList(new DetailResp.TermItem("包含", "in"), new DetailResp.TermItem("不包含", "not_in"));
                List<DetailResp.ValueItem> valueItems = Lists.newArrayList(new DetailResp.ValueItem(finalGroupNameMap.getOrDefault(Long.parseLong(itemVo.getItemValue()), "师傅人群"), itemVo.getItemValue()));
                itemVo.setItemTitle(itemTitle);
                itemVo.setTermList(groupTermItems);
                itemVo.setValueList(valueItems);
                itemVo.setItemType("master_group");
            });
        }


        DetailResp.SelectPushedMasterRule selectPushedMasterRule =  Optional.ofNullable(detailResp.getNoticeGroup())
                .map(DetailResp.NoticeGroup::getSelectPushedMasterRule)
                .orElse(null);

        DetailResp.ItemVo sortRule = selectPushedMasterRule != null ? selectPushedMasterRule.getSortRule() : null;
        if(sortRule != null){
            sortRule.setItemTitle("按照推送师傅的顺序");
            sortRule.setTermList(termItems);
        }

        return detailResp;
    }

    @Resource
    private PushContentTemplateApi pushContentTemplateApi;

    @Override
    public List<TemplateListResp> templateList(TemplateListRqt rqt){

        String pushNoticeChannel = rqt.getPushNoticeChannel();
        if(PushNoticeChannel.PUSH.code.equals(pushNoticeChannel)){
            return getPushTemplateList(rqt.getBusinessLineId());
        }else if(PushNoticeChannel.SMS.code.equals(pushNoticeChannel)){
            return getSmsTemplateList(rqt.getNoticeTarget(),rqt.getBusinessLineId());
        }else if(PushNoticeChannel.VOICE_OUTBOUND_CALL.code.equals(pushNoticeChannel)){
            return getVoiceTemplateList(rqt.getNoticeTarget(),rqt.getBusinessLineId());
        }
        return null;
    }


    private List<TemplateListResp> getPushTemplateList(Integer businessLineId){
        List<TemplateListResp> resultList = new ArrayList<>();
        TmplQueryReq tmplQueryReq = new TmplQueryReq();
        tmplQueryReq.setPlatform(businessLineId == 999 ? 5 : 1);
        List<BasePushContentTmplDto> basePushContentTmplDtoList = pushContentTemplateApi.selectList(tmplQueryReq);
        if(CollectionUtils.isNotEmpty(basePushContentTmplDtoList)){
            basePushContentTmplDtoList.forEach(basePushContentTmplDto -> {
                TemplateListResp resp = new TemplateListResp();
                resp.setTmplId(basePushContentTmplDto.getId());
                resp.setTmplName(basePushContentTmplDto.getTmplName());
                resultList.add(resp);
            });
        }
        return resultList;

    }


    @Resource
    private SmsTmplApi smsTmplApi;


    private List<TemplateListResp> getSmsTemplateList(String noticeTarget,Integer businessLineId){
        List<TemplateListResp> templateListRespList = new ArrayList<>();
        List<TemplateListResp> respList = this.getSmsTemplateList(noticeTarget,businessLineId,1);
        if(CollectionUtils.isNotEmpty(respList)){
            templateListRespList.addAll(respList);
        }

        respList = this.getSmsTemplateList(noticeTarget,businessLineId,3);
        if(CollectionUtils.isNotEmpty(respList)){
            templateListRespList.addAll(respList);
        }
        return templateListRespList;
    }

    private List<TemplateListResp> getSmsTemplateList(String noticeTarget,Integer businessLineId,Integer smsTypeId){
        List<TemplateListResp> resultList = new ArrayList<>();
        TmplQueryReq tmplQueryReq = new TmplQueryReq();
        tmplQueryReq.setPlatform(1);
        SmsTemplateRqt rqt = new SmsTemplateRqt();
        rqt.setSmsTypeId(smsTypeId);
        Integer receiverId = "user".equals(noticeTarget) ? 1 : (businessLineId == 999 ? 10 : 2);
        rqt.setSmsReceiverId(receiverId);
        List<SmsTmplListResp> smsTmplListRespList = smsTmplApi.selectPassTmpl(rqt);
        if(CollectionUtils.isNotEmpty(smsTmplListRespList)){
            smsTmplListRespList.forEach(smsTmplListResp -> {
                TemplateListResp resp = new TemplateListResp();
                resp.setTmplId(smsTmplListResp.getId());
                resp.setTmplName(smsTmplListResp.getTmplNameEn());
                resultList.add(resp);
            });
        }
        return resultList;
    }



    private List<TemplateListResp> getVoiceTemplateList(String noticeTarget,Integer businessLineId){
        List<TemplateListResp> resultList = new ArrayList<>();
        TmplQueryReq tmplQueryReq = new TmplQueryReq();
        tmplQueryReq.setPlatform(1);
        SmsTemplateRqt rqt = new SmsTemplateRqt();
        rqt.setSmsTypeId(4);
        Integer receiverId = "user".equals(noticeTarget) ? 1 : (businessLineId == 999 ? 10 : 2);
        rqt.setSmsReceiverId(receiverId);
        List<SmsTmplListResp> tmplListRespList = smsTmplApi.selectPassVoiceTmpl(rqt);
        if(CollectionUtils.isNotEmpty(tmplListRespList)){
            tmplListRespList.forEach(template -> {
                TemplateListResp resp = new TemplateListResp();
                resp.setTmplId(template.getId());
                resp.setTmplName(template.getTmplNameEn());
                resultList.add(resp);
            });
        }
        return resultList;
    }



}
