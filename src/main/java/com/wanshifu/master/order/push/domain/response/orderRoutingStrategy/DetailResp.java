package com.wanshifu.master.order.push.domain.response.orderRoutingStrategy;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;

/**
 * 描述 :  召回策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {


    private Integer strategyId;

    private Integer businessLineId;

    private Integer strategyStatus;

    private String strategyName;

    private String strategyDesc;

    private String orderFrom;

    private String orderTag;

    private String categoryIds;

    private String openCityMode;

    private String cityIds;

    private Integer matchRoutingId;

    private String matchRoutingName;

}