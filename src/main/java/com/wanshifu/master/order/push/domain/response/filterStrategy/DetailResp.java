package com.wanshifu.master.order.push.domain.response.filterStrategy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  召回策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    /**
     * 策略id
     */
    private Integer strategyId;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 类目id，多个以逗号拼接
     */
    private String categoryIds;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 策略状�?
     */
    private Integer strategyStatus;

    private String orderFlag;

    /**
     * 规则json
     */
    //private JSONArray ruleList;

    private List<RuleItem> ruleList;

    @Data
    public static class RuleItem {
        private String ruleName;
        private OpenCondition openCondition;
        private FilterRule filterRule;
    }

    @Data
    public static class FilterRule {
        private String condition;
        private List<FilterRuleItem> itemList;
    }
    @Data
    public static class OpenCondition {
        private String condition;
        private List<OpenConditionItem> itemList;
    }

    @Data
    public static class FilterRuleItem {
        private String itemType;
        private String itemName;
        private String term;
        private String itemValue;

        private String itemTitle;
        private List<TermItem> termList;
        private List<ValueItem> valueList;

    }

    @Data
    public static class OpenConditionItem {
        private String itemName;
        private String term;
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",�?:家具送货到楼下”]
         */
        private List<String> serveInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }
}
