package com.wanshifu.master.order.push.domain.request.pushnotice;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/21 18:33
 */
@Data
public class NoticeLimitConfigUpdateRqt {

    /**
     * 业务�?
     * 1�?用户
     * 2�?家庭
     */
    @NotNull
    @Min(1)
    private Integer businessLineId;

    /**
     * PushNoticeChannel
     * 触达渠道，push：push通知，sms:短信，inbox_message：站内信，wechat_official_accounts：微信公众号，voice_outbound_call：语音外�?
     */
    @NotEmpty
    @ValueIn("push,sms,inbox_message,wechat_official_accounts,voice_outbound_call")
    private String pushNoticeChannel;

    /**
     * 限制次数 默认1,最�?
     */
    @NotNull
    @Min(1)
    @Max(2)
    private Integer limitTimes;
}
