package com.wanshifu.master.order.push.domain.response.agreementOrderDistributeStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 10:44
 */
@Data
public class DetailResp {

    private Integer strategyId;

    private Integer businessLineId;

    private String strategyName;

    private String strategyDesc;

    private String categoryIds;

    private String cityIds;

    private List<DistributeStrategyItem> distributeStrategyList;


    private List<MatchRoutingRule> compensateDistributeStrategyList;


    private List<DetailResp.CompensateDistributeVo> compensateDistributeList;



    @Data
    public static class DistributeStrategyItem{

        /**
         * 开启条件
         */
        @NotNull
        @Valid
        private OpenCondition openCondition;

        /**
         * 触达策略id
         */
        @NotNull
        private Integer selectStrategyId;


        private String selectStrategyName;

        private Integer scoreStrategyId;


        private String scoreStrategyName;


        @NotBlank
        private String distributeRule;

        @NotNull
        private Integer distributeNum;


    }



    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        private String condition;

        /**
         * 规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem {

        /**
         * 规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;

    }


    @Data
    public static class MatchRoutingRule{

        /**
         *规则项
         */
        private RoutingItem item;


        private Integer matchRoutingId;

        private String matchRoutingName;
    }

    @Data
    public static class RoutingItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("none_receive,none_appoint")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

    }


    @Data
    public static class CompensateDistributeVo{

        private Integer compensateDistributeId;

        private String compensateDistributeName;

        private Integer orderRoutingStrategyId;

        private String orderRoutingStrategyName;


    }

}