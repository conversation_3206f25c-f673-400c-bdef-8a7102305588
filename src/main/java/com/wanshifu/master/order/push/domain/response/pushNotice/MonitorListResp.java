package com.wanshifu.master.order.push.domain.response.pushNotice;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.BusinessLineEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import com.wanshifu.master.order.push.domain.enums.PushNoticeChannelEnum;
import lombok.Data;

import java.util.Date;

@Data
public class MonitorListResp {


    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date date;
    @TranslateEnum(enumClass= BusinessLineEnum.class)
    private String businessLine;
    private String orderNo;
    private String lv1Division;
    private String lv2Division;
    private String lv3Division;
    private String lv4Division;
    private String category;
    private String serveType;
    private String appointType;
    private String orderFrom;
    private String noticeStrategyCombination;
    @TranslateEnum(enumClass= PushNoticeChannelEnum.class)
    private String pushNoticeChannel;
    private String noticeStrategyName;
    private Integer pushNoticeNum;
    private Integer pullMasterNum = 0;
    private Integer viewMasterNum = 0;
    private Integer offerMasterNum = 0;

}
