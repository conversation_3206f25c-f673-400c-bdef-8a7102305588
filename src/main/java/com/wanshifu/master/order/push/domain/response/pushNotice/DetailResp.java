package com.wanshifu.master.order.push.domain.response.pushNotice;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.notice.domains.request.pushNotice.CreateRqt;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class DetailResp {

    private Integer strategyId;

    private Integer businessLineId;

    private String strategyName;

    private String strategyDesc;

    private DetailResp.TriggerCondition triggerCondition;

    private DetailResp.PushNoticeMode pushNoticeMode;

    private DetailResp.NoticeGroup noticeGroup;


    private Long createAccountId;

    @Data
    public static class TriggerCondition{

        private DetailResp.ItemVo triggerTime;

        private DetailResp.RuleItem triggerParam;
    }


    @Data
    public static class RuleItem{

        private String condition;

        private List<DetailResp.ItemVo> itemList;

    }

    @Data
    public static class ItemVo{

        private String itemName;

        private String term;

        private String itemValue;

        private String itemType;

        private String itemTitle;

        private List<DetailResp.TermItem> termList;

        private List<DetailResp.ValueItem> valueList;


        private String startValue;

        private String endValue;

    }

    @Data
    public static class PushNoticeMode{

        private String noticeTarget;

        private String noticeChannelType;

        private String noticeChannel;

        private List<NoticeOrderLabelItem> noticeOrderLabelList;

        private SendTemplate smsTemplate;

        private SendLimit smsSendLimit;


        private SendTemplate imsTemplate;

        private SendLimit imsSendLimit;

        private SendTemplate pushTemplate;

        private SendLimit pushSendLimit;

        private SendTemplate voiceOutboundCallTemplate;

        private SendLimit voiceOutboundCallLimit;




    }

    @Data
    public static class SendLimit{
        private Integer open;
        private Integer normal;
        private Integer definitePrice;
        private Integer advancePay;
    }

    @Data
    public static class SendTemplate{
        private String open;
        private String normal;
        private String definitePrice;
        private String advancePay;
    }

    @Data
    public static class NoticeOrderLabelItem{

        private String label;

        private RuleItem removeRule;
    }

    @Data
    public static class NoticeGroup{

        private String groupType;

        private DetailResp.SelectPushedMasterRule selectPushedMasterRule;

        private RuleItem appointMasterGroup;

        private DetailResp.ExcludeMasterRule excludeMasterRule;
    }


    @Data
    public static class SelectPushedMasterRule{

        private String selectMasterMode;

        private DetailResp.ItemVo sortRule;

        private RuleItem selectRule;
    }


    @Data
    public static class ExcludeMasterRule{

        private Integer isExcludePulledMaster;

        private Integer isExcludeViewedMaster;

        private Integer isExcludeOfferPriceMaster;

        private Integer isExcludeRestMaster;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TermItem{
        private String termName;
        private String term;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ValueItem{
        private String name;
        private String code;
    }
}
