package com.wanshifu.master.order.push.controller.repushPolicy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.repushPolicy.DetailResp;
import com.wanshifu.master.order.push.domain.response.repushPolicy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.repushPolicy.*;
import com.wanshifu.master.order.push.service.repushPolicy.RepushPolicyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * 描述 :  重推.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 16:45
 */
@RestController
@RequestMapping("/repushPolicy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RepushPolicyController {

    private final RepushPolicyService repushPolicyService;


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return repushPolicyService.create(rqt);
    }

    /**
     * 修改重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return repushPolicyService.update(rqt);
    }

    /**
     * 重推策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    public DetailResp detail(@RequestBody @Valid DetailRqt rqt) {
        return repushPolicyService.detail(rqt);
    }


    /**
     * 启用/禁用重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return repushPolicyService.enable(rqt);
    }

    /**
     * 重推机制列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    public SimplePageInfo<ListResp> list(@RequestBody @Valid ListRqt rqt) {
        return repushPolicyService.list(rqt);
    }

    /**
     * 删除重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return repushPolicyService.delete(rqt);
    }
}