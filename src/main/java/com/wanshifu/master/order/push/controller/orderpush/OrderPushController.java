package com.wanshifu.master.order.push.controller.orderpush;

import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushPushNumberRqt;
import com.wanshifu.master.order.push.domain.request.orderpush.OrderPushRecordDetailRqt;
import com.wanshifu.master.order.push.domain.vo.orderpush.InfoOrderPushRecordDetailVo;
import com.wanshifu.master.order.push.domain.vo.orderpush.OrderPushRecordDetailVo;
import com.wanshifu.master.order.push.service.orderpush.OrderPushService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18 11:06
 */
@RestController
@Validated
@RequestMapping("/orderPush")
public class OrderPushController {

    @Resource
    private OrderPushService orderPushService;

    /**
     * ocs推单记录
     * @param rqt
     * @return
     */
    @PostMapping("/listOrderPushRecordDetail")
    public List<OrderPushRecordDetailVo> listOrderPushRecordDetail(@Valid @RequestBody OrderPushRecordDetailRqt rqt) {
        return orderPushService.listOrderPushRecordDetail(rqt);
    }

    /**
     * ocs信息订单推单记录
     * @param rqt
     * @return
     */
    @PostMapping("/listInfoOrderPushRecordDetail")
    public List<InfoOrderPushRecordDetailVo> listInfoOrderPushRecordDetail(@Valid @RequestBody OrderPushRecordDetailRqt rqt) {
        return orderPushService.listInfoOrderPushRecordDetail(rqt);
    }


    /**
     * ocs获取师傅推单数量
     * @param rqt
     * @return
     */
    @PostMapping("/getOrderPushPushNumber")
    public int getOrderPushPushNumber(@Valid @RequestBody OrderPushPushNumberRqt rqt) {
        return orderPushService.getOrderPushPushNumber(rqt);
    }
}
