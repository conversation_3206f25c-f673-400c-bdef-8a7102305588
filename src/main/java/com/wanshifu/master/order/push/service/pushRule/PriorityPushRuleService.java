package com.wanshifu.master.order.push.service.pushRule;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.response.priorityPushRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.priorityPushRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface PriorityPushRuleService {


    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer enable(EnableRqt rqt);


    Integer delete(DeleteRqt rqt);




}