package com.wanshifu.master.order.push.controller.simulatePush;


import com.wanshifu.framework.core.BusException;
import com.wanshifu.master.order.push.domain.request.simulatePush.ExportSimulatePushRqt;
import com.wanshifu.master.order.push.domain.request.simulatePush.SimulateRqt;
import com.wanshifu.master.order.push.service.simulatePush.OrderSimulatePushService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/simulatePush")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SimulatePushController {

    @Resource
    private OrderSimulatePushService orderSimulatePushService;


    @PostMapping("/simulate")
    public void simulate(@RequestBody @Valid SimulateRqt simulateRqt){
        orderSimulatePushService.simulate(simulateRqt);
    }


    /**
     * 导出测算记录
     * @param
     * @return
     */
    @GetMapping("/exportSimulatePush")
    public void exportSimulatePush(@Valid ExportSimulatePushRqt exportSimulatePushRqt, HttpServletResponse httpServletResponse) throws Exception{
        //  TODO: 2020/9/23 暂不支持大数据量同步导出，需要做异步导出！
        //由于之前做的同步导出使用的poi包是11年前的，现在做异步导出easyExcel的包也需要依赖poi包，且需要高版本的包，高版本的poi包对低版本是不兼容的，导致现在的运力不足明细的导出有问题，
        throw new BusException("暂不支持大数据量同步导出，需要做异步导出！");
    }


}