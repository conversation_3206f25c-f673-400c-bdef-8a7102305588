package com.wanshifu.master.order.push.controller.orderMatchRouting;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderMatchRoutingApi;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.CreateRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.ListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.UpdateRqt;
import com.wanshifu.master.order.push.service.orderMatchRouting.OrderMatchRoutingService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * Title�?
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRouting")
public class OrderMatchRoutingController {

    @Resource
    private OrderMatchRoutingService orderMatchRoutingService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return orderMatchRoutingService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return orderMatchRoutingService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return orderMatchRoutingService.detail(rqt);
    }



    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt){
        return orderMatchRoutingService.list(rqt);
    }


}
