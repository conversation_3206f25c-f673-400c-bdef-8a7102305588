package com.wanshifu.master.order.push.domain.response.orderSortingStrategy;

import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.domain.vo.orderSortingStrategy.ExposureSortRule;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 描述 :  订单排序策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 城市id,多个以,拼接
     */
    private String cityIds;

    /**
     * 类目id,多个以,拼接
     */
    private String categoryIds;

    /**
     * 策略状态，1：启用，0：禁用
     */
    private Integer strategyStatus;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createAccountId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private Long updateAccountId;

    /**
     * 是否实验， 1：是，0：否
     */
    private Integer testFlag;

    /**
     * 实验组策略名
     */
    private List<String> testGroupNameList;

    /**
     * 实验类别， 1：按师傅分组，2：按订单分组
     */
    private Integer testType;


    /**
     * 排序规则
     */
    private SortRule sortRule;

    /**
     * 干预排序规则
     */
    private ExposureSortRule exposureSortRule;

    @Data
    public static class SortRule {
        private SortRuleItemVo generalRule;
        private DifferenceRuleVo differenceRule;
        private SortRuleItemVo specialRule;

    }

    @Data
    public static class SortRuleItemVo {
        private BigDecimal weight;
        private List<RuleItem> itemList;
    }

    @Data
    public static class DifferenceRuleVo {
        private BigDecimal weight;
        private List<SortRuleItem> differenceRuleList;
    }

    @Data
    public static class SortRuleItem {
        /**
         * 开启条件
         */
        private OpenCondition openCondition;

        /**
         * 匹配项集合
         */
        private List<RuleItem> itemList;
    }

    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        private String condition;

        /**
         * 规则项
         */
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem {

        /**
         * 规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;
    }
}