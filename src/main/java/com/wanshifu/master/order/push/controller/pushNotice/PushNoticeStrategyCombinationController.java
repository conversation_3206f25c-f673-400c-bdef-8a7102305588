package com.wanshifu.master.order.push.controller.pushNotice;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.notice.domains.request.pushNoticeCombination.*;
import com.wanshifu.master.order.push.domain.request.pushnotice.AbTestGroupInfoRqt;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.AbTestGroupInfoResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushNoticeCombination.ListResp;
import com.wanshifu.master.order.push.service.pushNotice.PushNoticeStrategyCombinationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("pushNoticeStrategyCombination")
public class PushNoticeStrategyCombinationController {

    @Resource
    private PushNoticeStrategyCombinationService pushNoticeStrategyCombinationService;

    /**
     * 新增策略
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return pushNoticeStrategyCombinationService.create(rqt);
    }


    /**
     * 更新策略
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return pushNoticeStrategyCombinationService.update(rqt);
    }

    /**
     * 启用/禁用策略
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableRqt rqt) {
        return pushNoticeStrategyCombinationService.enable(rqt);
    }

    /**
     * 删除策略
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteRqt rqt) {
        return pushNoticeStrategyCombinationService.delete(rqt);
    }


    /**
     * 策略详情
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return pushNoticeStrategyCombinationService.detail(rqt);
    }


    /**
     * 策略组合列表
     *
     * @param rqt
     * @return
     */
    @PostMapping(value = "list")
    public SimplePageInfo<ListResp> list(@Valid @RequestBody ListRqt rqt) {
        return pushNoticeStrategyCombinationService.list(rqt);
    }

    /**
     * 根据实验id获取大数据abtest实验组别列表
     * @param rqt
     * @return
     */
    @PostMapping(value = "listAbTestGroupInfoByTestId")
    public List<AbTestGroupInfoResp> listAbTestGroupInfoByTestId(@Valid @RequestBody AbTestGroupInfoRqt rqt) {
        return pushNoticeStrategyCombinationService.listAbTestGroupInfoByTestId(rqt);
    }

}
