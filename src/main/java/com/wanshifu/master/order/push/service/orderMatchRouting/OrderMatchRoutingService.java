package com.wanshifu.master.order.push.service.orderMatchRouting;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.DetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRouting.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.*;

public interface OrderMatchRoutingService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);


    SimplePageInfo<ListResp> list(ListRqt rqt);


    }
