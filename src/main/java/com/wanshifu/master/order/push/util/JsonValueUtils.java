package com.wanshifu.master.order.push.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Objects;
import java.util.stream.Collectors;

public class JsonValueUtils {

    /**
     * 从JSON数组字符串中提取所有value值，用逗号分割返回
     */
    public static String extractAllValues(String jsonArrayString) {
        if (jsonArrayString == null || jsonArrayString.trim().isEmpty()) {
            return "";
        }

        try {
            JSONArray jsonArray = JSON.parseArray(jsonArrayString);
            return jsonArray.stream()
                    .map(obj -> (JSONObject) obj)
                    .flatMap(jsonObj -> jsonObj.values().stream())
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
        }
    }
}
