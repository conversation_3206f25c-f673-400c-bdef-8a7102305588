package com.wanshifu.master.order.push.service.pushRule;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.response.pushRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface PushRuleService {


    Integer create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    DetailResp detail(DetailRqt rqt);


    SimplePageInfo<ListResp> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);




}
