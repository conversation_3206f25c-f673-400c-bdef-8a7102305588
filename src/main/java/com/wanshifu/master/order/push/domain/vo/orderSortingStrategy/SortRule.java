package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  排序规则.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-28 15:44
 */
@Data
public class SortRule {

    /**
     * 通用项规则
     */
    @NotNull
    @Valid
    private SortRuleItemVo generalRule;


    /**
     * 订单差异项规则
     */
    @NotNull
    @Valid
    private DifferenceRuleVo differenceRule;


    /**
     * 特殊项规则
     */
    @Deprecated
    private SortRuleItemVo specialRule;

//    /**
//     * 特殊项规则类目
//     */
//    @NotNull
//    @Valid
//    private SortRuleCategoryItemVo specialCategoryRule;

}