package com.wanshifu.master.order.push.domain.vo.orderSortingStrategy;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  排序规则.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-28 15:44
 */
@Data
public class SortRule {

    /**
     * 通用项规�?
     */
    @NotNull
    @Valid
    private SortRuleItemVo generalRule;


    /**
     * 订单差异项规�?
     */
    @NotNull
    @Valid
    private DifferenceRuleVo differenceRule;


    /**
     * 特殊项规�?
     */
    @Deprecated
    private SortRuleItemVo specialRule;

//    /**
//     * 特殊项规则类�?
//     */
//    @NotNull
//    @Valid
//    private SortRuleCategoryItemVo specialCategoryRule;

}
