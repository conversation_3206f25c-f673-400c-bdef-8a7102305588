package com.wanshifu.master.order.push.export.strategy;

import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.export.convert.PushMatchLogExportConvert;

import java.io.File;
import java.rmi.server.ExportException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 17:30
 */
public interface PushMatchLogExportStrategy<T, R> {

    /**
     * 分页查询数据
     *
     * @param pageNum
     * @return
     */
    List<T> queryData(PushMatchLogRqt pushMatchLogRqt, int pageNum);

    /**
     * 获取数据转换器
     * @return
     */
    PushMatchLogExportConvert<T, R> getConverter();

    /**
     * 导出且上传七牛云
     *
     * @param pushMatchLogRqt
     * @param styleStrategy
     */
    FileUploadResp exportAndUpload(PushMatchLogRqt pushMatchLogRqt, HorizontalCellStyleStrategy styleStrategy) throws ExportException;

    /**
     * 获取当前导出策略类型
     * @return
     */
    String getExportType();
}
