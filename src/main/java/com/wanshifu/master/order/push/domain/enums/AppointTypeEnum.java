package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 指派类型
 * <AUTHOR>
 * @date 2023-07-12
 */
public enum AppointTypeEnum {

    /**
     * 发布任务
     */
    OPEN(2,"公开报价"),

    /**
     * 直接雇佣
     */
    NORMAL(3,"直接雇佣"),

    /**
     * 一口价
     */
    DEFINITE_PRICE(4,"一口价"),

    /**
     * 预付款
     */
    ADVANCE_PAY(5,"预付款");

    public final Integer value;

    public final String name;

    AppointTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static final Map<Integer, AppointTypeEnum> valueMapping = new HashMap<>((int) (AppointTypeEnum.values().length / 0.75));

    static {
        for (AppointTypeEnum instance : AppointTypeEnum.values()) {
            valueMapping.put(instance.value, instance);
        }
    }

    public static AppointTypeEnum asValue(Integer value) {
        return valueMapping.get(value);
    }

    public static Boolean isInclude(Integer value) {
        if(Objects.isNull(value)) {
            return false;
        }
        for (AppointTypeEnum appointType : valueMapping.values()) {
            if(appointType.value.equals(value)) {
                return true;
            }
        }
        return false;
    }

}
