package com.wanshifu.master.order.push.util;

import cn.hutool.core.bean.BeanUtil;
import com.wanshifu.framework.UtilsRuntimeException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * 描述 :  扩展对象属性copy工具类.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2022-04-01 14:32
 */
@Slf4j
public class BeanCopyUtil extends DataUtils {

    /**
     * 带回调函数的集合数据的拷贝（可自定义字段拷贝规则）
     *
     * @param sources:  数据源类
     * @param clazz:    目标类class
     * @param callBack: 回调函数
     * @return list
     */
    public static <S, T> List<T> copyListProperties(List<S> sources, Class<T> clazz, BiConsumer<S, T> callBack) {
        if(CollectionUtils.isEmpty(sources)){
            return Collections.emptyList();
        }
        List<T> list = new ArrayList<>(sources.size());
        for (S source : sources) {
            try {
                T t = clazz.newInstance();
                BeanUtil.copyProperties(source, t);
                list.add(t);
                if (callBack != null) {
                    // 回调
                    callBack.accept(source, t);
                }
            } catch (Exception e) {
                log.error("copyListProperties error",e);
                throw new UtilsRuntimeException();
            }

        }
        return list;
    }
}