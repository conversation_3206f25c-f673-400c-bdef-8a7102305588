package com.wanshifu.master.order.push.controller.pushmatchlog;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.request.pushmatchlog.PushMatchLogExportRqt;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.service.pushmatchlog.PushMatchLogService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @description 推单匹配日志
 * @date 2025/4/25 20:24
 */
@RestController
@RequestMapping("/pushMatchLog")
public class PushMatchLogController {

    @Resource
    private PushMatchLogService pushMatchLogService;


    /**
     * 平台协议派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("userAgreementList")
    public SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.userAgreementPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 总包直接指派日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("enterpriseAppointList")
    public SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.enterpriseAppointPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 合作经营派单日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("cooperationBusinessList")
    public SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.cooperationBusinessPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 样板城市派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("newModelCityList")
    public SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.newModelCityPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 导出推单匹配日志
     * @param rqt
     * @return
     */
    @PostMapping("/exportPushMatchLogList")
    public Integer exportPushMatchLogList(@Valid @RequestBody PushMatchLogExportRqt rqt) {
        return pushMatchLogService.exportPushMatchLogList(rqt);
    }

    /**
     * 平台协议派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("fullTimeMatchLogList")
    public SimplePageInfo<OrderFullTimeMasterMatchLogDto> fullTimeMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.fullTimeMatchLogList(pushMatchLogRqt);
    }

}
