package com.wanshifu.master.order.push.domain.request.pushnotice;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/22 15:26
 */
@Data
public class NoticeLimitConfigUpdateReq {

    @NotEmpty
    @Size(min = 5,max = 5)
    @Valid
    private List<NoticeLimitConfigUpdateRqt> configList;

}
