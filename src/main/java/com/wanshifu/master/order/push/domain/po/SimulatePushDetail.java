package com.wanshifu.master.order.push.domain.po;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 
 */
@Data
@ToString
@Table(name = "simulate_push_detail")
public class SimulatePushDetail {

    /**
     * 测算推单明细id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "detail_id")
    private Long detailId;

    /**
     * 测算推单id
     */
    @Column(name = "simulate_push_id")
    private Long simulatePushId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 推送轮数
     */
    @Column(name = "push_round")
    private Integer pushRound;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}