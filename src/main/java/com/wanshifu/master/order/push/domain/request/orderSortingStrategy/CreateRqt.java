package com.wanshifu.master.order.push.domain.request.orderSortingStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.orderSortingStrategy.ExposureSortRule;
import com.wanshifu.master.order.push.domain.vo.orderSortingStrategy.SortRule;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 描述 :  创建订单排序策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-03 10:27
 */
@Data
public class CreateRqt {


    /**
     * 策略名称
     */
    @NotEmpty
    private String strategyName;


    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 城市id,多个用逗号拼接
     */
    @NotEmpty
    private String cityIds;

    /**
     * 类目id,多个用逗号拼接
     */
    @NotEmpty
    private String categoryIds;

    /**
     * 排序规则
     */
    @NotNull
    @Valid
    private SortRule sortRule;

    /**
     * 干预排序规则
     */
    @Valid
    private ExposureSortRule exposureSortRule;

    /**
     * 是否实验， 1：是，0：否
     */
    @NotNull
    private Integer testFlag;

    /**
     * 实验组策略名
     */
    private List<String> testGroupNameList;

    /**
     * 实验类别， 1：按师傅分组，2：按订单分组
     */
    @ValueIn("1,2")
    private Integer testType;

}