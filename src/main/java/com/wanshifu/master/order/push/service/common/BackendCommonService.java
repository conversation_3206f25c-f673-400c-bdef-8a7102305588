package com.wanshifu.master.order.push.service.common;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.request.common.GetGroupByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GetServeByServeIdsReq;
import com.wanshifu.master.order.push.domain.request.common.GroupListReq;
import com.wanshifu.master.order.push.domain.request.common.MasterQuotaReq;
import com.wanshifu.master.order.push.domain.request.common.ServeListReq;
import com.wanshifu.master.order.push.domain.request.common.ServeReq;
import com.wanshifu.master.order.push.domain.response.common.*;
import com.wanshifu.master.order.sort.domains.api.response.common.OrderSortItemListResp;

import java.util.List;

/**
 * 描述 :  公共服务类接口.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-20 14:41
 */
public interface BackendCommonService {

    SimplePageInfo<UserGroupListResp> userGroupList(GroupListReq rqt);

    SimplePageInfo<UserGroupListResp> masterGroupList(GroupListReq rqt);

    SimplePageInfo<MasterQuotaResp> masterQuota(MasterQuotaReq rqt);

    List<MasterItemListResp> masterItemList(String itemName);

    List<CategoryListResp> categoryList(Integer businessLineId);

    List<ServeListResp> serveList(ServeListReq rqt);

    List<ServeResp> serve(ServeReq rqt);

    List<GetGroupByGroupIdsResp> getGroupByGroupIds(GetGroupByGroupIdsReq rqt);

    List<GetServeByServeIdsResp> getServeByServeIds(GetServeByServeIdsReq rqt);

    List<GetGoodsListResp> getGoodsList(Integer businessLineId);

    List<GetServeTypeListResp> getServeTypeList(Long businessLineId,Long categoryId);

    List<GetServeTypeListResp> batchGetServeTypeList(Long businessLineId,String categoryIds);

    List<OrderSortItemListResp> orderSortItemList(String itemName);

}