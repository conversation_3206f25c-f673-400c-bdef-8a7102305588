package com.wanshifu.master.order.push.service.longTailStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.response.longTailStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.response.longTailStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;

/**
 * <AUTHOR>
 */
public interface LongTailStrategyService {
    /**
     * 策略管理-获取列表
     * @return
     */
    SimplePageInfo<ListResp> list(ListRqt rqt);

    /**
     * 策略管理-创建长尾单策略
     * @return
     */
    int create(CreateRqt createRqt);

    /**
     * 策略管理-修改长尾单策略
     * @return
     */
    int update(UpdateRqt updateRqt);

    /**
     * 策略管理-更新策略状态（启用/禁用）
     * @return
     */
    int updateStatus(EnableRqt enableRqt);

    /**
     * 策略管理-删除长尾单策略
     * @return
     */
    int delete(DeleteRqt deleteRqt);

    /**
     * 策略管理-策略详情
     * @return
     */
    DetailResp detail(DetailRqt detailRqt);
}
