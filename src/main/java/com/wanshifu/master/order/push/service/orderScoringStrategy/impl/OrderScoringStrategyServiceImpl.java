package com.wanshifu.master.order.push.service.orderScoringStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.OrderScoringStrategyApi;
import com.wanshifu.master.order.push.api.bigdata.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.ItemTypeEnum;
import com.wanshifu.master.order.push.domain.enums.MasterResourcesEnum;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.request.common.BigdataGetAllGroupListByGroupIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.response.common.BigdataGetAllGroupListForPageResp;
import com.wanshifu.master.order.push.domain.response.common.GetGroupByGroupIdsResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.GetOrderScoringStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderScoringStrategy.OrderScoringStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.common.GoodsCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.orderScoringStrategy.OrderScoringStrategyService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderScoringStrategyServiceImpl implements OrderScoringStrategyService {

    @Resource
    private OrderScoringStrategyApi orderScoringStrategyApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private AddressCommonService addressCommonService;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;

    @Resource
    private ServeCommonService serveCommonService;


    @Override
    public Integer create(CreateOrderScoringStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setCreateAccountId(loginUserId);
        return orderScoringStrategyApi.create(rqt);
    }

    @Override
    public Integer update(UpdateOrderScoringStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderScoringStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableOrderScoringStrategyRqt rqt){
        Long loginUserId = UserInfoUtils.getCurrentLoginAccountId();
        rqt.setUpdateAccountId(loginUserId);
        return orderScoringStrategyApi.enable(rqt);
    }

    @Override
    public OrderScoringStrategyDetailResp detail(OrderScoringStrategyDetailRqt rqt){
        OrderScoringStrategy orderScoringStrategy = orderScoringStrategyApi.detail(rqt);
        OrderScoringStrategyDetailResp detailResp = new OrderScoringStrategyDetailResp();
        BeanCopyUtil.copyProperties(orderScoringStrategy, detailResp);
        List<OrderScoringStrategyDetailResp.ScoreRule> scoreRuleList = JSON.parseArray(orderScoringStrategy.getScoringStrategy(), OrderScoringStrategyDetailResp.ScoreRule.class);

        List<RuleItem> ruleItemList = new ArrayList<>();
        scoreRuleList.forEach(sortRule -> ruleItemList.addAll(sortRule.getItemList()));
        ruleItemList.forEach(ruleItem -> {
            if(StringUtils.isBlank(ruleItem.getItemType())){
                ruleItem.setItemType(ItemTypeEnum.MASTER_QUOTA.getCode());
            }
        });

        detailResp.setRuleList(scoreRuleList);

        List<RuleItem> masterGroupItemList = ruleItemList.stream().filter(ruleItem -> ruleItem.getItemType().equals(ItemTypeEnum.MASTER_GROUP.getCode())).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(masterGroupItemList)){
            List<RuleItem.ScoreItem> scoreItemList = new ArrayList<>();
            masterGroupItemList.forEach(ruleItem -> scoreItemList.addAll(ruleItem.getScoreList()));
            List<Long> groupIdList =  scoreItemList.stream().map(RuleItem.ScoreItem::getValue).map(Long::parseLong).collect(Collectors.toList());
            detailResp.setMasterGroupList(Optional.ofNullable(masterBigDataOpenApi.getAllGroupListByGroupIds(new BigdataGetAllGroupListByGroupIdsReq(groupIdList, 2)))
                    .map(BigdataGetAllGroupListForPageResp::getData)
                    .orElse(Collections.emptyList()).stream().
                            map(it -> new GetGroupByGroupIdsResp(it.getGroupId(), it.getGroupName())).collect(Collectors.toList()));
        }

        Set<Long> serveIds = scoreRuleList.stream().flatMap(it -> it.getOpenCondition().getItemList().stream())
                .filter(it->CollectionUtils.isNotEmpty(it.getServeIdList()))
                .flatMap(it -> it.getServeIdList().stream())
                .collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> serveInfoMap =Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(serveIds)){
            serveInfoMap = serveCommonService.getServeBaseInfoByServeIdSet(serveIds).stream()
                    .collect(Collectors.toMap(ServeBaseInfoResp::getServeId, it -> it.getServeId() + ":" + it.getName()));
        }
        Map<Long, String> finalServeInfoMap = serveInfoMap;
        scoreRuleList.forEach(scoreRule -> Optional.ofNullable(scoreRule.getOpenCondition().getItemList()).ifPresent(it -> it.forEach(item -> {

            List<List<Long>> serveIdListList = item.getServeIdList();
            if(CollectionUtils.isNotEmpty(serveIdListList)){
                List<Long> serveIdList = serveIdListList.stream().flatMap(Collection::stream).collect(Collectors.toList());
                item.setServeInfoList( serveIdList.stream().map(finalServeInfoMap::get).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        })));

        return detailResp;

    }

    @Override
    public SimplePageInfo<GetOrderScoringStrategyListResp> list(GetOrderScoringStrategyListRqt rqt){

        SimplePageInfo<OrderScoringStrategy> simplePageInfo = orderScoringStrategyApi.list(rqt);


        SimplePageInfo<GetOrderScoringStrategyListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<OrderScoringStrategy> orderScoringStrategyList = simplePageInfo.getList();

        //类目名称
        List<Long> goodsIds = orderScoringStrategyList.stream().map(OrderScoringStrategy::getCategoryIds).flatMap(it -> Arrays.stream(it.split(","))).distinct().filter(it -> !StringUtils.equals("all", it)).map(Long::parseLong).collect(Collectors.toList());
        List<Goods> goods = goodsCommonService.queryBatch(goodsIds);
        Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));

        List<Long> updateAccountIds = orderScoringStrategyList.stream().map(OrderScoringStrategy::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds))).map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;


        List<GetOrderScoringStrategyListResp> listResps = BeanCopyUtil.copyListProperties(orderScoringStrategyList, GetOrderScoringStrategyListResp.class, (s, t) -> {
            //规则条数
            t.setItemNum(JSON.parseArray(s.getScoringStrategy()).size());

            t.setCategoryNames(Arrays.stream(s.getCategoryIds().split(",")).map(it -> goodsNameMap.getOrDefault(Long.parseLong(it), it)).collect(Collectors.joining(",")));

            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            MasterResourcesEnum masterResourcesEnum = MasterResourcesEnum.asValue(s.getMasterResources());
            t.setMasterResources(masterResourcesEnum != null ? masterResourcesEnum.getDesc() : "-");
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;

    }

    @Override
    public Integer delete(DeleteOrderScoringStrategyRqt rqt){
        return orderScoringStrategyApi.delete(rqt);
    }


    @Override
    public List<OrderScoringItemListResp> scoringItemList(String itemName){
        return orderScoringStrategyApi.scoringItemList(itemName);
    }

}
