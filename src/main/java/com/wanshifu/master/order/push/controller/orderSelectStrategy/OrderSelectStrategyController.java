package com.wanshifu.master.order.push.controller.orderSelectStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.GetOrderSelectStrategyListResp;
import com.wanshifu.master.order.push.domain.response.orderSelectStrategy.OrderSelectStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;
import com.wanshifu.master.order.push.service.orderSelectStrategy.OrderSelectStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderSelectStrategy")
public class OrderSelectStrategyController {

    @Resource
    private OrderSelectStrategyService orderSelectStrategyService;


    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.create(rqt);
    }


    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.update(rqt);
    }


    @PostMapping(value = "detail")
    public OrderSelectStrategyDetailResp detail(@Valid @RequestBody OrderSelectStrategyDetailRqt rqt) {
        return orderSelectStrategyService.detail(rqt);
    }


    @PostMapping(value = "list")
    public SimplePageInfo<GetOrderSelectStrategyListResp> list(@Valid @RequestBody GetOrderSelectStrategyListRqt rqt) {
        return orderSelectStrategyService.list(rqt);
    }


    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.enable(rqt);
    }


    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.delete(rqt);
    }


    @PostMapping(value = "quotaList")
    public SimplePageInfo<MasterQuotaResp> quotaList(@Valid @RequestBody MasterQuotaListRqt rqt) {
        return orderSelectStrategyService.quotaList(rqt);
    }
}
