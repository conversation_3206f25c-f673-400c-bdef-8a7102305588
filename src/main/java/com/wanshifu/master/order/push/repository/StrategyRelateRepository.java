//package com.wanshifu.master.order.push.repository;
//
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.po.StrategyRelate;
//import org.apache.ibatis.session.RowBounds;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//@Repository
//public class StrategyRelateRepository extends BaseRepository<StrategyRelate> {
//
//
//    public int insert(Long relateId, String strategyRelateType, Long baseSelectStrategyId, Long filterStrategyId, Long sortingStrategyId) {
//        StrategyRelate strategyRelate = new StrategyRelate();
//        strategyRelate.setRelateId(relateId);
//        strategyRelate.setRelateType(strategyRelateType);
//        strategyRelate.setBaseSelectStrategyId(baseSelectStrategyId);
//        strategyRelate.setFilterStrategyId(filterStrategyId);
//        strategyRelate.setSortingStrategyId(sortingStrategyId);
//        return this.insertSelective(strategyRelate);
//    }
//
//    public void deleteByRelateId(Long relateId) {
//        Example example = new Example(StrategyRelate.class);
//        example.createCriteria().andEqualTo("relateId", relateId);
//        this.deleteByExample(example);
//    }
//
//    public StrategyRelate selectByBaseSelectStrategyId(Long strategyId) {
//        Example example = new Example(StrategyRelate.class);
//        example.createCriteria().andEqualTo("baseSelectStrategyId", strategyId);
//        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
//    }
//
//    public StrategyRelate selectByFilterStrategyId(Long strategyId) {
//        Example example = new Example(StrategyRelate.class);
//        example.createCriteria().andEqualTo("filterStrategyId", strategyId);
//        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
//    }
//
//    public StrategyRelate selectBySortingStrategyId(Long sortingStrategyId) {
//        Example example = new Example(StrategyRelate.class);
//        example.createCriteria().andEqualTo("sortingStrategyId", sortingStrategyId);
//        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
//    }
//}
