//package com.wanshifu.master.order.push.repository;
//
//import cn.hutool.core.lang.Assert;
//import com.wanshifu.framework.persistence.base.impl.BaseRepository;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.constant.CommonConstant;
//import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
//import com.wanshifu.master.order.push.domain.po.OrderSortingStrategy;
//import org.springframework.stereotype.Repository;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
//@Repository
//public class BackendOrderSortingStrategyRepository extends BaseRepository<OrderSortingStrategy> {
//
//
//    public OrderSortingStrategy selectByStrategyId(Long strategyId) {
//        OrderSortingStrategy orderSortingStrategy = this.selectByPrimaryKey(strategyId);
//        Assert.isTrue(orderSortingStrategy != null && Objects.equals(orderSortingStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
//        return orderSortingStrategy;
//    }
//
//    public int insert(String strategyName, String strategyDesc, String cityIds, String sortingRule, String ruleExpression) {
//        OrderSortingStrategy orderSortingStrategy = new OrderSortingStrategy();
//        orderSortingStrategy.setStrategyName(strategyName);
//        orderSortingStrategy.setStrategyDesc(strategyDesc);
//        orderSortingStrategy.setCityIds(cityIds);
//        orderSortingStrategy.setSortingRule(sortingRule);
//        orderSortingStrategy.setRuleExpression(ruleExpression);
//        return this.insertSelective(orderSortingStrategy);
//    }
//
//    public int update(Long strategyId,String strategyName, String strategyDesc, String cityIds, String sortingRule, String ruleExpression) {
//        OrderSortingStrategy orderSortingStrategy = new OrderSortingStrategy();
//        orderSortingStrategy.setStrategyId(strategyId);
//        orderSortingStrategy.setStrategyName(strategyName);
//        orderSortingStrategy.setStrategyDesc(strategyDesc);
//        orderSortingStrategy.setCityIds(cityIds);
//        orderSortingStrategy.setSortingRule(sortingRule);
//        orderSortingStrategy.setRuleExpression(ruleExpression);
//        return this.updateByPrimaryKeySelective(orderSortingStrategy);
//    }
//
//    public int updateStatus(Long strategyId, Integer strategyStatus) {
//        OrderSortingStrategy orderSortingStrategy = new OrderSortingStrategy();
//        orderSortingStrategy.setStrategyId(strategyId);
//        orderSortingStrategy.setStrategyStatus(strategyStatus);
//        return this.updateByPrimaryKeySelective(orderSortingStrategy);
//    }
//
//    public List<OrderSortingStrategy> selectList(String strategyName, Integer strategyStatus, Date createStartTime, Date createEndTime) {
//        Example example = new Example(BaseSelectStrategy.class);
//        Example.Criteria criteria = example.createCriteria();
//
//        if(StringUtils.isNotBlank(strategyName)){
//            criteria.andEqualTo("strategyName",strategyName);
//        }
//        if(strategyStatus!=null){
//            criteria.andEqualTo("strategyStatus",strategyStatus);
//        }
//        if(createStartTime!=null){
//            criteria.andGreaterThanOrEqualTo("createTime",createStartTime);
//        }
//        if(createEndTime!=null){
//            criteria.andLessThanOrEqualTo("createTime",createEndTime);
//        }
//        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
//        example.orderBy("updateTime").desc();
//        return this.selectByExample(example);
//    }
//
//    public int softDeleteByStrategyId(Long strategyId) {
//        OrderSortingStrategy sortingStrategy = new OrderSortingStrategy();
//        sortingStrategy.setStrategyId(strategyId);
//        sortingStrategy.setIsDelete(CommonConstant.DELETE_STATUS_1);
//        return updateByPrimaryKeySelective(sortingStrategy);
//    }
//}