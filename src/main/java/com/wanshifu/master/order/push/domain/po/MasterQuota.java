package com.wanshifu.master.order.push.domain.po;

import javax.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 师傅指标表
 */
@Data
@ToString
@Table(name = "master_quota")
public class MasterQuota {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "quota_id")
    private Long quotaId;

    /**
     * 指标编码
     */
    @Column(name = "quota_code")
    private String quotaCode;

    /**
     * 指标名称
     */
    @Column(name = "quota_name")
    private String quotaName;

    /**
     * 指标口径描述
     */
    @Column(name = "quota_desc")
    private String quotaDesc;

    /**
     * 值类型：range_value: 度量，enum_value: 枚举
     */
    @Column(name = "value_type")
    private String valueType;

    /**
     * 指标条件表达式
     */
    @Column(name = "feature_expression")
    private String featureExpression;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}