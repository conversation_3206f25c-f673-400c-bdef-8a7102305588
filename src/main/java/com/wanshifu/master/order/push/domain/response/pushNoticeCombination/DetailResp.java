package com.wanshifu.master.order.push.domain.response.pushNoticeCombination;

import com.wanshifu.master.notice.domains.dto.AbTestStrategyRelationDto;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 10:44
 */
@Data
public class DetailResp {

    private Integer strategyId;

    private Integer businessLineId;

    private String strategyName;

    private String strategyDesc;

    private String categoryIds;

    private String cityIds;

    private List<RuleItem> ruleList;

    /**
     * 是否ab实验，1：是，0：否
     */
    private Integer testFlag;

    private Integer testType;

    /**
     * 大数据ab实验编号
     */
    private Integer testId;

    /**
     * 策略关联大数据实验组别信息
     */
    private List<AbTestStrategyRelationDto> strategyRelationList;

    @Data
    public static class RuleItem {

        /**
         * 开启条件
         */
        private DetailResp.OpenCondition openCondition;


        private String noticeStrategyIds;

        /**
         * 触达策略
         */
        private List<com.wanshifu.master.notice.domains.response.pushNoticeCombination.DetailResp.NoticeStrategy> noticeStrategyList;
    }


    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        private String condition;

        /**
         * 规则项
         */
        @NotEmpty
        @Valid
        private List<DetailResp.OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem {

        /**
         * 规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<LinkedList<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;

    }

}