package com.wanshifu.master.order.push.domain.response.repushPolicy;

import com.wanshifu.master.order.push.annotation.TranslateEnum;
import com.wanshifu.master.order.push.domain.enums.OrderFlagEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述 :  重推策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    /**
     * 机制id
     */
    private Long policyId;


    @TranslateEnum(enumClass= OrderFlagEnum.class)
    private String orderFlag;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 重推机制名称
     */
    private String policyName;

    /**
     * 重推机制描述
     */
    private String policyDesc;

    /**
     * 城市id，多个用逗号拼接
     */
    private String cityIds;

    /**
     * 类目id，多个以逗号拼接
     */
    private String categoryIds;

    /**
     * 省份id，多个以逗号拼接，all:表示全国
     */
    private String provinceIds;

    /**
     * 机制状态
     */
    private Integer policyStatus;

    private List<PushStrategyVo> pushStrategy;

    @Data
    public static class PushStrategyVo {

        /**
         * 开启条件
         */
        private OpenCondition openCondition;


        /**
         * 重推机制，new_strategy_combination:按新路由重新推送
         */
        private String repushPolicy;

        /**
         * 是否删除已推送师傅
         */
        private Integer isFilterPushedMaster;

        /**
         * 策略组合
         */
        private StrategyCombination strategyCombination;


        private String pushRuleType;


        /**
         * 推送规则
         */
        private List<PushRule> pushRule;


        private Integer pushRuleId;

        private String pushRuleName;


        @Data
        public static class OpenCondition {
            private String condition;
            private List<OpenConditionItem> itemList;
            private OfferIntervalTime offerIntervalTime;
        }

        @Data
        public static class OfferIntervalTime {
            private Integer interval;
            private String term;
            private Integer itemValue;
        }

        @Data
        public static class StrategyCombination {
            /**
             * 初筛策略
             */
            private Long baseSelectStrategyId;
            private String baseSelectStrategyName;

            /**
             * 召回策略
             */
            private Long filterStrategyId;
            private String filterStrategyName;

            /**
             * 精排策略
             */
            private Long sortingStrategyId;
            private String  sortingStrategyName;
        }

        @Data
        private static class PushRule {
            /**
             * 指派模式 2:报价招标 4:一口价 5:预付款
             */
            private Integer appointType;

            /**
             * 最佳报价数
             */
            private Integer bestOfferNum;

            /**
             * 推送时间间隔
             */
            private Integer delayMinutesBetweenRounds;

            /**
             * 首轮推送人数
             */
            private Integer firstPushMasterNumPerRound;

            /**
             * 非首轮推送人数
             */
            private Integer delayPushMasterNumPerRound;

            /**
             * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
             */
            private String firstPushMasterFlag;

            /**
             * 首轮推送师傅人数占比 (0,100]
             */
            private BigDecimal firstPushMasterPercent;

            /**
             * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
             */
            private String delayPushMasterFlag;

            /**
             * 非首轮推送师傅人数占比 (0,100]
             */
            private BigDecimal delayPushMasterPercent;
        }

        /**
         * 开启条件item
         */
        @Data
        public static class OpenConditionItem{
            /**
             *
             *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源
             */
            private String itemName;

            /**
             * 符号 in:包含  not_in:不包含
             */
            private String term;

            /**
             * 规则项值
             */
            private String itemValue;

            /**
             * [1],[1,2],[1,2,3] 数组长度表示 服务级别
             */
            private List<List<Long>> serveIdList;

            /**
             * ["1:家具安装",“2:家具送货到楼下”]
             */
            private List<String> serveInfoList;

        }
    }
}