package com.wanshifu.master.order.push.service.orderMatchRoute.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.OrderMatchRouteApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.OrderPushFlagEnum;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteDetailResp;
import com.wanshifu.master.order.push.domain.response.orderMatchRoute.OrderMatchRouteListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.orderMatchRoute.OrderMatchRouteService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderMatchRouteServiceImpl implements OrderMatchRouteService {

    @Resource
    private OrderMatchRouteApi orderMatchRouteApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private IopAccountApi iopAccountApi;


    @Override
    public Integer create(CreateOrderMatchRouteRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderMatchRouteApi.create(rqt);
    }


    @Override
    public Integer update(UpdateOrderMatchRouteRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return orderMatchRouteApi.update(rqt);
    }

    @Override
    public OrderMatchRouteDetailResp detail(OrderMatchRouteDetailRqt rqt){
        OrderMatchRoute orderMatchRoute = orderMatchRouteApi.detail(rqt);
        if(orderMatchRoute == null){
            return null;
        }

        OrderMatchRouteDetailResp resp = new OrderMatchRouteDetailResp();
        BeanUtils.copyProperties(orderMatchRoute,resp);

        resp.setOrderStandbyMatchRule(JSON.parseArray(orderMatchRoute.getOrderStandbyMatchRule(),CreateOrderMatchRouteRqt.MatchRule.class));
        return resp;
    }

    @Override
    public SimplePageInfo<OrderMatchRouteListResp> list(OrderMatchRouteListRqt rqt){


        SimplePageInfo<OrderMatchRoute> orderMatchRouteSimplePageInfo = orderMatchRouteApi.list(rqt);

        List<OrderMatchRoute> orderMatchRouteList = orderMatchRouteSimplePageInfo.getList();



        List<Long> updateAccountIds = orderMatchRouteList.stream().map(OrderMatchRoute::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<OrderMatchRouteListResp> listResps = BeanCopyUtil.copyListProperties(orderMatchRouteList, OrderMatchRouteListResp.class, (s, t) -> {
            t.setStandbyMatchRuleNum(JSON.parseArray(s.getOrderStandbyMatchRule(),CreateOrderMatchRouteRqt.MatchRule.class).size());
            t.setOrderPushFlag(OrderPushFlagEnum.asValue(s.getOrderPushFlag()).name);
            t.setOrderPriorityMatchRule(OrderPushFlagEnum.asValue(s.getOrderPriorityMatchRule()).name);
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));
        });


        SimplePageInfo<OrderMatchRouteListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(orderMatchRouteSimplePageInfo.getPages());
        listRespSimplePageInfo.setPageNum(orderMatchRouteSimplePageInfo.getPageNum());
        listRespSimplePageInfo.setTotal(orderMatchRouteSimplePageInfo.getTotal());
        listRespSimplePageInfo.setPageSize(orderMatchRouteSimplePageInfo.getPageSize());
        listRespSimplePageInfo.setList(listResps);
        return listRespSimplePageInfo;   
    }
}
