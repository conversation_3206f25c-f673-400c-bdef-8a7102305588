package com.wanshifu.master.order.push.domain.request.orderpush;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18 11:22
 */
@Data
public class OrderPushRecordDetailRqt {

    @NotNull
    @Min(value = 1L)
    private Long masterOrderId;

    @NotNull
    @Min(value = 1L)
    private Long thirdDivisionId;
}
