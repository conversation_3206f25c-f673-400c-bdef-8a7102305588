package com.wanshifu.master.order.push.service.pushRule.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.PushRuleApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.pushRule.DetailResp;
import com.wanshifu.master.order.push.domain.response.pushRule.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;
//import com.wanshifu.master.order.push.service.common.AuthHandler;
import com.wanshifu.master.order.push.service.pushRule.PushRuleService;
import com.wanshifu.master.order.push.util.BeanCopyUtil;
import com.wanshifu.master.order.push.util.InterprectChineseUtil;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PushRuleServiceImpl implements PushRuleService {

    @Resource
    private PushRuleApi pushRuleApi;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private IopAccountApi iopAccountApi;


    @Override
    public Integer create(CreateRqt rqt){
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushRuleApi.create(rqt);
    }

    @Override
    public Integer update(UpdateRqt rqt){
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return pushRuleApi.update(rqt);
    }


    @Override
    public DetailResp detail(DetailRqt rqt){
        DetailResp detailResp = new DetailResp();
        PushRule pushRule = pushRuleApi.detail(rqt);
        if (pushRule != null) {
            detailResp.setRuleId(pushRule.getRuleId());
            detailResp.setRuleName(pushRule.getRuleName());
            detailResp.setRuleDesc(pushRule.getRuleDesc());
            List<DetailResp.PushRuleEntity> pushRuleList = JSON.parseArray(pushRule.getPushRuleList(), DetailResp.PushRuleEntity.class);

            detailResp.setPushRuleList(pushRuleList);
        }
        return detailResp;    
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt){

        SimplePageInfo<PushRule> simplePageInfo = pushRuleApi.list(rqt);

        SimplePageInfo<ListResp> simplePageInfoResp = new SimplePageInfo<>();
        simplePageInfoResp.setPageNum(simplePageInfo.getPageNum());
        simplePageInfoResp.setPageSize(simplePageInfo.getPageSize());
        simplePageInfoResp.setPages(simplePageInfo.getPages());
        simplePageInfoResp.setTotal(simplePageInfo.getTotal());

        List<PushRule> pushRuleList = simplePageInfo.getList();


        List<Long> updateAccountIds = pushRuleList.stream().map(PushRule::getUpdateAccountId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

        List<ListResp> listResps = BeanCopyUtil.copyListProperties(pushRuleList, ListResp.class, (s, t) -> {
            t.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(s.getUpdateAccountId()))
                    .map(IopAccountResp.IopAccount::getChineseName).orElse(""));
            InterprectChineseUtil.reflexEnum(t);
        });

        simplePageInfoResp.setList(listResps);
        return simplePageInfoResp;
    }

    @Override
    public Integer delete(DeleteRqt rqt){
        return pushRuleApi.delete(rqt);
    }



}
