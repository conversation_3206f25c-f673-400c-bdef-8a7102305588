package com.wanshifu.master.order.push.domain.enums;


import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * @ClassName ServeTypeEnum
 * @Description 订单服务类型
 * <AUTHOR>
 * @Date 2019/4/28 19:56
 * @Version 1.
 **/
public enum ServeTypeEnum {
    /**
     * 服务类型枚举
     */

    UNKNOWN(0, "未知"),

    DELIVERY_DOWNSTAIRS(1, "送货到楼�?),

    DELIVERY_HOME(2, "送货到家"),

    DELIVERY_HOME_INSTALL(3, "送货到家并安�?),

    ONLY_INSTALL(4, "安装"),

    REPAIR(5, "维修"),

    RETURN_GOODS(6, "返货"),

    MAINTAIN(7, "保养"),

    CLEAN(8, "清洗"),

    MEASURE(9, "测量"),

    MEASURE_INSTALL(10, "测量并安�?),

    DISASSEMBLE(11, "拆机"),

    DISASSEMBLE_RETURN(12, "拆机并返�?),

    GONG_CHENG_DAN(15, "工程�?),

    DEBUGGING(16, "调试"),

    MEASURE_INSTALL_NEW(17, "测量并安�?�?"),

    SUIT_INSTALL(18, "套装安装"),
    ;

    private final int code;


    private final String desc;


    /**
     * 测量类型列表
     */
    public static final List<ServeTypeEnum> measureTypeList = Arrays.asList(MEASURE, MEASURE_INSTALL, MEASURE_INSTALL_NEW);
    /**
     * 仅安装类型列�?
     */
    public static final List<ServeTypeEnum> onlyInstallTypeList = Arrays.asList(ServeTypeEnum.ONLY_INSTALL, ServeTypeEnum.SUIT_INSTALL);
    private final static Map<Integer, ServeTypeEnum> enumMap = new HashMap<>();

    static {
        Arrays.asList(ServeTypeEnum.values()).forEach(e -> enumMap.put(e.code, e));
    }

    ServeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public static String getDesc(int code) {
        ServeTypeEnum serveTypeEnum = Stream.of(ServeTypeEnum.values())
                    .filter(value -> value.getCode() == code)
                .findFirst().orElse(null);
        if(serveTypeEnum == null){
            return "";
        }else{
            return serveTypeEnum.desc;
        }
    }


    public static ServeTypeEnum get(Integer code) {
        if (enumMap.containsKey(code)) {
            return enumMap.get(code);
        }

        return ServeTypeEnum.UNKNOWN;
    }

}
