package com.wanshifu.master.order.push.service.common;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.response.common.CategoryListResp;
import com.wanshifu.order.config.api.GoodsServiceApi;
import com.wanshifu.order.config.domains.po.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 描述 :  商品公共服务.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-09 10:29
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class GoodsCommonService {

    private final GoodsServiceApi goodsServiceApi;
    private final ServeCommonService serveCommonService;


    /**
     * 批量获取商品服务
     *
     * @param goodsIds
     * @return
     */
    public List<Goods> queryBatch(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        try {
            return goodsServiceApi.queryBatch(goodsIds);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("queryBatch-批量获取商品服务失败!");
        }
        return Collections.emptyList();
    }

    public List<CategoryListResp> getCategoryByBusinessLineId(Integer businessLineId) {
        return serveCommonService.getLevel1ServeByBusinessLineId(businessLineId).stream()
                .filter(it -> it.getIsDelete() == 0)
                .map(it -> new CategoryListResp(it.getGoodsId(), it.getGoodsName()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(CategoryListResp::getGoodsId))), ArrayList::new));
    }

    public List<CategoryListResp> getCategoryByBusinessLineIds(List<Integer> businessLineIdList) {

        if (CollectionUtils.isEmpty(businessLineIdList)) {
            return Collections.emptyList();
        }
        return businessLineIdList.stream().flatMap(businessLineId -> serveCommonService.getLevel1ServeByBusinessLineId(businessLineId).stream()
                        .filter(it -> it.getIsDelete() == 0)
                        .map(it -> new CategoryListResp(it.getGoodsId(), it.getGoodsName())))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(CategoryListResp::getGoodsId))), ArrayList::new));
    }

}