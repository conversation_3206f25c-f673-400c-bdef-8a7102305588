package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 描述 :  范围初筛.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 16:01
 */
@Data
public class RangeSelectVo {

    /**
     * 范围类型
     * order_master_distance：服务地址与师傅常住地的距离，
     * order_master_serve_division: 服务地址所处师傅服务区域范围(三级地址)
     * order_master_serve_division_level4: 服务地址所处师傅服务区域范围(四级地址)
     * order_master_distance_and_order_master_serve_division: 服务地址与师傅常住地的距离所匹配的师傅服务范围
     * order_master_distance_or_order_master_serve_division: 师傅常住地的距离或者服务地址所处师傅服务区域范围
     * order_master_out_district:服务地址在师傅所在城市(师傅服务区县外)
     */
    @NotEmpty
    @ValueIn("order_master_distance,order_master_serve_division,order_master_serve_division_level4,order_master_distance_and_order_master_serve_division,order_master_distance_or_order_master_serve_division,order_master_out_district")
    private String rangeType;

    /**
     * 范围规则
     */
    private RangeRule rangeRule;

    @Data
    public static class RangeRule{
        /**
         *距离类型，line: 直线距离
         */
        private String distanceType;

        /**
         * 距离值 (公里)
         */
        private Integer distance;
    }
}