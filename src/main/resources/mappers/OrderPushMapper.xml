<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.master.order.push.mapper.OrderPushMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderPush">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="push_id" jdbcType="BIGINT" property="pushId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="appoint_type" jdbcType="TINYINT" property="appointType"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="order_label" jdbcType="VARCHAR" property="orderLabel"/>
        <!--<result column="offer_number" jdbcType="INTEGER" property="offerNumber" />-->
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="offer_time" jdbcType="TIMESTAMP" property="offerTime"/>
        <result column="hire_time" jdbcType="TIMESTAMP" property="hireTime"/>
        <result column="limit_offer" jdbcType="TINYINT" property="limitOffer"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="first_pull_time" jdbcType="TIMESTAMP" property="firstPullTime"/>
        <result column="first_view_time" jdbcType="TIMESTAMP" property="firstViewTime"/>
        <result column="stop_offer_time" jdbcType="TIMESTAMP" property="stopOfferTime"/>
        <result column="is_arrived" jdbcType="TINYINT" property="isArrived"/>
        <result column="is_intention" jdbcType="TINYINT" property="isIntention"/>
        <result column="order_division_id" jdbcType="BIGINT" property="orderDivisionId"/>
        <result column="master_latitude" jdbcType="DECIMAL" property="masterLatitude"/>
        <result column="master_longitude" jdbcType="DECIMAL" property="masterLongitude"/>
        <result column="push_distance" jdbcType="BIGINT" property="pushDistance"/>
        <result column="push_distance_type" jdbcType="TINYINT" property="pushDistanceType"/>
        <result column="push_from" jdbcType="TINYINT" property="pushFrom"/>
        <result column="exclusive_flag" jdbcType="TINYINT" property="exclusiveFlag"/>
        <result column="emergency_order_flag" jdbcType="TINYINT" property="emergencyOrderFlag"/>
        <result column="agent_order_flag" jdbcType="TINYINT" property="agentOrderFlag"/>
        <result column="technique_type_ids" jdbcType="VARCHAR" property="techniqueTypeIds"/>
        <result column="category_id" jdbcType="TINYINT" property="categoryId"/>
        <result column="is_pull_order_distance" jdbcType="TINYINT" property="isPullOrderDistance"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="exposure_score" jdbcType="DECIMAL" property="exposureScore"/>
        <result column="less_contend_flag" jdbcType="TINYINT" property="lessContendFlag"/>
        <result column="menu_category" jdbcType="TINYINT" property="menuCategory"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="according_distance_push_flag" jdbcType="TINYINT" property="accordingDistancePushFlag"/>
        <result column="according_technology_push_flag" jdbcType="TINYINT" property="accordingTechnologyPushFlag"/>
        <result column="less_contend_flag" jdbcType="TINYINT" property="lessContendFlag"/>
        <result column="province_next_id" jdbcType="INTEGER" property="provinceNextId"/>
        <result column="tmpl_city_flag" jdbcType="INTEGER" property="tmplCityFlag"/>
        <result column="tmpl_city_tip_time" jdbcType="TIMESTAMP" property="tmplCityTipTime"/>
        <result column="must_order_flag" jdbcType="TINYINT" property="mustOrderFlag"/>
        <result column="is_pull_view" jdbcType="TINYINT" property="isPullView"/>

    </resultMap>

    <!--逻辑删除订单推送记录-->
    <update id="softDeleteOrderPush">
        update `ltb_order_push`
        set `is_delete` = 1,
            `note`      = CONCAT(`note`, #{appendNote})
        WHERE `order_id` = #{orderId}
          and `is_delete` = 0
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
          limit #{limitCount}
    </update>

    <sql id="orderPushWhere">
        AND is_delete = 0
          AND offer_time IS NULL
          AND (  stop_offer_time IS NULL OR stop_offer_time > NOW())
          AND exclusive_flag != 101
    </sql>



    <select id="selectByMasterIdAndOrderIdsFiler" resultMap="BaseResultMap">
        select
        `order_id`,`master_id`,`appoint_type`,`stop_offer_time`,`is_intention`,`push_distance`,`push_distance_type`,is_pull_order_distance, menu_category
        FROM ltb_order_push
        WHERE master_id = #{masterId}
        and `order_id` IN
        <foreach collection="orderIdIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        and is_delete = 0
        and offer_time is NULL
        and stop_offer_time > #{currentDate}
        and is_intention = 0
        and exclusive_flag != 101
        <if test="tmplCityFlag != null and tmplCityFlag.size() > 0">
            AND tmpl_city_flag in
            <foreach collection="tmplCityFlag" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
    </select>




    <sql id= "waitListFilterSortWhere" >
        WHERE `master_id` = #{masterId} and `is_delete` = 0
        <if test="divisionId != null and divisionId.size() > 0">
            AND order_division_id in
            <foreach collection="divisionId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="appointType != null and appointType.size() > 0">
            and
            <foreach collection="appointType" item="itemValue" open="(" separator="or" close=")">
                <if test="null != itemValue and 6 != itemValue">
                    <if test="2 == itemValue">
                        (appoint_type in (2, 3) AND is_intention != 1)
                    </if>
                    <if test="2 != itemValue">
                        (appoint_type = #{itemValue} AND is_intention != 1)
                    </if>
                </if>
                <if test="null != itemValue and 6 == itemValue">
                    is_intention = 1
                </if>
            </foreach>
        </if>
        <if test="orderAccountLabel != null and orderAccountLabel.size() > 0">
            and
            <foreach collection="orderAccountLabel" item="itemValue" open="(" separator="or" close=")">
                <if test="3 == itemValue">
                    order_from = 'ikea'
                </if>
                <if test="4 == itemValue">
                    order_from = 'applet'
                </if>
                <if test="1 == itemValue">
                    ((order_from = 'site' AND account_type != 'enterprise' ) OR order_from = 'thirdpart' OR is_intention
                    = 1)
                </if>
                <if test="2 == itemValue">
                    account_type = 'enterprise'
                </if>
            </foreach>
        </if>
        AND offer_time is null
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="2 == pushFrom">
            AND ( push_time >= #{restStartTime} and (push_from = #{pushFrom} OR exclusive_flag IN (102,103,104)))
        </if>
        <if test="techniqueTypeId != null and techniqueTypeId.size() > 0">
            and
            <foreach collection="techniqueTypeId" item="itemValue" open="(" separator="or" close=")">
                FIND_IN_SET(#{itemValue},technique_type_ids)
            </foreach>
        </if>
        <if test="categoryId != null and categoryId.size() > 0">
            AND category_id in
            <foreach collection="categoryId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="isArrived != null and isArrived.size() > 0">
            AND is_arrived in
            <foreach collection="isArrived" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != pushFlag">
            <choose>
                <when test="pushFlag == 0">
                    AND (push_flag = 0 or according_technology_push_flag = 1)
                </when>
                <otherwise>
                    AND push_flag = #{pushFlag}
                </otherwise>
            </choose>
        </if>
        <if test="null != menuCategory">
            AND menu_category = #{menuCategory}
        </if>
        <if test="null != lessContendFlag">
            AND less_contend_flag = #{lessContendFlag}
        </if>
        <if test="null != mustOrderFlag">
            AND must_order_flag = #{mustOrderFlag}
        </if>

        AND exclusive_flag != 101
        AND tmpl_city_flag != 3

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(1)">
            AND is_pull_view = 0
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(2)">
            AND must_order_flag = 1
        </if>

        <if test="null != provinceNextIdList and provinceNextIdList.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextIdList" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

    </sql>

    <select id="selectIocWaitOfferFilter" resultMap="BaseResultMap">
        SELECT order_id, appoint_type
        FROM ltb_order_push
        WHERE master_id = #{masterId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDate}
          and is_intention = 0
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by stop_offer_time DESC LIMIT #{queryNumber};
    </select>

    <select id="selectWaitOfferMasterIdsByOrderId" resultType="java.lang.Long">
        select master_id from ltb_order_push
        where order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <include refid="orderPushWhere"></include>
    </select>

    <select id="selectInviteMasterByOrderId" resultType="java.lang.Long">
        select master_id
        FROM ltb_order_push
        WHERE order_id = #{orderId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDateTime}
          and is_intention = 0
          and exclusive_flag != 101
        and first_view_time is NULL
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by push_time limit 10
    </select>

    <select id="selectMasterCategorySelector" resultType="java.lang.Long">
        SELECT DISTINCT category_id
        FROM ltb_order_push
        WHERE master_id = #{masterId}
          AND `is_delete` = 0
          AND offer_time IS NULL
          AND (stop_offer_time IS NULL OR stop_offer_time > #{currentDateTime})
          AND exclusive_flag != 101
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
    </select>


    <select id="selectOrderPushForNotice" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
          and is_intention = 0
          and exclusive_flag != 101
          and push_flag = 0
          and according_technology_push_flag = 1
    </select>

    <select id="selectOrderPushByOrderId" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE order_id = #{orderId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDateTime}
          and is_intention = 0
          and exclusive_flag != 101
          and push_flag = 0
        and first_view_time is NULL
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by push_time limit 1000
    </select>


    <select id="listByMasterDivisionIdForAppletUnLogin" resultMap="BaseResultMap">
        select distinct order_id, appoint_type, stop_offer_time
        FROM ltb_order_push
        <where>
            <if test="masterDivisionId != null">
                AND order_division_id = #{masterDivisionId}
            </if>
            <if test="stopOfferTime != null">
                AND stop_offer_time > #{stopOfferTime}
            </if>
            <if test="pushTime != null">
                AND push_time > #{pushTime}
            </if>
            <if test="null != provinceNextId and provinceNextId.size() > 0">
                and `province_next_id` in
                <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                    #{itemValue}
                </foreach>
            </if>
            and is_delete = 0
            and is_intention = 0
        </where>
        order by push_id desc limit 20
    </select>

    <delete id="deleteByOrderIdAndLimit">
        delete from ltb_order_push
        where order_id = #{orderId}
          and is_delete = #{isDelete}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </delete>

    <update id="updateMenuCategoryByOrderIdAndLimit">
        update `ltb_order_push` set `menu_category` = #{menuCategory}
        WHERE order_id = #{orderId}
        and is_delete = 0
        and `menu_category` = 0
        and push_flag = 0
        and offer_time is NULL
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <update id="updateMenuCategoryByOrderIdAndLimitV2">
        update `ltb_order_push` set `menu_category` = #{menuCategory}
        WHERE order_id = #{orderId}
        and is_delete = 0
        and `menu_category` = 2
        and offer_time is NULL
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <delete id="deleteByOrderId">
        delete from ltb_order_push
        where order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
            limit #{limit}
    </delete>

    <delete id="deleteExpiredOrderPushByPushIds">
        delete from ${tableName}
        where push_id in
        <foreach collection="pushIds" item="itemValue" open="(" separator="," close=")">
            #{itemValue}
        </foreach>
    </delete>


    <select id="selectMasterOrderPushByAccount" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE is_delete = 0
        and offer_time is NULL
        and stop_offer_time > #{currentDateTime}
        and is_intention = 0
        and exclusive_flag != 101
        and tmpl_city_flag = 0
        and from_account = #{accountId}
        and account_type = #{accountType}
        and master_id = #{masterId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit 300
    </select>

</mapper>