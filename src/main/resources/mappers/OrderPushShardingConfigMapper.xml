<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.master.order.push.mapper.OrderPushShardingConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderPushShardingConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="suffix_table_name" jdbcType="VARCHAR" property="suffixTableName"/>
        <result column="city_division_id" jdbcType="CHAR" property="cityDivisionId"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectAllConfigs" resultMap = "BaseResultMap">
        select * from order_push_sharding_config
    </select>

</mapper>