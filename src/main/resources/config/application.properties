#spring.application.name=master-order-push-backend-service
##############
##\u65E5\u5FD7\u914D\u7F6E
##############
#logging.config=classpath:logback-spring.xml
######################################
##profile\u914D\u7F6E\u5BF9\u5E94\u73AF\u5883\u7684properties\u6587\u4EF6
######################################
#spring.profiles.active=@deploy.env@
##spring.profiles.active=dev
#
#########################################################
##Mybatis\u914D\u7F6E#
#########################################################
#mybatis.mapper-locations=classpath*:**/mappers/*.xml
#mybatis.type-aliases-package=com.wanshifu.master.order.push.domain.po
#################\u901A\u7528mapper\u914D\u7F6E##########################
##mappers \u591A\u4E2A\u63A5\u53E3\u65F6\u9017\u53F7\u9694\u5F00
#mapper.mappers=com.wanshifu.framework.persistence.base.IBaseCommMapper
#mapper.not-empty=false
#mapper.identity=MYSQL
##########################################################
##pagehelper\u5206\u9875\u914D\u7F6E#
##########################################################
#pagehelper.helperDialect=mysql
#pagehelper.reasonable=true
#pagehelper.supportMethodsArguments=true
#pagehelper.params=count=countSql
#
######################################
##          \u63A5\u53E3\u914D\u7F6E
######################################
##\u6253\u5F00actuator\u76D1\u63A7
#management.security.enabled=false
###########################################################
##log\u8DEF\u5F84
###########################################################
#logging.path=/data/servicesLog/tomcat--order-push-backend-service/logs
#javamelody.advisor-auto-proxy-creator-enabled=false