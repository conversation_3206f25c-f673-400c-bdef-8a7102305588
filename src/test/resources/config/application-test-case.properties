javamelody.enabled=false
wanshifu.swagger.enable=false
wanshifu.task.pool.enable=false

#############JPA\u914D\u7F6E#############
spring.jpa.database=h2
#spring.jpa.properties.hibernate.hbm2ddl.auto\u662Fhibernate\u7684\u914D\u7F6E\u5C5E\u6027\uFF0C\u5176\u4E3B\u8981\u4F5C\u7528\u662F\uFF1A\u81EA\u52A8\u521B\u5EFA\u3001\u66F4\u65B0\u3001\u9A8C\u8BC1\u6570\u636E\u5E93\u8868\u7ED3\u6784\u3002
spring.jpa.properties.hibernate.hbm2ddl.auto=create
spring.jpa.show-sql=true
spring.jpa.generate-ddl=true
spring.data.jpa.repositories.enabled=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
############H2\u914D\u7F6E############
wanshifu.dataSource.enable=false
spring.datasource.type=org.apache.tomcat.jdbc.pool.DataSource
spring.dataSource.username=sa
spring.dataSource.password=
spring.dataSource.driver-class-name=org.h2.Driver
spring.h2.console.path=/console
spring.h2.console.enabled=true
spring.h2.console.settings.trace=true
spring.datasource.platform=h2
spring.dataSource.url=jdbc:h2:mem:h2tests;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=TRUE
#####################


