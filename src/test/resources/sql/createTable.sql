DROP TABLE IF EXISTS base_feature; 

CREATE TABLE `base_feature` (
  `feature_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '注解id',
  `feature_code` varchar(255) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_type` varchar(255) NOT NULL DEFAULT '' COMMENT '特征类型',
  `feature_for` varchar(255) NOT NULL DEFAULT '' COMMENT '特征所属对象：master: 师傅，order: 订单',
  `feature_name` varchar(255) NOT NULL DEFAULT '' COMMENT '特征名称',
  `feature_field` varchar(255) NOT NULL DEFAULT '',
  `feature_dimension` varchar(255) NOT NULL DEFAULT '' COMMENT '特征维度',
  `default_value` varchar(255) NOT NULL DEFAULT '' COMMENT '默认值',
  `field_type` varchar(255) NOT NULL DEFAULT '' COMMENT '字段类型',
  `instance_name` varchar(255) NOT NULL DEFAULT '' COMMENT 'tablestore实例名称',
  `table_name` varchar(255) NOT NULL DEFAULT '' COMMENT 'tablestore表名',
  `multi_value_dimension` varchar(255) DEFAULT '' COMMENT '订单特征维度多值字段',
  `calculate_type` varchar(255) DEFAULT '' COMMENT '特征计算类型,sum: 求和，min: 最小值，max: 最大值',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`feature_id`)
) ;


DROP TABLE IF EXISTS base_select_strategy; 

CREATE TABLE `base_select_strategy` (
  `strategy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略id',
  `snapshot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `open_condition` varchar(2000) NOT NULL COMMENT '开启条件',
  `range_select` varchar(2000) NOT NULL COMMENT '范围初筛',
  `technique_select` varchar(2000) NOT NULL COMMENT '技能初筛',
  `status_select` varchar(3000) NOT NULL COMMENT '状态初筛',
  `strategy_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '策略状态,1: 启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：已删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`strategy_id`)
) COMMENT='初筛策略表';


DROP TABLE IF EXISTS base_select_strategy_snapshot; 

CREATE TABLE `base_select_strategy_snapshot` (
  `snapshot_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `open_condition` varchar(2000) NOT NULL COMMENT '开启条件',
  `range_select` varchar(2000) NOT NULL COMMENT '范围初筛',
  `technique_select` varchar(2000) NOT NULL COMMENT '技能初筛',
  `status_select` varchar(3000) NOT NULL COMMENT '状态初筛',
  `strategy_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '策略状态,1: 启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：已删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`snapshot_id`)
) COMMENT='初筛策略快照表';


DROP TABLE IF EXISTS complex_feature; 

CREATE TABLE `complex_feature` (
  `feature_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '特征id',
  `feature_code` varchar(255) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_name` varchar(255) NOT NULL DEFAULT '' COMMENT '特征名称',
  `feature_for` varchar(255) NOT NULL DEFAULT '' COMMENT '特征所属对象：master: 师傅，order: 订单',
  `calculate_expression` varchar(5000) NOT NULL DEFAULT '' COMMENT '特征计算表达式',
  `feature_dependency` varchar(255) NOT NULL DEFAULT '' COMMENT '依赖的特征集合',
  `dependency_check` varchar(255) NOT NULL,
  `field_type` varchar(255) NOT NULL DEFAULT '' COMMENT '字段类型',
  `default_value` varchar(255) NOT NULL DEFAULT '' COMMENT '默认值',
  `is_delete` tinyint(3) NOT NULL COMMENT '是否删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`feature_id`)
) ;


DROP TABLE IF EXISTS filter_strategy; 

CREATE TABLE `filter_strategy` (
  `strategy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略id',
  `snapshot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `filter_rule` text NOT NULL COMMENT '召回规则配置(JSON格式)',
  `rule_expression` text NOT NULL COMMENT '召回规则表达式',
  `strategy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '策略状态，1：启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`strategy_id`)
) COMMENT='召回策略表';


DROP TABLE IF EXISTS filter_strategy_snapshot; 

CREATE TABLE `filter_strategy_snapshot` (
  `snapshot_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `filter_rule` text NOT NULL COMMENT '召回规则配置(JSON格式)',
  `rule_expression` text NOT NULL COMMENT '召回规则表达式',
  `strategy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '策略状态，1：启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`snapshot_id`)
) COMMENT='召回策略快照表';


DROP TABLE IF EXISTS handle_progress; 

CREATE TABLE `handle_progress` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `to_be_detected_master_num` int(10) unsigned NOT NULL COMMENT '待检测师傅数',
  `direct_push_num` int(20) NOT NULL COMMENT '强推师傅数',
  `filtered_master_num` int(10) NOT NULL COMMENT '过滤师傅数',
  `filter_message` mediumtext NOT NULL COMMENT '过滤详情',
  `score_message` mediumtext NOT NULL COMMENT '评分详情',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  PRIMARY KEY (`id`) 
) COMMENT='推单处理记录表';


DROP TABLE IF EXISTS master_feature; 

CREATE TABLE `master_feature` (
  `feature_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '注解id',
  `feature_code` varchar(255) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_type` varchar(255) NOT NULL DEFAULT '' COMMENT '特征类型',
  `feature_field` varchar(255) NOT NULL DEFAULT '' COMMENT '特征名称',
  `feature_dimension` varchar(255) NOT NULL DEFAULT '' COMMENT '特征维度',
  `feature_name` varchar(500) NOT NULL DEFAULT '',
  `default_value` varchar(255) NOT NULL DEFAULT '' COMMENT '默认值',
  `field_type` varchar(255) NOT NULL DEFAULT '' COMMENT '字段类型',
  `table_name` varchar(255) NOT NULL DEFAULT '' COMMENT 'tablestore表名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`feature_id`)
) ;


DROP TABLE IF EXISTS master_push; 

CREATE TABLE `master_push` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230216; 

CREATE TABLE `master_push_20230216` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230217; 

CREATE TABLE `master_push_20230217` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230218; 

CREATE TABLE `master_push_20230218` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230219; 

CREATE TABLE `master_push_20230219` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230220; 

CREATE TABLE `master_push_20230220` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230221; 

CREATE TABLE `master_push_20230221` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230222; 

CREATE TABLE `master_push_20230222` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230223; 

CREATE TABLE `master_push_20230223` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230224; 

CREATE TABLE `master_push_20230224` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230225; 

CREATE TABLE `master_push_20230225` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230228; 

CREATE TABLE `master_push_20230228` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230302; 

CREATE TABLE `master_push_20230302` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230303; 

CREATE TABLE `master_push_20230303` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230310; 

CREATE TABLE `master_push_20230310` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230313; 

CREATE TABLE `master_push_20230313` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230314; 

CREATE TABLE `master_push_20230314` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230315; 

CREATE TABLE `master_push_20230315` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230316; 

CREATE TABLE `master_push_20230316` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230317; 

CREATE TABLE `master_push_20230317` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230318; 

CREATE TABLE `master_push_20230318` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230319; 

CREATE TABLE `master_push_20230319` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_push_20230320; 

CREATE TABLE `master_push_20230320` (
  `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本',
  `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
  `list_offset` bigint(20) NOT NULL COMMENT '推送进度位点',
  `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
  `score` decimal(10,2) NOT NULL COMMENT '分数',
  `flag` varchar(20) NOT NULL DEFAULT 'old' COMMENT 'flag',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_id`),
  UNIQUE KEY (`order_id`,`order_version`,`master_type`,`list_offset`,`flag`)  
) ;


DROP TABLE IF EXISTS master_quota; 

CREATE TABLE `master_quota` (
  `quota_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `quota_code` varchar(100) NOT NULL COMMENT '指标编码',
  `quota_name` varchar(255) NOT NULL COMMENT '指标名称',
  `quota_desc` varchar(255) NOT NULL COMMENT '指标口径描述',
  `value_type` varchar(20) NOT NULL COMMENT '值类型：range_value: 度量，enum_value: 枚举',
  `feature_expression` varchar(500) DEFAULT '' COMMENT '指标条件表达式',
  `is_delete` tinyint(3) NOT NULL COMMENT '是否删除，1：已删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`quota_id`)
) COMMENT='师傅指标表';


DROP TABLE IF EXISTS master_quota_value; 

CREATE TABLE `master_quota_value` (
  `value_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '指标枚举值id',
  `master_quota_id` bigint(20) unsigned NOT NULL COMMENT '指标id',
  `code` varchar(100) NOT NULL DEFAULT '' COMMENT '指标枚举值编码',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '指标枚举值名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`value_id`)
) COMMENT='师傅指标枚举值表';


DROP TABLE IF EXISTS order_feature; 

CREATE TABLE `order_feature` (
  `feature_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '注解id',
  `feature_code` varchar(255) NOT NULL DEFAULT '' COMMENT '特征编码',
  `feature_type` varchar(255) NOT NULL DEFAULT '' COMMENT '特征类型',
  `feature_field` varchar(255) NOT NULL DEFAULT '' COMMENT '特征名称',
  `feature_name` varchar(500) NOT NULL DEFAULT '',
  `feature_dimension` varchar(255) NOT NULL DEFAULT '' COMMENT '特征维度',
  `default_value` varchar(255) NOT NULL DEFAULT '' COMMENT '默认值',
  `field_type` varchar(255) NOT NULL DEFAULT '' COMMENT '字段类型',
  `table_name` varchar(255) NOT NULL DEFAULT '' COMMENT 'tablestore表名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`feature_id`)
) ;


DROP TABLE IF EXISTS order_push_abnormal_result; 

CREATE TABLE `order_push_abnormal_result` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `global_order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '全局订单id',
  `msg_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'mq消息id',
  `topic` varchar(255) NOT NULL DEFAULT '' COMMENT 'mq消息topic',
  `tag` varchar(255) NOT NULL DEFAULT '' COMMENT 'mq消息tag',
  `msg_body` text NOT NULL COMMENT 'mq消息内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ;


DROP TABLE IF EXISTS order_sorting_strategy; 

CREATE TABLE `order_sorting_strategy` (
  `strategy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `city_ids` varchar(5000) NOT NULL DEFAULT '' COMMENT '城市id，多个以逗号拼接',
  `sorting_rule` text NOT NULL COMMENT '排序规则配置(JSON格式)',
  `rule_expression` text NOT NULL COMMENT '排序规则表达式',
  `strategy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '策略状态，1：启用，0：禁用',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`strategy_id`)
) COMMENT='订单排序策略表';


DROP TABLE IF EXISTS push_config; 

CREATE TABLE `push_config` (
  `push_config_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `push_config_expression` varchar(500) NOT NULL DEFAULT '' COMMENT '配置触发条件aviator',
  `best_offer_number` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最佳报价数',
  `delay_minutes_between_rounds` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '每轮推送间隔',
  `first_old_master_push_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '首轮老师傅推送数',
  `first_new_master_push_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '首轮新师傅推送数',
  `delay_push_old_master_num_per_round` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '延迟老师傅推送数',
  `delay_push_new_master_num_per_round` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '延迟新师傅推送数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`push_config_id`)
) COMMENT='推送控制表';


DROP TABLE IF EXISTS push_handle; 

CREATE TABLE `push_handle` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_version` varchar(100) NOT NULL DEFAULT '' COMMENT '订单版本号',
  `base_select_master_num` int(11) NOT NULL DEFAULT '0' COMMENT '初筛师傅数',
  `direct_push_num` int(11) NOT NULL DEFAULT '0' COMMENT '强推师傅数',
  `filtered_master_num` int(11) NOT NULL DEFAULT '0' COMMENT '过滤师傅数',
  `filter_message` mediumtext NOT NULL COMMENT '过滤详情',
  `score_message` mediumtext NOT NULL COMMENT '评分详情',
  `push_strategy` varchar(100) DEFAULT '' COMMENT '推送策略',
  `flag` varchar(20) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ;


DROP TABLE IF EXISTS push_progress; 

CREATE TABLE `push_progress` (
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `new_master_offset` int(10) NOT NULL COMMENT '新师傅推送位点',
  `old_master_offset` int(10) NOT NULL COMMENT '老师傅推送位点',
  `base_select_master_num` int(10) NOT NULL DEFAULT '0' COMMENT '初筛师傅数',
  `list_length` int(10) NOT NULL COMMENT '师傅列表总长度',
  `filtered_master_num` int(10) NOT NULL COMMENT '状态异常师傅数量',
  `pushed_master_num` int(10) NOT NULL COMMENT '已推师傅数量',
  `pushed_round` int(10) NOT NULL DEFAULT '99' COMMENT '已推轮数',
  `current_stop_reason` varchar(100) DEFAULT '' COMMENT '当前订单停止推送的原因',
  `push_status` varchar(10) NOT NULL DEFAULT 'UNKNOW' COMMENT '订单当前推送状态',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本号',
  `direct_push_num` int(10) NOT NULL COMMENT '强推师傅数',
  `first_push_time` datetime NOT NULL COMMENT '首次推送时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `flag` varchar(20) NOT NULL DEFAULT 'old',
  PRIMARY KEY (`order_id`,`order_version`,`flag`)
) COMMENT='推送进度表';


DROP TABLE IF EXISTS push_progress_202302; 

CREATE TABLE `push_progress_202302` (
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `new_master_offset` int(10) NOT NULL COMMENT '新师傅推送位点',
  `old_master_offset` int(10) NOT NULL COMMENT '老师傅推送位点',
  `base_select_master_num` int(10) NOT NULL DEFAULT '0' COMMENT '初筛师傅数',
  `list_length` int(10) NOT NULL COMMENT '师傅列表总长度',
  `filtered_master_num` int(10) NOT NULL COMMENT '状态异常师傅数量',
  `pushed_master_num` int(10) NOT NULL COMMENT '已推师傅数量',
  `pushed_round` int(10) NOT NULL DEFAULT '99' COMMENT '已推轮数',
  `current_stop_reason` varchar(100) DEFAULT '' COMMENT '当前订单停止推送的原因',
  `push_status` varchar(10) NOT NULL DEFAULT 'UNKNOW' COMMENT '订单当前推送状态',
  `order_version` varchar(20) NOT NULL COMMENT '订单版本号',
  `direct_push_num` int(10) NOT NULL COMMENT '强推师傅数',
  `first_push_time` datetime NOT NULL COMMENT '首次推送时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `flag` varchar(20) NOT NULL DEFAULT 'old',
  PRIMARY KEY (`order_id`,`order_version`,`flag`)
) COMMENT='推送进度表';


DROP TABLE IF EXISTS repush_policy; 

CREATE TABLE `repush_policy` (
  `policy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '重推机制id',
  `policy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '重推机制名称',
  `policy_desc` varchar(500) DEFAULT NULL COMMENT '重推机制描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `city_ids` varchar(750) NOT NULL DEFAULT '' COMMENT '城市id，多个以逗号拼接',
  `strategy_combination` text NOT NULL COMMENT '重推策略配置(JSON格式)',
  `policy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态，1：启用，0：禁用',
  `business_line_id` tinyint(3) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`policy_id`),
  FULLTEXT KEY (`city_ids`) 
) COMMENT='重推机制表';


DROP TABLE IF EXISTS repush_policy_snapshot; 

CREATE TABLE `repush_policy_snapshot` (
  `snapshot_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '快照id',
  `policy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '重推机制名称',
  `policy_desc` varchar(500) DEFAULT NULL COMMENT '重推机制描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `city_ids` varchar(750) NOT NULL DEFAULT '' COMMENT '城市id，多个以逗号拼接',
  `strategy_combination` text NOT NULL COMMENT '重推策略配置(JSON格式)',
  `policy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态，1：启用，0：禁用',
  `business_line_id` tinyint(3) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`snapshot_id`)
) COMMENT='重推机制快照表';


DROP TABLE IF EXISTS score_item; 

CREATE TABLE `score_item` (
  `item_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '注解id',
  `item_code` varchar(100) NOT NULL DEFAULT '' COMMENT '匹配项编码',
  `item_name` varchar(255) NOT NULL DEFAULT '' COMMENT '匹配项名称',
  `value_type` varchar(20) NOT NULL DEFAULT '' COMMENT '赋值类型',
  `feature_expression` varchar(500) DEFAULT '' COMMENT '匹配项条件表达式',
  `is_delete` tinyint(3) NOT NULL COMMENT '是否删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`item_id`)
) ;


DROP TABLE IF EXISTS score_item_value; 

CREATE TABLE `score_item_value` (
  `value_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '匹配项枚举值id',
  `score_item_id` bigint(20) unsigned NOT NULL COMMENT '匹配项id',
  `code` varchar(100) NOT NULL DEFAULT '' COMMENT '匹配项枚举值编码',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '匹配项枚举值名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`value_id`)
) ;


DROP TABLE IF EXISTS simulate_push; 

CREATE TABLE `simulate_push` (
  `simulate_push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '测算推id',
  `simulate_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '测试id',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
  `strategy_list_name` varchar(1000) NOT NULL DEFAULT '' COMMENT '策略名称集合',
  `is_alternate_strategy` int(11) NOT NULL DEFAULT '0' COMMENT '是否启用备用路由',
  `push_master_num` int(11) NOT NULL DEFAULT '0' COMMENT '推送师傅数',
  `push_rounds` int(11) NOT NULL DEFAULT '0' COMMENT '推送轮数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`simulate_push_id`)
) ;


DROP TABLE IF EXISTS simulate_push_detail; 

CREATE TABLE `simulate_push_detail` (
  `detail_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '测算推单明细id',
  `simulate_push_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '测算推单id',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
  `master_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '师傅id',
  `push_time` varchar(1000) NOT NULL DEFAULT '' COMMENT '推送时间',
  `push_round` int(11) NOT NULL DEFAULT '0' COMMENT '推送轮数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`detail_id`)
) ;


DROP TABLE IF EXISTS sorting_strategy; 

CREATE TABLE `sorting_strategy` (
  `strategy_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略id',
  `snapshot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `sorting_rule` text NOT NULL COMMENT '评分规则配置(JSON格式)',
  `rule_expression` text NOT NULL COMMENT '评分规则表达式',
  `strategy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '策略状态，1：启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`strategy_id`)
) COMMENT='精排策略表';


DROP TABLE IF EXISTS sorting_strategy_snapshot; 

CREATE TABLE `sorting_strategy_snapshot` (
  `snapshot_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '快照id',
  `strategy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `sorting_rule` text NOT NULL COMMENT '评分规则配置(JSON格式)',
  `rule_expression` text NOT NULL COMMENT '评分规则表达式',
  `strategy_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '策略状态，1：启用，0：禁用',
  `business_line_id` int(11) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`snapshot_id`)
) COMMENT='精排策略快照表';


DROP TABLE IF EXISTS strategy_combination; 

CREATE TABLE `strategy_combination` (
  `combination_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '组合id',
  `snapshot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '快照id',
  `combination_name` varchar(255) NOT NULL DEFAULT '' COMMENT '组合名称',
  `combination_desc` varchar(500) DEFAULT NULL COMMENT '组合描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `city_ids` varchar(750) NOT NULL DEFAULT '' COMMENT '城市id，多个以逗号拼接',
  `priority_strategy_combination` text NOT NULL COMMENT '优先推荐路由(JSON格式)',
  `alternate_strategy_combination` text NOT NULL COMMENT '备用推荐路由(JSON格式)',
  `combination_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '组合状态，1：启用，0：禁用',
  `business_line_id` tinyint(3) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`combination_id`),
  FULLTEXT KEY (`city_ids`) 
) COMMENT='策略组合表';


DROP TABLE IF EXISTS strategy_combination_simulate; 

CREATE TABLE `strategy_combination_simulate` (
  `simulate_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '测算id',
  `strategy_combination_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '策略组合id',
  `simulated_order_num` int(11) NOT NULL DEFAULT '0' COMMENT '已测算订单数量',
  `order_num` int(11) NOT NULL DEFAULT '0' COMMENT '订单数量',
  `simulate_status` int(11) NOT NULL DEFAULT '0' COMMENT '测算状态',
  `simulate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '测算时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`simulate_id`)
) ;


DROP TABLE IF EXISTS strategy_combination_snapshot; 

CREATE TABLE `strategy_combination_snapshot` (
  `snapshot_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '快照id',
  `combination_name` varchar(255) NOT NULL DEFAULT '' COMMENT '组合名称',
  `combination_desc` varchar(500) DEFAULT NULL COMMENT '组合描述',
  `category_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '类目id，多个以逗号拼接',
  `city_ids` varchar(750) NOT NULL DEFAULT '' COMMENT '城市id，多个以逗号拼接',
  `priority_strategy_combination` text NOT NULL COMMENT '优先推荐路由(JSON格式)',
  `alternate_strategy_combination` text NOT NULL COMMENT '备用推荐路由(JSON格式)',
  `combination_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '组合状态，1：启用，0：禁用',
  `business_line_id` tinyint(3) NOT NULL DEFAULT '1' COMMENT '业务线id',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，1：删除，0：未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新人id',
  PRIMARY KEY (`snapshot_id`)
) COMMENT='策略组合快照表';


DROP TABLE IF EXISTS strategy_relate; 

CREATE TABLE `strategy_relate` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `relate_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联策略id',
  `relate_type` varchar(35) NOT NULL DEFAULT '' COMMENT '关联类型[repush_policy:重推机制,strategy_combination_priority:策略组合-优先推荐路由,strategy_combination_alternate:策略组合-备用推荐路由]',
  `base_select_strategy_Id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '初筛策略id',
  `filter_strategy_Id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '召回策略id',
  `sorting_strategy_Id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '精排策略id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY (`relate_id`,`relate_type`)    
) COMMENT='策略关联表';


